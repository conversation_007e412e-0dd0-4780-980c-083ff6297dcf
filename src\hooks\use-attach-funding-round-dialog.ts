import { useState, useEffect } from "react";
import { dialogManager } from "@/lib/dialog-manager";

/**
 * Hook to manage the attach to funding round dialog state
 */
export function useAttachFundingRoundDialog() {
  const [state, setState] = useState(dialogManager.getState());

  useEffect(() => {
    const unsubscribe = dialogManager.subscribe(setState);
    return unsubscribe;
  }, []);

  return {
    isOpen: state.isOpen,
    projectData: state.projectData,
    openDialog: dialogManager.openAttachFundingRoundDialog.bind(dialogManager),
    closeDialog:
      dialogManager.closeAttachFundingRoundDialog.bind(dialogManager),
  };
}
