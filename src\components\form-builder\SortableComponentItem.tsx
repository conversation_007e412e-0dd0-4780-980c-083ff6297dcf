import { memo, useMemo, useRef, useEffect } from "react";
import { FormComponent } from "@/lib/types/form";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Trash2,
  Settings,
  // GripVertical,
  CircleArrowUp,
  CircleArrowDown,
  Layers,
  FolderOpen,
  ChevronDown,
  ChevronRight,
} from "lucide-react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import {
  getComponentBgColorClass,
  getComponentTypeName,
  isContainerComponent,
} from "@/lib/utils/component-utils";

interface SortableComponentItemProps {
  readonly component: FormComponent;
  readonly id: string;
  readonly isSelected: boolean;
  readonly allComponents: FormComponent[];
  readonly onSelect: (id: string, activateEditTab?: boolean) => void;
  readonly onDelete: (id: string) => void;
  readonly onMove: (id: string, direction: "up" | "down") => void;
  readonly isExpanded?: boolean;
  readonly onToggleExpand?: (id: string) => void;
  readonly level?: number; // For indentation of nested components
  readonly expandedContainers?: Record<string, boolean>; // Add expandedContainers to props
}

function SortableComponentItem({
  component,
  id,
  isSelected,
  allComponents,
  onSelect,
  onDelete,
  onMove,
  isExpanded = true,
  onToggleExpand,
  level = 0,
  expandedContainers = {},
}: SortableComponentItemProps) {
  // Create a ref for the component card
  const componentRef = useRef<HTMLDivElement>(null);

  // Scroll into view when selected
  useEffect(() => {
    if (isSelected && componentRef.current) {
      // Use a small timeout to ensure the UI has updated
      setTimeout(() => {
        componentRef.current?.scrollIntoView({
          behavior: "smooth",
          block: "center",
        });
      }, 100);
    }
  }, [isSelected]);

  const {
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 1 : 0,
  };

  // Get the appropriate icon based on component type
  const getComponentIcon = () => {
    switch (component.type) {
      case "step":
        return <Layers className="h-5 w-5" />;
      case "section":
        return <FolderOpen className="h-5 w-5" />;
      default:
        return null;
    }
  };

  // Determine if this is a container component (step or section)
  const isContainer = isContainerComponent(component.type);

  // Get background color class based on component type
  const bgColorClass = getComponentBgColorClass(component.type);

  // Find child components
  const childComponents = useMemo(() => {
    if (!isContainer) return [];
    return allComponents.filter((c) => c.parentId === component.id);
  }, [allComponents, component.id, isContainer]);

  // Toggle expand/collapse - memoized to prevent recreating on every render
  const handleToggleExpand = useMemo(() => {
    return (e: React.MouseEvent) => {
      e.stopPropagation();
      if (onToggleExpand) {
        onToggleExpand(component.id);
      }
    };
  }, [onToggleExpand, component.id]);

  // Calculate left padding based on nesting level
  const levelPadding = level * 16; // 16px per level

  return (
    <div>
      <Card
        ref={(node) => {
          // Combine the DnD ref with our component ref
          setNodeRef(node);
          if (node && isSelected) {
            componentRef.current = node;
          }
        }}
        style={{
          ...style,
          marginLeft: `${levelPadding}px`,
        }}
        className={`cursor-pointer transition-colors ${isSelected ? "border-primary" : "hover:border-muted-foreground/50"
          } ${isDragging ? "shadow-lg" : ""} ${bgColorClass}`}
        onClick={() => onSelect(component.id)}
      >
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {/* <Button
                variant="ghost"
                size="icon"
                className="cursor-grab touch-none"
                {...attributes}
                {...listeners}
              >
                <GripVertical className="h-4 w-4 text-muted-foreground" />
              </Button> */}
              <div className="flex items-center gap-2">
                {isContainer && (
                  <div className="flex h-8 w-8 items-center justify-center rounded-md bg-muted">
                    {getComponentIcon()}
                  </div>
                )}
                <div>
                  <h3 className="font-medium">{component.label}</h3>
                  <p className="text-sm text-muted-foreground">
                    {getComponentTypeName(component.type)}
                  </p>
                  {isContainer &&
                    (component.type === "step" ||
                      component.type === "section") &&
                    "description" in component &&
                    component.description && (
                      <p className="text-xs text-muted-foreground mt-1">
                        {component.description}
                      </p>
                    )}
                </div>
              </div>
            </div>
            <div className="flex space-x-1">
              {isContainer && childComponents.length > 0 && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleToggleExpand}
                  aria-label={isExpanded ? "Collapse" : "Expand"}
                >
                  {isExpanded ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </Button>
              )}
              <Button
                variant="ghost"
                size="icon"
                onClick={(e) => {
                  e.stopPropagation();
                  onMove(component.id, "up");
                }}
                aria-label="Move up"
              >
                <CircleArrowUp className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={(e) => {
                  e.stopPropagation();
                  onMove(component.id, "down");
                }}
                aria-label="Move down"
              >
                <CircleArrowDown className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={(e) => {
                  e.stopPropagation();
                  // Pass true to activate the edit tab
                  onSelect(component.id, true);
                  // Ensure the component is scrolled into view
                  if (componentRef.current) {
                    setTimeout(() => {
                      componentRef.current?.scrollIntoView({
                        behavior: "smooth",
                        block: "center",
                      });
                    }, 100);
                  }
                }}
                aria-label="Settings"
              >
                <Settings className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete(component.id);
                }}
                aria-label="Delete"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {isContainer &&
            component.type === "section" &&
            childComponents.length === 0 && (
              <div className="mt-2 pl-10">
                <div className="rounded-md border border-dashed border-muted-foreground/30 p-2 text-sm text-muted-foreground">
                  <div className="flex items-center">
                    <ChevronDown className="mr-1 h-4 w-4" />
                    <span>Drag components here</span>
                  </div>
                </div>
              </div>
            )}
        </CardContent>
      </Card>

      {/* Render child components if expanded */}
      {isContainer && isExpanded && childComponents.length > 0 && (
        <div className="ml-8 mt-2 space-y-2">
          {childComponents.map((childComponent) => (
            <SortableComponentItem
              key={childComponent.id}
              id={childComponent.id}
              component={childComponent}
              allComponents={allComponents}
              isSelected={isSelected}
              onSelect={onSelect}
              onDelete={onDelete}
              onMove={onMove}
              // Use the expandedContainers state for each child component
              isExpanded={expandedContainers[childComponent.id] !== false}
              onToggleExpand={onToggleExpand}
              level={level + 1}
              expandedContainers={expandedContainers}
            />
          ))}
        </div>
      )}
    </div>
  );
}

export default memo(SortableComponentItem);
