import { useCallback } from "react";
import { DataGridComponent as DataGridType } from "@/lib/schemas/form-schemas";
import {
  getCellConfig,
  getCellInputType,
  isCellHeader,
  getCellCustomId,
} from "@/lib/utils/grid-utils";
import { useCalculationEngine } from "./useCalculationEngine";

/**
 * Decode HTML entities in validation expressions
 */
const decodeHtmlEntities = (str: string): string => {
  const htmlEntities: Record<string, string> = {
    "&lt;": "<",
    "&gt;": ">",
    "&le;": "≤",
    "&ge;": "≥",
    "&amp;": "&",
    "&quot;": '"',
    "&#39;": "'",
    "&nbsp;": " ",
  };

  return str.replace(/&[a-zA-Z0-9#]+;/g, (entity) => {
    return htmlEntities[entity] || entity;
  });
};

// Get validation rules for a cell
export const getCellValidationRules = (
  component: DataGridType,
  rowIndex: number,
  colIndex: number
): Record<string, any> => {
  const cell = getCellConfig(component.cells, rowIndex, colIndex);
  if (!cell || cell.type === "header" || !cell.validations) return {};

  const rules: Record<string, any> = {};
  const inputType = cell.inputType ?? "text";

  // Add required validation if specified
  if (cell.required) {
    rules.required = "This field is required";
  }

  // Process validations
  cell.validations.forEach((validation) => {
    switch (validation.rule) {
      case "required":
        rules.required = validation.message ?? "This field is required";
        break;
      case "min":
        if (inputType === "number") {
          rules.min = {
            value: validation.value,
            message:
              validation.message ?? `Minimum value is ${validation.value}`,
          };
        }
        break;
      case "max":
        if (inputType === "number") {
          rules.max = {
            value: validation.value,
            message:
              validation.message ?? `Maximum value is ${validation.value}`,
          };
        }
        break;
      case "minLength":
        if (inputType === "text") {
          rules.minLength = {
            value: validation.value,
            message:
              validation.message ??
              `Minimum length is ${validation.value} characters`,
          };
        }
        if (inputType === "number") {
          rules.min = {
            value: validation.value,
            message:
              validation.message ?? `Minimum value is ${validation.value}`,
          };
        }
        break;
      case "maxLength":
        if (inputType === "text") {
          rules.maxLength = {
            value: validation.value,
            message:
              validation.message ??
              `Maximum length is ${validation.value} characters`,
          };
        }
        if (inputType === "number") {
          rules.max = {
            value: validation.value,
            message:
              validation.message ?? `Maximum value is ${validation.value}`,
          };
        }
        break;
      case "pattern":
        if (inputType === "text") {
          rules.pattern = {
            value: new RegExp(validation.value as string),
            message: validation.message ?? "Invalid format",
          };
        }
        break;
      case "email":
        if (inputType === "text") {
          rules.pattern = {
            value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
            message: validation.message ?? "Invalid email address",
          };
        }
        break;
      case "url":
        if (inputType === "text") {
          rules.pattern = {
            value: /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,})(\/[/\w .-]*)?$/,
            message: validation.message ?? "Invalid URL",
          };
        }
        break;
      case "expression":
        rules.expression = {
          value: validation.expression,
          message: validation.message ?? "Invalid expression",
        };
        break;
    }
  });

  return rules;
};

// Validate number type values
export const validateNumberValue = (
  value: string,
  rules: Record<string, any>
): string | null => {
  const numValue = parseFloat(value);

  // Check min
  if (rules.min && numValue < rules.min.value) {
    return rules.min.message;
  }

  // Check max
  if (rules.max && numValue > rules.max.value) {
    return rules.max.message;
  }

  return null;
};

// Validate text type values
export const validateTextValue = (
  value: string,
  rules: Record<string, any>
): string | null => {
  // Check minLength
  if (rules.minLength && value.length < rules.minLength.value) {
    return rules.minLength.message;
  }

  // Check maxLength
  if (rules.maxLength && value.length > rules.maxLength.value) {
    return rules.maxLength.message;
  }

  // Check pattern
  if (rules.pattern && !rules.pattern.value.test(value)) {
    return rules.pattern.message;
  }

  return null;
};

// Helper function to validate value based on input type
export const validateValueByType = (
  component: DataGridType,
  rowIndex: number,
  colIndex: number,
  value: string,
  rules: Record<string, any>
): string | null => {
  const inputType = getCellInputType(component.cells, rowIndex, colIndex);

  if (inputType === "number") {
    return validateNumberValue(value, rules);
  }

  if (inputType === "text") {
    return validateTextValue(value, rules);
  }

  return null;
};

export const validateExpression = (
  value: string,
  rules: Record<string, any>,
  cellId: string,
  formValues: any,
  evaluateValidationExpression: (
    expression: string,
    componentId: string,
    context: any
  ) => boolean,
  gridComponentName: string
): string | null => {
  if (rules.expression) {
    // For data grid cells, we need to create a proper context
    // The expression engine expects the grid data to be flattened
    const contextData = { ...formValues };

    // Add the current cell value to the context using the custom cell ID
    // This allows expressions to reference this cell using its custom ID
    contextData[cellId] = value;

    // Also add it to the grid component's data if it exists
    if (
      contextData[gridComponentName] &&
      typeof contextData[gridComponentName] === "object"
    ) {
      contextData[gridComponentName] = {
        ...contextData[gridComponentName],
        [cellId]: value,
      };
    }

    // Decode HTML entities in the expression
    const decodedExpression = decodeHtmlEntities(rules.expression.value);

    const isValid = evaluateValidationExpression(
      decodedExpression,
      cellId, // Use the custom cell ID as the component ID
      contextData
    );

    if (!isValid) {
      return rules.expression.message;
    }
  }
  return null;
};

// Validate a cell value
export const validateCellValue = (
  component: DataGridType,
  rowIndex: number,
  colIndex: number,
  value: string,
  formValues: any,
  evaluateValidationExpression: (
    expression: string,
    componentId: string,
    context: any
  ) => boolean
): string | null => {
  const rules = getCellValidationRules(component, rowIndex, colIndex);
  if (Object.keys(rules).length === 0) return null;

  // Check required
  if (rules.required && (!value || value.toString().trim() === "")) {
    return rules.required;
  }

  // If value is empty and not required, no need to validate further
  if (!value || value.toString().trim() === "") return null;

  // Use custom cell ID for expression validation
  const cellId = getCellCustomId(component.cells, rowIndex, colIndex);
  const expressionError = validateExpression(
    value,
    rules,
    cellId,
    formValues,
    evaluateValidationExpression,
    component.name // Pass the grid component name
  );

  if (expressionError) return expressionError;

  const validateByTypeError = validateValueByType(
    component,
    rowIndex,
    colIndex,
    value.toString(),
    rules
  );
  return validateByTypeError;
};

// Validate all cells in the grid
export const validateAllCells = (
  component: DataGridType,
  gridData: Record<string, any>,
  formValues: any,
  evaluateValidationExpression: (
    expression: string,
    componentId: string,
    context: any
  ) => boolean
): Record<string, string> => {
  const errors: Record<string, string> = {};

  // Skip header row
  for (let rowIndex = 1; rowIndex < component.rows; rowIndex++) {
    // Skip header column
    for (let colIndex = 1; colIndex < component.columns; colIndex++) {
      // Use custom cell ID to get value from gridData
      const customCellId = getCellCustomId(component.cells, rowIndex, colIndex);
      const value = gridData[customCellId] ?? "";

      // Only validate data cells, not headers
      if (!isCellHeader(component.cells, rowIndex, colIndex)) {
        const errorMessage = validateCellValue(
          component,
          rowIndex,
          colIndex,
          value,
          formValues,
          evaluateValidationExpression
        );
        if (errorMessage) {
          // Store error using custom cell ID
          errors[customCellId] = errorMessage;
        }
      }
    }
  }
  return errors;
};

/**
 * Custom hook for data grid validation
 */
export function useDataGridValidation(
  component: DataGridType,
  formSchema: any,
  formValues: any
) {
  const { evaluateValidationExpression } = useCalculationEngine(
    formSchema,
    formValues
  );

  const validateCell = useCallback(
    (rowIndex: number, colIndex: number, value: string): string | null => {
      return validateCellValue(
        component,
        rowIndex,
        colIndex,
        value,
        formValues,
        evaluateValidationExpression
      );
    },
    [component, formValues, evaluateValidationExpression]
  );

  const validateGrid = useCallback(
    (gridData: Record<string, any>): Record<string, string> => {
      return validateAllCells(
        component,
        gridData,
        formValues,
        evaluateValidationExpression
      );
    },
    [component, formValues, evaluateValidationExpression]
  );

  return {
    validateCellValue: validateCell,
    validateAllCells: validateGrid,
  };
}
