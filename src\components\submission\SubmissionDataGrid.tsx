import {
  Table,
  TableBody,
  Table<PERSON>ell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { excelToIndices } from "@/lib/utils/grid-utils";

interface SubmissionDataGridProps {
  label: string;
  data: any;
}

// Types for better readability
type GridRow = {
  rowHeader: string;
  cells: Record<string, any>;
};

type RowCondition = {
  cellId: string;
  operator: string;
  value: string;
  targetRowIndex: number;
};

/**
 * Extracts cell value from a row's cells
 */
const extractCellValue = (cellData: any): string => {
  if (!cellData) return "";

  if (typeof cellData === "object") {
    return cellData.value ?? "";
  }

  return cellData ?? "";
};

/**
 * Gets a cell value from the grid data
 */
const getCellValue = (
  rows: GridRow[],
  columnHeaders: string[],
  rowIndex: number,
  _rowIndex: number,
  colIdx: number
): string => {
  // Convert indices to column header
  const colHeader = columnHeaders[colIdx - 1]; // Adjust for 1-based index
  if (!colHeader) return "";

  // If we're looking for a value in the current row
  if (_rowIndex === rowIndex + 1) {
    const row = rows[rowIndex];
    if (!row?.cells || !(colHeader in row.cells)) return "";
    return extractCellValue(row.cells[colHeader]);
  }

  // If we're looking for a value in a different row
  const targetRow = rows[_rowIndex - 1]; // Adjust for 0-based array indexing
  if (!targetRow?.cells || !(colHeader in targetRow.cells)) return "";
  return extractCellValue(targetRow.cells[colHeader]);
};

/**
 * Evaluates a single condition
 */
const evaluateCondition = (
  cellValue: string,
  operator: string,
  value: string
): boolean => {
  // Special handling for empty/notEmpty operators
  if (operator === "empty") {
    return cellValue === undefined || cellValue === null || cellValue === "";
  }

  if (operator === "notEmpty") {
    return cellValue !== undefined && cellValue !== null && cellValue !== "";
  }

  // Evaluate the condition based on the operator
  switch (operator) {
    case "equals":
      return cellValue === value;
    case "notEquals":
      return cellValue !== value;
    case "contains":
      return typeof cellValue === "string" && cellValue.includes(value);
    case "greaterThan":
      return Number(cellValue) > Number(value);
    case "lessThan":
      return Number(cellValue) < Number(value);
    default:
      return true; // Unknown operator, assume condition is met
  }
};

/**
 * Checks if a row should be rendered based on conditions
 */
const shouldRenderRow = (
  rows: GridRow[],
  columnHeaders: string[],
  rowIndex: number,
  conditionalRows: RowCondition[] | undefined
): boolean => {
  // If no conditions, always render
  if (!conditionalRows || conditionalRows.length === 0) {
    return true;
  }

  try {
    // Check if there are any conditions targeting this row
    const rowConditions = conditionalRows.filter(
      (condition) => condition.targetRowIndex === rowIndex + 1
    );

    // If no conditions for this row, render it
    if (rowConditions.length === 0) {
      return true;
    }

    // Check each condition
    for (const condition of rowConditions) {
      try {
        const { cellId, operator, value } = condition;

        // Convert cellId to indices
        const [condRowIndex, condColIndex] = excelToIndices(cellId);

        // Get the current value of the cell this condition depends on
        const cellValue = getCellValue(
          rows,
          columnHeaders,
          rowIndex,
          condRowIndex,
          condColIndex
        );

        // If any condition is met, render the row
        if (evaluateCondition(cellValue, operator, value)) {
          return true;
        }
      } catch (error) {
        console.error(`Error evaluating condition:`, error);
      }
    }

    // No conditions were met
    return false;
  } catch (error) {
    console.error(`Error in conditional row evaluation:`, error);
    return true; // On error, render the row to be safe
  }
};

/**
 * Formats cell data for display
 */
const formatCellData = (
  row: GridRow,
  colHeader: string
): { cellValue: string; unit: string } => {
  let cellValue = "";
  let unit = "";

  if (row.cells && colHeader in row.cells) {
    const cellData = row.cells[colHeader];

    if (cellData && typeof cellData === "object") {
      // Handle structured cell data with value and unit
      cellValue = cellData.value ?? "";
      unit = cellData.unit ?? "";
    } else {
      // Handle simple cell data
      cellValue = cellData ?? "";
    }
  }

  return { cellValue, unit };
};

/**
 * Renders a DataGrid component in read-only mode for submission viewing
 * Supports both structured format (old) and custom ID format (new) with backward compatibility
 */
export function SubmissionDataGrid({
  label,
  data,
}: Readonly<SubmissionDataGridProps>) {
  // Early return if no data or not in expected format
  if (!data?.rows?.length || !data?.metadata) {
    return (
      <div className="space-y-2 py-2">
        <div className="text-sm font-medium text-muted-foreground">{label}</div>
        <div className="text-sm italic text-muted-foreground">
          No data available - Data grid appears to be empty or in an unexpected format
        </div>
        <details className="mt-2">
          <summary className="text-xs text-muted-foreground cursor-pointer">
            View raw data for debugging
          </summary>
          <pre className="text-xs text-muted-foreground overflow-auto max-h-40 p-2 bg-muted/20 rounded mt-1">
            {JSON.stringify(data, null, 2)}
          </pre>
        </details>
      </div>
    );
  }

  const { rows, metadata } = data;

  // Get conditional rows from component metadata if available
  const conditionalRows = metadata.component?.conditionalRows;

  // Extract column headers from metadata or from the first row's cells
  let columnHeaders: string[] = [];

  // Try different ways to get column headers
  if (Array.isArray(metadata.columnHeaders)) {
    // If columnHeaders is an array in metadata
    columnHeaders = metadata.columnHeaders;
  } else if (
    metadata.columnHeaders &&
    typeof metadata.columnHeaders === "object"
  ) {
    // If columnHeaders is an object in metadata
    columnHeaders = Object.values(metadata.columnHeaders);
  } else if (rows[0]?.cells && typeof rows[0].cells === "object") {
    // Extract from the first row's cells as a fallback
    columnHeaders = Object.keys(rows[0].cells);
  }

  // Add a header for the row labels
  const tableHeaders = ["", ...columnHeaders];

  return (
    <div className="space-y-2 py-4">
      <div className="text-base font-medium">{label}</div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              {tableHeaders.map((header, index) => (
                <TableHead
                  key={`header-${header ?? index}`}
                  className="font-medium bg-muted/50"
                >
                  {header}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {rows
              .map((row: GridRow, rowIndex: number) => {
                // Check if this row should be rendered based on conditions
                if (
                  !shouldRenderRow(
                    rows,
                    columnHeaders,
                    rowIndex,
                    conditionalRows
                  )
                ) {
                  return null; // Skip rendering this row
                }

                return (
                  <TableRow key={`row-${row.rowHeader ?? rowIndex}`}>
                    {/* Row header cell */}
                    <TableCell className="font-medium bg-muted/30">
                      {row.rowHeader}
                    </TableCell>

                    {/* Data cells */}
                    {columnHeaders.map((colHeader) => {
                      const { cellValue, unit } = formatCellData(
                        row,
                        colHeader
                      );

                      return (
                        <TableCell key={`cell-${rowIndex}-${colHeader}`}>
                          {cellValue}
                          {unit}
                        </TableCell>
                      );
                    })}
                  </TableRow>
                );
              })
              .filter(Boolean)}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
