import { resolve } from "path";
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import tailwindcss from "@tailwindcss/vite";

export default defineConfig(() => {
  return {
    plugins: [react(), tailwindcss()],
    resolve: {
      alias: {
        "@": resolve(__dirname, "./src"),
      },
    },
    server: {
      host: true,
      port: 3000,
      allowedHosts: true ,
    },
    build: {
      rollupOptions: {
        external: () => false,
        output: {
          manualChunks: (id) => {
            if (id.includes("node_modules/prismjs")) return "vendor-prismjs";
            if (id.includes("node_modules/react-syntax-highlighter"))
              return "vendor-syntax-highlighter";
            if (id.includes("node_modules/react-simple-code-editor"))
              return "vendor-react";
            if (
              id.includes("node_modules/react/") ||
              id.includes("node_modules/react-dom/") ||
              id.includes("node_modules/react-router-dom/")
            )
              return "vendor-react";
            if (id.includes("node_modules/@radix-ui/react-"))
              return "vendor-ui";
            if (id.includes("node_modules/@dnd-kit/")) return "vendor-dnd";
            if (
              id.includes("node_modules/react-hook-form") ||
              id.includes("node_modules/@hookform/")
            )
              return "vendor-form";
            if (
              id.includes("node_modules/class-variance-authority") ||
              id.includes("node_modules/clsx") ||
              id.includes("node_modules/tailwind-merge") ||
              id.includes("node_modules/lucide-react")
            )
              return "vendor-utils";
            if (
              id.includes("/components/form-builder/") &&
              !id.includes("index")
            )
              return "form-builder-components";
            if (id.includes("/hooks/") && !id.includes("index")) return "hooks";
            return undefined;
          },
        },
      },
      chunkSizeWarningLimit: 1000,
    },
  };
});
