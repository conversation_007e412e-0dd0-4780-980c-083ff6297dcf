
# Build stage
FROM node:20-alpine AS builder

WORKDIR /app

# Install build dependencies for native modules
RUN apk add --no-cache g++ make python3

COPY package*.json ./

# Clear npm cache and install dependencies with force rebuild
RUN npm cache clean --force && \
  rm -rf node_modules package-lock.json && \
  npm install

COPY . .
RUN npm run build

# Production stage
FROM nginx:alpine AS production

RUN apk add --no-cache wget

COPY nginx.conf /etc/nginx/nginx.conf
COPY --from=builder /app/dist /usr/share/nginx/html
COPY docker-entrypoint.sh /docker-entrypoint.sh

RUN sed -i 's/\r$//' /docker-entrypoint.sh && \
  chmod +x /docker-entrypoint.sh && \
  chown -R nginx:nginx /usr/share/nginx/html && \
  chown nginx:nginx /docker-entrypoint.sh

EXPOSE 3000


ENTRYPOINT ["/docker-entrypoint.sh"]
CMD ["nginx", "-g", "daemon off;"]
