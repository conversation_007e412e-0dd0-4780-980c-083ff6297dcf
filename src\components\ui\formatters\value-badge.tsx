import { EnhancedBadge } from "@/components/ui/enhanced-badge";
import {
  TrendingUp,
  TrendingDown,
  Minus,
  DollarSign,
  Percent,
  Hash,
} from "lucide-react";
import { cn } from "@/lib/utils";

interface ValueBadgeProps {
  value: number | string;
  type?: "number" | "currency" | "percent" | "custom";
  size?: "sm" | "md" | "lg";
  showIcon?: boolean;
  showTrend?: boolean;
  thresholds?: {
    positive?: number;
    negative?: number;
  };
  prefix?: string;
  suffix?: string;
  className?: string;
  formatter?: (value: number) => string;
}

/**
 * Component for displaying numeric values as badges with optional icons and formatting
 */
export function ValueBadge({
  value,
  type = "number",
  size = "md",
  showIcon = true,
  showTrend = false,
  thresholds = { positive: 0, negative: 0 },
  prefix,
  suffix,
  className,
  formatter,
}: ValueBadgeProps) {
  // Convert value to number if it's a string
  const numericValue = typeof value === "string" ? parseFloat(value) : value;
  const isNumeric = !isNaN(numericValue);

  // Determine badge variant based on value and thresholds
  const getBadgeVariant = () => {
    if (!isNumeric) return "outline";

    const positiveThreshold = thresholds.positive ?? 0;
    const negativeThreshold = thresholds.negative ?? 0;

    if (numericValue > positiveThreshold) return "success";
    if (numericValue < negativeThreshold) return "destructive";

    // For percent type, add more granular variants
    if (type === "percent") {
      if (numericValue >= 80) return "success";
      if (numericValue >= 50) return "info";
      if (numericValue >= 25) return "warning";
      if (numericValue < 25) return "destructive";
    }

    return "secondary";
  };

  // Get icon based on type and value
  const getIcon = () => {
    if (!showIcon) return null;

    if (type === "currency") {
      return <DollarSign className="h-3.5 w-3.5 mr-1" />;
    }

    if (type === "percent") {
      return <Percent className="h-3.5 w-3.5 mr-1" />;
    }

    if (showTrend && isNumeric) {
      if (numericValue > 0) {
        return <TrendingUp className="h-3.5 w-3.5 mr-1" />;
      }
      if (numericValue < 0) {
        return <TrendingDown className="h-3.5 w-3.5 mr-1" />;
      }
      return <Minus className="h-3.5 w-3.5 mr-1" />;
    }

    return <Hash className="h-3.5 w-3.5 mr-1" />;
  };

  // Format the value based on type
  const formatValue = () => {
    if (!isNumeric) return String(value);

    if (formatter) {
      return formatter(numericValue);
    }

    switch (type) {
      case "currency":
        return new Intl.NumberFormat("en-US", {
          style: "currency",
          currency: "USD",
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        }).format(numericValue);
      case "percent":
        return `${numericValue.toFixed(1)}%`;
      case "number":
      default:
        return numericValue.toLocaleString();
    }
  };

  // Get size classes
  const getSizeClasses = () => {
    switch (size) {
      case "sm":
        return "text-xs px-2 py-0.5";
      case "lg":
        return "text-sm px-3 py-1";
      case "md":
      default:
        return "text-xs px-2.5 py-0.5";
    }
  };

  // Format the final display value with optional prefix and suffix
  const displayValue = `${prefix || ""}${formatValue()}${suffix || ""}`;

  return (
    <EnhancedBadge
      variant={getBadgeVariant()}
      className={cn(`flex items-center ${getSizeClasses()}`, className)}
    >
      {getIcon()}
      {displayValue}
    </EnhancedBadge>
  );
}

export default ValueBadge;
