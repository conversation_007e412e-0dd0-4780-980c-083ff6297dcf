import { apiClient } from "@/lib/api/api-client";
import {PageableObject, PermissionStatus} from "@/lib/types/api.ts";


export interface Project {
  projectRef: string;
  name: string;
  permissions: PermissionStatus
}

export interface ProjectsResponse {
  content: Project[];
  page: PageableObject
}

/**
 * Service to retrieve permissions for a specific project
 */
export class ProjectPermissionsService {
  /**
   * Retrieves a project's permissions by its ID
   */
  static async getProjectPermissions(projectId: string): Promise<PermissionStatus | null> {
    try {
      const response = await apiClient.get<ProjectsResponse>(`/projects?projectRef=${projectId}`);

      if (response.data.content && response.data.content.length > 0) {
        const project = response.data.content[0];
        //console.log('🚀 ~ project:', project.permissions);
        return project.permissions;
      }

      return null;
    } catch (error) {
      console.error(`Error retrieving project permissions ${projectId}:`, error);
      return null;
    }
  }
}
