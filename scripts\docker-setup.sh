#!/bin/bash

# Docker setup script for HNES Form Builder
# This script helps set up the Docker environment for development

set -e

echo "🐳 HNES Form Builder Docker Setup"
echo "=================================="

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    echo "   Visit: https://docs.docker.com/get-docker/"
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    echo "   Visit: https://docs.docker.com/compose/install/"
    exit 1
fi

echo "✅ Docker and Docker Compose are installed"

# Check if .env.local exists
if [ ! -f ".env.local" ]; then
    echo "📝 Creating .env.local from template..."
    cp .env.docker .env.local
    echo "✅ Created .env.local file"
    echo "💡 You can edit .env.local to customize environment variables"
else
    echo "✅ .env.local already exists"
fi

# Function to start development environment
start_dev() {
    echo "🚀 Starting development environment (connects to your existing backend on port 8080)..."
    docker-compose up --build app
}

# Function to start development with mock API
start_dev_mock() {
    echo "🚀 Starting development environment with mock API..."
    docker-compose --profile mock-api up --build
}

# Function to start production environment
start_prod() {
    echo "🚀 Starting production environment..."
    docker-compose --profile production up --build app-prod
}

# Function to stop all services
stop_services() {
    echo "🛑 Stopping all services..."
    docker-compose down
}

# Function to clean up
cleanup() {
    echo "🧹 Cleaning up Docker resources..."
    docker-compose down -v --rmi local
    echo "✅ Cleanup complete"
}

# Main menu
echo ""
echo "What would you like to do?"
echo "1) Start development environment (connects to existing backend on port 8080)"
echo "2) Start development with mock API (if no backend available)"
echo "3) Start production environment"
echo "4) Stop all services"
echo "5) Clean up Docker resources"
echo "6) Exit"

read -p "Enter your choice (1-6): " choice

case $choice in
    1)
        start_dev
        ;;
    2)
        start_dev_mock
        ;;
    3)
        start_prod
        ;;
    4)
        stop_services
        ;;
    5)
        cleanup
        ;;
    6)
        echo "👋 Goodbye!"
        exit 0
        ;;
    *)
        echo "❌ Invalid choice. Please run the script again."
        exit 1
        ;;
esac
