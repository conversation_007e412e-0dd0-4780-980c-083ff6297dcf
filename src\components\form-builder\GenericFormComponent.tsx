import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, UseFormReturn } from "react-hook-form";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

/**
 * Props for the generic form component
 */
interface GenericFormComponentProps<
  TFormValues extends FieldValues,
  TSubmitResponse
> {
  // Form title
  title: string;

  // Form description (optional)
  description?: string;

  // React Hook Form methods
  methods: UseFormReturn<TFormValues>;

  // Form state
  isSubmitting: boolean;

  // Form status
  formStatus?: {
    message: string;
    type: "idle" | "loading" | "success" | "error";
  };

  // Submit handler
  onSubmit: () => Promise<TSubmitResponse | undefined>;

  // Cancel handler (optional)
  onCancel?: () => void;

  // Submit button text (optional)
  submitText?: string;

  // Cancel button text (optional)
  cancelText?: string;

  // Children (form fields)
  children: React.ReactNode;
}

/**
 * Generic form component with TypeScript generics
 */
export function GenericFormComponent<
  TFormValues extends FieldValues = FieldValues,
  TSubmitResponse = any
>({
  title,
  description,
  methods,
  isSubmitting,
  formStatus,
  onSubmit,
  onCancel,
  submitText = "Submit",
  cancelText = "Cancel",
  children,
}: Readonly<GenericFormComponentProps<TFormValues, TSubmitResponse>>) {
  // Handle form submission
  const handleFormSubmit = methods.handleSubmit(async () => {
    await onSubmit();
  });

  // Helper function to get alert class based on status type
  const getAlertClassByType = (
    type: "idle" | "loading" | "success" | "error"
  ): string => {
    if (type === "error") return "bg-destructive/10 text-destructive";
    if (type === "success") return "bg-success/10 text-success";
    return "";
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && (
          <p className="text-sm text-muted-foreground">{description}</p>
        )}
      </CardHeader>

      <FormProvider {...methods}>
        <form onSubmit={handleFormSubmit}>
          <CardContent>
            {/* Form status message */}
            {formStatus?.message && formStatus.type !== "idle" && (
              <Alert className={`mb-4 ${getAlertClassByType(formStatus.type)}`}>
                <AlertDescription>{formStatus.message}</AlertDescription>
              </Alert>
            )}

            {/* Form fields */}
            <div className="space-y-4">{children}</div>
          </CardContent>

          <CardFooter className="flex justify-between">
            {/* Cancel button */}
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting}
              >
                {cancelText}
              </Button>
            )}

            {/* Submit button */}
            <Button
              type="submit"
              disabled={isSubmitting}
              className={onCancel ? "" : "ml-auto"}
            >
              {isSubmitting && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              {submitText}
            </Button>
          </CardFooter>
        </form>
      </FormProvider>
    </Card>
  );
}

/**
 * Type-safe field component for use with GenericFormComponent
 */
interface FormFieldProps<T extends FieldValues> {
  name: keyof T & string;
  label: string;
  required?: boolean;
  children: React.ReactNode;
  description?: string;
  error?: string;
}

export function FormField<T extends FieldValues>({
  name,
  label,
  required,
  children,
  description,
  error,
}: Readonly<FormFieldProps<T>>) {
  return (
    <div className="space-y-2">
      <div className="flex items-center">
        <label
          htmlFor={name}
          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
        >
          {label}
          {required && <span className="text-destructive ml-1">*</span>}
        </label>
      </div>

      {description && (
        <p className="text-xs text-muted-foreground">{description}</p>
      )}

      {children}

      {error && <p className="text-sm text-destructive">{error}</p>}
    </div>
  );
}
