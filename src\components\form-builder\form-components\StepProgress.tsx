import { memo } from "react";

interface StepProgressProps {
  currentStep: number;
  totalSteps: number;
  stepLabel?: string;
  stepDescription?: string;
  steps?: Array<{
    label: string;
    description?: string;
  }>;
}

/**
 * Displays progress information for multi-step forms
 */
function StepProgress({
  currentStep,
  totalSteps,
  stepLabel,
  stepDescription,
  steps,
}: StepProgressProps) {
  // Get current step label and description
  const currentStepLabel = steps ? steps[currentStep]?.label : stepLabel;
  const currentStepDescription = steps
    ? steps[currentStep]?.description
    : stepDescription;

  return (
    <div className="mt-4">
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-lg font-medium">{currentStepLabel}</h3>
        <div className="text-sm text-muted-foreground">
          Step {currentStep + 1} of {totalSteps}
        </div>
      </div>

      {/* Step progress bar */}
      <div className="w-full bg-muted h-2 rounded-full overflow-hidden">
        <div
          className="bg-primary h-full transition-all duration-300 ease-in-out"
          style={{
            width: `${((currentStep + 1) / totalSteps) * 100}%`,
          }}
        />
      </div>

      {currentStepDescription && (
        <p className="text-sm text-muted-foreground mt-2">
          {currentStepDescription}
        </p>
      )}
    </div>
  );
}

export default memo(StepProgress);
