import React from "react";
import { Row } from "@tanstack/react-table";
import { DateFormatter } from "@/components/ui/formatters/date-formatter";
import { ColumnConfig } from "@/lib/types/page-config";

interface DateCellRendererProps {
  row: Row<unknown>;
  columnId: string;
  configColumn: ColumnConfig;
  showTime?: boolean;
}

export const DateCellRenderer: React.FC<DateCellRendererProps> = ({
  row,
  columnId,
  configColumn,
  showTime = false,
}) => {
  const value = row.getValue(columnId);
  return value ? (
    <DateFormatter
      dateString={String(value)}
      format={(configColumn.formatOptions?.variant as any) ?? "medium"}
      showTime={showTime}
    />
  ) : null;
};
