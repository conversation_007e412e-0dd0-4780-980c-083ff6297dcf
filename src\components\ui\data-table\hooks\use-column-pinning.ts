import { useState, useCallback, useEffect, useRef } from "react";
import { ColumnDef } from "@tanstack/react-table";

interface UseColumnPinningProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  enablePinning: boolean;
  defaultPinnedColumns: Record<string, number>;
}

interface UseColumnPinningResult {
  pinnedColumns: Record<string, number>;
  canColumnBePinned: (columnId: string) => boolean;
  toggleColumnPin: (columnId: string, shouldPin?: boolean) => void;
  handlePinColumn: (columnId: string) => void;
}

/**
 * Custom hook to manage column pinning functionality
 */
export function useColumnPinning<TData, TValue>({
  columns,
  enablePinning,
  defaultPinnedColumns,
}: UseColumnPinningProps<TData, TValue>): UseColumnPinningResult {
  // State for pinned columns - store the order of pinning, initialize with defaultPinnedColumns
  const [pinnedColumns, setPinnedColumns] = useState<Record<string, number>>(
    () => {
      // Filter defaultPinnedColumns to only include columns that can be pinned
      const filteredPinnedColumns: Record<string, number> = {};

      // Process each default pinned column
      Object.entries(defaultPinnedColumns).forEach(([columnId, order]) => {
        // Find the column definition
        const column = columns.find((col) =>
          typeof col.id === "string" ? col.id === columnId : false
        );

        // Check if column has meta.enablePinning defined
        const columnEnablePinning = column?.meta?.enablePinning;

        // Only include if pinning is enabled for this column
        if ((columnEnablePinning ?? enablePinning) && order > 0) {
          filteredPinnedColumns[columnId] = order;
        }
      });

      return filteredPinnedColumns;
    }
  );

  // Check if a column can be pinned based on its meta settings
  const canColumnBePinned = useCallback(
    (columnId: string) => {
      // Find the column definition
      const column = columns.find((col) =>
        typeof col.id === "string" ? col.id === columnId : false
      );

      // Check if column has meta.enablePinning defined
      const columnEnablePinning = column?.meta?.enablePinning;

      // If column has explicit setting, use it; otherwise use the global setting
      return columnEnablePinning ?? enablePinning;
    },
    [columns, enablePinning]
  );

  // Function to pin or unpin a column
  const toggleColumnPin = useCallback(
    (columnId: string, shouldPin?: boolean) => {
      // Check if this column can be pinned
      if (!canColumnBePinned(columnId)) return;

      setPinnedColumns((prev) => {
        const newPinnedColumns = { ...prev };
        const isCurrentlyPinned = Boolean(newPinnedColumns[columnId]);

        // If shouldPin is not specified, toggle the current state
        const shouldPinColumn = shouldPin ?? !isCurrentlyPinned;

        if (isCurrentlyPinned && !shouldPinColumn) {
          // Unpin the column
          delete newPinnedColumns[columnId];

          // Reorder remaining pinned columns
          const sortedIds = Object.entries(newPinnedColumns)
            .sort(([, orderA], [, orderB]) => orderA - orderB)
            .map(([id]) => id);

          // Reset order numbers
          return sortedIds.reduce((acc, id, index) => {
            acc[id] = index + 1;
            return acc;
          }, {} as Record<string, number>);
        } else if (!isCurrentlyPinned && shouldPinColumn) {
          // Pin the column with the next order number
          const maxOrder =
            Object.values(prev).length > 0
              ? Math.max(...Object.values(prev))
              : 0;
          newPinnedColumns[columnId] = maxOrder + 1;
          return newPinnedColumns;
        }

        // No change needed
        return prev;
      });
    },
    [canColumnBePinned]
  );

  // Handle UI pin button click - just a wrapper around toggleColumnPin
  const handlePinColumn = useCallback(
    (columnId: string) => {
      toggleColumnPin(columnId);
    },
    [toggleColumnPin]
  );

  // Cleanup effect to remove pinned columns that are no longer allowed to be pinned
  const cleanupRef = useRef(false);

  useEffect(() => {
    if (cleanupRef.current) return;

    cleanupRef.current = true;

    // Use setTimeout to prevent rapid re-renders
    const timeoutId = setTimeout(() => {
      setPinnedColumns((prev) => {
        const newPinnedColumns = { ...prev };
        let hasChanges = false;

        // Check each pinned column
        Object.keys(newPinnedColumns).forEach((columnId) => {
          if (!canColumnBePinned(columnId)) {
            delete newPinnedColumns[columnId];
            hasChanges = true;
          }
        });

        // Only return a new object if changes were made
        return hasChanges ? newPinnedColumns : prev;
      });

      cleanupRef.current = false;
    }, 50);

    return () => clearTimeout(timeoutId);
  }, [canColumnBePinned, columns, enablePinning]);

  return {
    pinnedColumns,
    canColumnBePinned,
    toggleColumnPin,
    handlePinColumn,
  };
}
