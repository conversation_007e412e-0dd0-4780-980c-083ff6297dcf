import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { CustomBadge } from "@/components/ui/custom-badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Search, Calendar, FileText } from "lucide-react";
import { useApplications, EnhancedApplication } from "@/hooks/useApplications";

// Format date for display
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString("en-GB", {
    day: "numeric",
    month: "short",
    year: "numeric",
  });
};

// Application Card Component
interface ApplicationCardProps {
  application: EnhancedApplication;
}

const ApplicationCard = ({ application }: ApplicationCardProps) => {
  // Determine if the application is in progress
  const isInProgress = application.submissionStatus === "draft";

  // Determine the button text based on the submission status
  const buttonText = isInProgress
    ? "Continue Application"
    : "Start Application";

  // Determine the link URL (include submissionId if it exists)
  const linkUrl =
    isInProgress && application.submissionId
      ? `/applications/${application.id}/submit/${application.submissionId}`
      : `/applications/${application.id}/submit`;

  return (
    <Card className="transition-all hover:shadow-md">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>{application.name}</CardTitle>
          {isInProgress ? (
            <CustomBadge variant="outline">In Progress</CustomBadge>
          ) : (
            <CustomBadge variant="default">Active</CustomBadge>
          )}
        </div>
        <CardDescription>{application.description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {application.createdAt && (
            <div className="flex items-center text-sm">
              <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
              <span>Created: {formatDate(application.createdAt)}</span>
            </div>
          )}
          {application.updatedAt && (
            <div className="flex items-center text-sm">
              <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
              <span>Updated: {formatDate(application.updatedAt)}</span>
            </div>
          )}

        </div>
      </CardContent>
      <CardFooter>
        <Button asChild variant="outline" className="w-full">
          <Link to={linkUrl}>{buttonText}</Link>
        </Button>
      </CardFooter>
    </Card>
  );
};

// Search Bar Component
interface SearchBarProps {
  searchQuery: string;
  onSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const SearchBar = ({ searchQuery, onSearchChange }: SearchBarProps) => {
  return (
    <div className="relative flex-1">
      <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
      <Input
        type="search"
        placeholder="Search applications..."
        className="pl-8"
        value={searchQuery}
        onChange={onSearchChange}
      />
    </div>
  );
};

// Applications List Component
interface ApplicationsListProps {
  applications: EnhancedApplication[];
  isLoading: boolean;
  searchQuery: string;
}

const ApplicationsList = ({
  applications,
  isLoading,
  searchQuery,
}: ApplicationsListProps) => {
  if (isLoading) {
    return <ApplicationsSkeleton />;
  }

  if (applications.length === 0) {
    return <EmptyState searchQuery={searchQuery} />;
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {applications.map((app) => (
        <ApplicationCard key={app.id} application={app} />
      ))}
    </div>
  );
};

// Loading Skeleton Component
const ApplicationsSkeleton = () => (
  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
    {[1, 2, 3].map((i) => (
      <Card key={i} className="overflow-hidden">
        <CardHeader className="gap-2">
          <Skeleton className="h-5 w-1/2" />
          <Skeleton className="h-4 w-full" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-24 w-full" />
        </CardContent>
        <CardFooter>
          <Skeleton className="h-4 w-1/3" />
        </CardFooter>
      </Card>
    ))}
  </div>
);

// Empty State Component
interface EmptyStateProps {
  searchQuery: string;
}

const EmptyState = ({ searchQuery }: EmptyStateProps) => (
  <Card className="flex flex-col items-center justify-center p-6 text-center">
    <div className="mb-4 rounded-full bg-muted p-3">
      <FileText className="h-6 w-6 text-muted-foreground" />
    </div>
    <CardTitle className="mb-2">No applications found</CardTitle>
    <CardDescription>
      {searchQuery
        ? `No applications match "${searchQuery}"`
        : "There are no active applications available at the moment."}
    </CardDescription>
  </Card>
);

// Main Page Component
export default function ApplicationsPage() {
  // Use our custom hook for applications data and search functionality
  const { filteredApplications, isLoading, searchQuery, handleSearchChange } =
    useApplications();

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Applications</h1>
      </div>

      <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
        <SearchBar
          searchQuery={searchQuery}
          onSearchChange={handleSearchChange}
        />
      </div>

      <ApplicationsList
        applications={filteredApplications}
        isLoading={isLoading}
        searchQuery={searchQuery}
      />
    </div>
  );
}
