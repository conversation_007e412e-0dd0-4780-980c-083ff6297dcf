import { useApiQuery, useApiMutation, useInvalidateQueries } from "./useApi";
import { GetAllResponses, queryKeys } from "@/lib/types/api";
import { useMutation } from "@tanstack/react-query";
import { apiClient } from "@/lib/api/api-client";
import { useToast } from "@/components/ui/use-toast";

/**
 * Type definition for a funding round
 */
export type FundingRound = {
  id: string;
  roundNumber: number;
  openDate: string;
  closeDate: string;
  createdAt?: string;
  updatedAt?: string;
};

/**
 * Hook for fetching all funding rounds
 */
export function useFundingRounds() {
  return useApiQuery<GetAllResponses<FundingRound>>(
    queryKeys.fundingRounds(),
    "/fundingRounds",
    undefined,
    {
      // Keep funding rounds data fresh for a reasonable time (5 minutes)
      staleTime: 5 * 60 * 1000,
    }
  );
}

/**
 * Hook for fetching a single funding round by ID
 */
export function useFundingRound(id: string) {
  return useApiQuery<FundingRound>(
    queryKeys.fundingRound(id),
    `/fundingRounds/${id}`,
    undefined,
    {
      // Don't fetch if no ID is provided
      enabled: !!id,
    }
  );
}

/**
 * Hook for creating a new funding round
 */
export function useCreateFundingRound() {
  const { invalidateQueries } = useInvalidateQueries();

  return useApiMutation<
    FundingRound,
    Omit<FundingRound, "id" | "createdAt" | "updatedAt">
  >("/fundingRounds", "POST", {
    onSuccess: () => {
      // Invalidate funding rounds list to refetch data
      invalidateQueries(queryKeys.fundingRounds());
    },
  });
}

/**
 * Type for the response when attaching a project to a funding round
 */
export type AttachProjectResponse = {
  success: boolean;
  message?: string;
};

/**
 * Hook for attaching a project to a funding round
 */
export function useAttachProjectToFundingRound() {
  const { invalidateQueries } = useInvalidateQueries();
  const { toast } = useToast();

  return useMutation<
    AttachProjectResponse,
    Error,
    { fundingRoundId: string; projectId: string }
  >({
    mutationFn: async ({ fundingRoundId, projectId }) => {
      const endpoint = `/fundingRounds/${fundingRoundId}/project/${projectId}`;
      const response = await apiClient.patch<AttachProjectResponse>(endpoint);
      return response.data;
    },
    onSuccess: (_, variables) => {
      // Show success toast
      toast({
        title: "Success",
        description: "Project successfully attached to funding round",
      });

      // Invalidate funding rounds to refresh any attached project data
      invalidateQueries(queryKeys.fundingRounds());
      invalidateQueries(queryKeys.fundingRound(variables.fundingRoundId));

      // Invalidate projects to refresh any funding round associations
      invalidateQueries(queryKeys.projects());
      invalidateQueries(queryKeys.project(variables.projectId));
    },
    onError: (error) => {
      // Show error toast
      toast({
        title: "Error",
        description:
          error.message || "Failed to attach project to funding round",
        variant: "destructive",
      });
    },
  });
}

/**
 * Hook for updating an existing funding round
 */
export function useUpdateFundingRound() {
  const { invalidateQueries } = useInvalidateQueries();

  return useApiMutation<
    FundingRound,
    { id: string; data: Partial<FundingRound> }
  >("/fundingRounds", "PUT", {
    onSuccess: (_, variables) => {
      // Invalidate both the specific funding round and the list
      invalidateQueries(queryKeys.fundingRound(variables.id));
      invalidateQueries(queryKeys.fundingRounds());
    },
  });
}

/**
 * Hook for deleting a funding round
 */
export function useDeleteFundingRound() {
  const { invalidateQueries } = useInvalidateQueries();

  return useApiMutation<void, string>("/fundingRounds", "DELETE", {
    onSuccess: (_, id) => {
      // Invalidate both the specific funding round and the list
      invalidateQueries(queryKeys.fundingRound(id));
      invalidateQueries(queryKeys.fundingRounds());
    },
  });
}
