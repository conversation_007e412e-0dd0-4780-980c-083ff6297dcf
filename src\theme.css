.text-schema {
  font-weight: 700;
  font-size: 1.25rem; /* text-xl */
  letter-spacing: -0.025em;
  text-shadow: 0 1px 2px rgba(54, 178, 61, 0.15);
  background: linear-gradient(to right, var(--primary), #2d9a33);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  position: relative;
}

/* Add a subtle animation on hover */
@keyframes pulse {
  0% {
    text-shadow: 0 1px 2px rgba(54, 178, 61, 0.15);
  }
  50% {
    text-shadow: 0 1px 8px rgba(54, 178, 61, 0.3);
  }
  100% {
    text-shadow: 0 1px 2px rgba(54, 178, 61, 0.15);
  }
}

.text-schema:hover {
  animation: pulse 2s infinite ease-in-out;
}
