import React, { useMemo } from "react";
import { useLocation } from "react-router-dom";
import { FileText } from "lucide-react";
import { User } from "@/lib/types/auth";

export interface NavItem {
  name: string;
  href: string;
  icon: React.ReactNode;
  active: boolean;
}

interface UseNavigationProps {
  isAuthenticated: boolean;
  user: User | null;
}

/**
 * Hook to generate navigation items based on user role and authentication status
 */
export function useNavigation({ isAuthenticated, user }: UseNavigationProps) {
  const location = useLocation();

  const navItems = useMemo(() => {
    // Base navigation items that all users can see
    const items: NavItem[] = [
      // {
      //   name: "Applications",
      //   href: "/applications",
      //   icon: <LayoutGrid className="h-5 w-5" />,
      //   active: location.pathname.startsWith("/applications"),
      // },
    ];

    // Admin-only navigation items
    if (isAuthenticated && user?.role === "admin") {
      // Add submissions dashboard for admins
      // items.push({
      //   name: "Submissions",
      //   href: "/submissions",
      //   icon: <FileText className="h-5 w-5" />,
      //   active: location.pathname.startsWith("/submissions"),
      // });

      // Add Dynamic Page Examples
      items.push({
        name: "Projects List",
        href: "/projects",
        icon: <FileText className="h-5 w-5" />,
        active: location.pathname.startsWith("/projects"),
      });

      items.push({
        name: "Funding Rounds",
        href: "/funding-rounds",
        icon: <FileText className="h-5 w-5" />,
        active: location.pathname.startsWith("/funding-rounds"),
      });

      // Add form builder as the first item
      items.unshift({
        name: "Form Builder",
        href: "/forms",
        icon: <FileText className="h-5 w-5" />,
        active: location.pathname.startsWith("/forms"),
      });

      items.push({
        name: "Users",
        href: "/users",
        icon: <FileText className="h-5 w-5" />,
        active: location.pathname.startsWith("/users"),
      });
    }

    return items;
  }, [location.pathname, isAuthenticated, user?.role]);

  return { navItems };
}
