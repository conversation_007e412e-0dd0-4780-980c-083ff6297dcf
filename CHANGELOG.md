# Changelog

All notable changes to the HNES Form Builder project will be documented in this file.

This changelog is automatically generated using [Conventional Commits](https://www.conventionalcommits.org/).
The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.1.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Features

- Initial project setup with React 19, TypeScript, and Vite
- Form builder core functionality with drag-and-drop interface
- Multi-step form creation capabilities
- Conditional rendering rules for form components
- Data Grid component with Excel-style configuration
- Form validation support
- JSON Schema generation
- Responsive layout with collapsible navigation
- Role-based access control (Admin and Applicant views)
- Form submission and application management
- shadcn UI components integration
- Tailwind CSS 4 styling
- React Hook Form for form state management
- TanStack Table for configurable tables
- Performance optimizations with React best practices
- Code splitting with dynamic imports

### Documentation

- Add Conventional Commits documentation and tooling
- Add automated changelog generation from commit messages

## [0.1.0] - YYYY-MM-DD

<!-- Future release -->

[Unreleased]: https://gitlab.dom.tti/gemserv/Scheme-Manager-Frontend/compare/v0.1.0...HEAD
[0.1.0]: https://gitlab.dom.tti/gemserv/Scheme-Manager-Frontend/releases/tag/v0.1.0
