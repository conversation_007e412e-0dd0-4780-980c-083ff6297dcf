import { cva } from "class-variance-authority";
import { cn } from "@/lib/utils";

interface ProgressBarProps {
  progress: number;
  showPercentage?: boolean;
  height?: number;
  color?: string;
  size?: "sm" | "md" | "lg";
  variant?: "default" | "success" | "warning" | "danger" | "info";
  showLabel?: boolean;
  labelPosition?: "right" | "inside";
  animated?: boolean;
  className?: string;
}

// Define variants for progress bar
const progressVariants = cva(
  "h-full rounded-full transition-all duration-300",
  {
    variants: {
      variant: {
        default: "bg-primary",
        success: "bg-green-500",
        warning: "bg-yellow-500",
        danger: "bg-warning",
        info: "bg-info",
      },
      size: {
        sm: "h-1.5",
        md: "h-2.5",
        lg: "h-4",
      },
      animated: {
        true: "animate-pulse",
        false: "",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "md",
      animated: false,
    },
  }
);

/**
 * Enhanced component for displaying progress bars
 */
export function ProgressBar({
  progress,
  showPercentage = false,
  height,
  color,
  size = "md",
  variant = "default",
  showLabel = false,
  labelPosition = "right",
  animated = false,
  className,
}: Readonly<ProgressBarProps>) {
  // Ensure progress is between 0 and 100
  const normalizedProgress = Math.max(0, Math.min(100, progress));

  // Determine color variant based on progress value if not explicitly set
  const getAutoVariant = () => {
    if (variant !== "default") return variant;

    if (normalizedProgress >= 80) return "success";
    if (normalizedProgress >= 50) return "info";
    if (normalizedProgress >= 25) return "warning";
    if (normalizedProgress < 25) return "danger";
    return "default";
  };

  // Get the appropriate height based on size
  const getHeight = () => {
    if (height) return height;

    switch (size) {
      case "sm":
        return 6;
      case "lg":
        return 16;
      case "md":
      default:
        return 10;
    }
  };

  // Format the progress label
  const progressLabel = `${normalizedProgress.toFixed(0)}%`;

  // Determine if we should show the label inside (only for larger sizes and progress)
  const showInsideLabel =
    labelPosition === "inside" && size !== "sm" && normalizedProgress > 25;

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <div className="relative flex-1">
        <div
          className="w-full bg-muted rounded-full overflow-hidden"
          style={{ height: getHeight() }}
        >
          <div
            className={progressVariants({
              variant: getAutoVariant(),
              size,
              animated,
            })}
            style={{
              width: `${normalizedProgress}%`,
              backgroundColor: color || undefined,
            }}
          >
            {showInsideLabel && (
              <span className="text-xs text-white px-2 flex items-center h-full font-medium">
                {progressLabel}
              </span>
            )}
          </div>
        </div>
      </div>

      {(showPercentage || (showLabel && labelPosition === "right")) && (
        <span className="text-xs text-muted-foreground min-w-8 text-right">
          {progressLabel}
        </span>
      )}
    </div>
  );
}

export default ProgressBar;
