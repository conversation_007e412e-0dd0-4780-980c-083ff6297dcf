import { memo, useCallback } from "react";
import { InfoTextComponent } from "@/lib/types/form";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { InfoTextInput } from "../form-components/InputComponents";

interface InfoTextConfigTabContentProps {
  component: InfoTextComponent;
  onChange: <K extends keyof InfoTextComponent>(
    field: K,
    value: InfoTextComponent[K]
  ) => void;
}

/**
 * Editor for configuring InfoText component properties
 */
const InfoTextConfigTabContent = memo(function InfoTextConfigTabContent({
  component,
  onChange,
}: InfoTextConfigTabContentProps) {
  // Handle content change
  const handleContentChange = useCallback(
    (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      onChange("infoContent", e.target.value);
    },
    [onChange]
  );

  // Handle variant change
  const handleVariantChange = useCallback(
    (value: string) => {
      onChange("variant", value as "default" | "info" | "warning" | "success");
    },
    [onChange]
  );

  return (
    <div className="space-y-4 pt-4">
      <div className="space-y-2">
        <Label htmlFor="infoContent">Information Content</Label>
        <Textarea
          id="infoContent"
          value={component.infoContent}
          onChange={handleContentChange}
          placeholder="Enter informative text to help users complete the form"
          className="min-h-[120px]"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="variant">Style Variant</Label>
        <Select
          value={component.variant || "default"}
          onValueChange={handleVariantChange}
        >
          <SelectTrigger id="variant">
            <SelectValue placeholder="Select a style variant" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="default">Default</SelectItem>
            <SelectItem value="info">Info (Blue)</SelectItem>
            <SelectItem value="warning">Warning (Amber)</SelectItem>
            <SelectItem value="success">Success (Green)</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2 pt-4">
        <Label>Preview</Label>
        <div className="p-2 border rounded-md">
          <InfoTextInput
            id="preview"
            infoContent={component.infoContent}
            variant={component.variant}
          />
        </div>
      </div>
    </div>
  );
});

export default InfoTextConfigTabContent;
