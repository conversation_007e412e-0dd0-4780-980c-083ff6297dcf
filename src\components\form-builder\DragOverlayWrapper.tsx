import { DragOverlay } from "@dnd-kit/core";
import { Card, CardContent } from "@/components/ui/card";
import { FormComponent } from "@/lib/types/form";

interface DragOverlayWrapperProps {
  activeId: string | null;
  components: FormComponent[];
}

export default function DragOverlayWrapper({
  activeId,
  components,
}: DragOverlayWrapperProps) {
  if (!activeId) return null;

  return (
    <DragOverlay>
      <Card className="cursor-grabbing shadow-lg border-primary opacity-80">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium">
                {activeId.startsWith("palette-")
                  ? activeId
                      .replace("palette-", "")
                      .charAt(0)
                      .toUpperCase() +
                    activeId.replace("palette-", "").slice(1) +
                    " Field"
                  : components.find((c) => c.id === activeId)?.label}
              </h3>
              <p className="text-sm text-muted-foreground">
                {activeId.startsWith("palette-")
                  ? activeId.replace("palette-", "")
                  : `${
                      components.find((c) => c.id === activeId)?.type
                    } - ${
                      components.find((c) => c.id === activeId)?.name
                    }`}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </DragOverlay>
  );
}
