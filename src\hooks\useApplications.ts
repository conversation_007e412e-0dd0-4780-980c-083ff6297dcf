import { useState, useEffect, useCallback } from "react";
import { FormMeta } from "@/lib/types/form";
import { FormService } from "@/lib/services/form-service";
import { SubmissionService } from "@/lib/services/submission-service";
import { SubmissionStatus } from "@/lib/types/submission";
import { useAuth } from "@/contexts/AuthContext";

/**
 * Enhanced application data with submission status
 */
export interface EnhancedApplication extends FormMeta {
  submissionId?: string;
  submissionStatus?: SubmissionStatus;
}

/**
 * Custom hook for managing applications (active forms)
 */
export function useApplications() {
  const { user } = useAuth();
  const [applications, setApplications] = useState<EnhancedApplication[]>([]);
  const [filteredApplications, setFilteredApplications] = useState<
    EnhancedApplication[]
  >([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");

  // Helper function to find a draft submission for a form
  const findDraftSubmission = (formId: string, submissions: any[]) =>
    submissions.find((sub) => sub.formId === formId);

  // Helper function to enhance a form with submission data
  const enhanceFormWithSubmissionData = (
    form: FormMeta,
    draftSubmission: any
  ) => ({
    ...form,
    submissionId: draftSubmission?.id,
    submissionStatus: draftSubmission?.status as SubmissionStatus | undefined,
  });

  // Load applications (active forms) and user's draft submissions on component mount
  useEffect(() => {
    const loadApplications = async () => {
      setIsLoading(true);
      try {
        // Get only active forms as applications
        const formsData = await FormService.getForms("ACTIVE");

        // If user is logged in, get their draft submissions
        let userSubmissions: any[] = [];
        if (user) {
          userSubmissions = await SubmissionService.getSubmissions();
        }

        // Combine forms with submission data
        const enhancedApplications: EnhancedApplication[] = formsData.map(
          (form) => {
            const draftSubmission = findDraftSubmission(
              form.id,
              userSubmissions
            );
            return enhanceFormWithSubmissionData(form, draftSubmission);
          }
        );

        setApplications(enhancedApplications);
        setFilteredApplications(enhancedApplications);
      } catch (error) {
        console.error("Failed to load applications:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadApplications();
  }, [user]);

  // Handle search input changes
  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchQuery(e.target.value);
    },
    []
  );

  // Filter applications based on search query
  useEffect(() => {
    if (searchQuery.trim() === "") {
      setFilteredApplications(applications);
    } else {
      const query = searchQuery.toLowerCase();
      const filtered = applications.filter(
        (app) =>
          app.name.toLowerCase().includes(query) ||
          app.description?.toLowerCase().includes(query)
      );
      setFilteredApplications(filtered);
    }
  }, [searchQuery, applications]);

  return {
    applications,
    filteredApplications,
    isLoading,
    searchQuery,
    handleSearchChange,
  };
}
