# Enhanced DynamicPage Features

This document describes the new features added to the DynamicPage system to support URL parameters and custom dialog components.

## Overview

The enhanced DynamicPage system now supports:

1. **URL Parameter Support**: Extract parameters from URLs to filter entity lists and pass context data
2. **Custom Dialog Components**: Use custom React components in add/edit dialogs instead of standard forms
3. **Context Data Flow**: Seamlessly pass URL-derived data to forms and custom components

## URL Parameter Support

### Basic Usage

```typescript
// 1. Define parameter configuration
const paramConfig = createParameterConfig({
  required: ['projectId'],
  optional: ['category'],
  pathPattern: '/projects/:projectId/documents/:category?',
  validation: {
    projectId: parameterValidators.isUUID,
    category: parameterValidators.isAlphanumeric,
  }
});

// 2. Create parameterized page configuration
const documentsConfig = createParameterizedListPageConfig(
  'Document',
  paramConfig,
  undefined, // no custom dialogs
  (params) => ({
    // Transform URL params to context data
    parentProjectId: params.projectId,
    documentCategory: params.category || 'general',
  })
);

// 3. Use in page component
export default function ProjectDocumentsList() {
  return <DynamicPage config={documentsConfig} />;
}
```

### URL Parameter Configuration

```typescript
interface ParameterConfig {
  required?: string[];           // Parameters that must be present
  optional?: string[];           // Parameters that are optional
  pathPattern?: string;          // URL pattern for validation
  validation?: Record<string, (value: string) => boolean>; // Custom validators
}
```

### Built-in Validators

```typescript
import { parameterValidators } from '@/lib/config/entity-config-registry';

const validation = {
  id: parameterValidators.isUUID,
  count: parameterValidators.isNumeric,
  name: parameterValidators.isAlphanumeric,
  title: parameterValidators.minLength(3),
  description: parameterValidators.maxLength(500),
};
```

## Custom Dialog Components

### Basic Custom Dialog

```typescript
import { CustomDialogProps } from '@/lib/types/page-config';

interface CustomDocumentUploadProps extends CustomDialogProps {
  allowedTypes?: string[];
  maxFileSize?: number;
}

function CustomDocumentUploadDialog({
  onSuccess,
  onCancel,
  contextData,
  allowedTypes = ['pdf', 'docx'],
  maxFileSize = 10 * 1024 * 1024, // 10MB
}: CustomDocumentUploadProps) {
  const [file, setFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);

  const handleUpload = async () => {
    if (!file) return;
    
    setUploading(true);
    try {
      // Upload logic here
      const result = await uploadDocument(file, contextData);
      onSuccess?.(result);
    } catch (error) {
      console.error('Upload failed:', error);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="space-y-4">
      <h3>Upload Document</h3>
      <p>Project: {contextData?.parentProjectId}</p>
      
      <input
        type="file"
        accept={allowedTypes.map(type => `.${type}`).join(',')}
        onChange={(e) => setFile(e.target.files?.[0] || null)}
      />
      
      <div className="flex gap-2">
        <button onClick={handleUpload} disabled={!file || uploading}>
          {uploading ? 'Uploading...' : 'Upload'}
        </button>
        <button onClick={onCancel}>Cancel</button>
      </div>
    </div>
  );
}
```

### Using Custom Dialogs

```typescript
// Create configuration with custom dialogs
const customDialogs: CustomDialogsConfig = {
  create: {
    component: CustomDocumentUploadDialog,
    props: {
      allowedTypes: ['pdf', 'docx', 'txt'],
      maxFileSize: 20 * 1024 * 1024, // 20MB
    }
  },
  edit: {
    component: CustomDocumentEditDialog,
    props: {
      enableVersioning: true,
    }
  }
};

const documentsConfig = createCustomDialogListPageConfig(
  'Document',
  customDialogs
);
```

## Complete Example: Project Documents

Here's a complete example showing both URL parameters and custom dialogs:

```typescript
// pages/ProjectDocumentsList.tsx
import React from 'react';
import { DynamicPage } from '@/components/dynamic-page/DynamicPage';
import { 
  createParameterizedListPageConfig,
  createParameterConfig,
  parameterValidators 
} from '@/lib/config/entity-config-registry';
import { CustomDialogsConfig } from '@/lib/types/page-config';
import { CustomDocumentUploadDialog } from './CustomDocumentUploadDialog';

// Parameter configuration
const paramConfig = createParameterConfig({
  required: ['projectId'],
  optional: ['category'],
  validation: {
    projectId: parameterValidators.isUUID,
    category: parameterValidators.isAlphanumeric,
  }
});

// Custom dialogs configuration
const customDialogs: CustomDialogsConfig = {
  create: {
    component: CustomDocumentUploadDialog,
    props: {
      allowedTypes: ['pdf', 'docx', 'txt'],
      maxFileSize: 20 * 1024 * 1024,
    }
  }
};

// Context transformation
const contextTransform = (params: Record<string, string>) => ({
  parentProjectId: params.projectId,
  documentCategory: params.category || 'general',
  breadcrumb: `Project ${params.projectId} / Documents`,
});

export default function ProjectDocumentsList() {
  const config = createParameterizedListPageConfig(
    'Document',
    paramConfig,
    customDialogs,
    contextTransform
  );

  if (!config) {
    return <div>Configuration not found</div>;
  }

  return <DynamicPage config={config} />;
}
```

## Migration Guide

### From Basic DynamicPage

**Before:**
```typescript
export default function ProjectsList() {
  const [config] = useState(() => createListPageConfig("Project"));
  return <DynamicPage config={config} />;
}
```

**After (with URL parameters):**
```typescript
export default function UserProjectsList() {
  const paramConfig = createParameterConfig({
    required: ['userId'],
    validation: { userId: parameterValidators.isUUID }
  });
  
  const config = createParameterizedListPageConfig(
    'Project',
    paramConfig,
    undefined,
    (params) => ({ ownerId: params.userId })
  );

  return config ? <DynamicPage config={config} /> : null;
}
```

### Adding Custom Dialogs

**Before:**
```typescript
const config = createListPageConfig("Document");
```

**After:**
```typescript
const customDialogs = {
  create: {
    component: CustomDocumentUploadDialog,
    props: { allowedTypes: ['pdf'] }
  }
};

const config = createCustomDialogListPageConfig("Document", customDialogs);
```

## Best Practices

1. **Parameter Validation**: Always validate URL parameters to prevent errors
2. **Error Handling**: Custom dialogs should handle errors gracefully
3. **Context Data**: Use context transformation to provide meaningful data to components
4. **Type Safety**: Define proper TypeScript interfaces for custom dialog props
5. **Fallback Forms**: Provide fallback form configurations when possible

## Troubleshooting

### Common Issues

1. **Parameter validation errors**: Check that URL parameters match the configured pattern
2. **Custom dialog not rendering**: Ensure the component is properly exported and imported
3. **Context data not available**: Verify that contextTransform function is correctly implemented
4. **TypeScript errors**: Make sure custom dialog props extend CustomDialogProps

### Debug Tips

1. Use browser dev tools to inspect URL parameters
2. Add console.log statements in contextTransform functions
3. Check the DynamicPage component props in React dev tools
4. Verify entity configuration is properly registered
