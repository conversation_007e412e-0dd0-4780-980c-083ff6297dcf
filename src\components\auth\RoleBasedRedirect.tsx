import { useEffect, memo, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { Loading } from "@/components/ui/loading";
import { GlobalPermissions, NavigationItem } from "@/lib/types/api";
import { navConfig } from "@/hooks/useBackendNavigation";

/**
 * Component that redirects users based on their role
 * - Admin users are redirected to the FormListPage
 * - Applicant users are redirected to the ApplicationsPage
 * Optimized with React.memo and useCallback to prevent unnecessary re-renders
 */

function getFirstAccessibleHref(
  globalPermissions: GlobalPermissions | null,
  navConfig: Omit<NavigationItem, 'permissions'>[]
): string | null {
  if (!globalPermissions) return null;
  for (const navItem of navConfig) {
    const permission = globalPermissions[navItem.id]?.list;
    if (permission === "ENABLED") {
      return navItem.href;
    }
  }
  return null; // or "/" or fallback href
}
const RoleBasedRedirectComponent = () => {
  const { isLoading, hasPermission, globalPermissions } = useAuth();
  const navigate = useNavigate();

  // Memoized navigation handler
  const handleNavigation = useCallback(() => {
    if (!isLoading) {
      globalPermissions && console.log("🚀 ~ handleNavigation ~ globalPermissions:", globalPermissions)

      const href = getFirstAccessibleHref(globalPermissions, navConfig);
      if (href) {
        navigate(href, { replace: true });
        return;
      }
      navigate("/projects", { replace: true });
    }
  }, [isLoading, hasPermission, navigate]);

  useEffect(() => {
    handleNavigation();
  }, [handleNavigation]);

  // Show loading while checking authentication and redirecting
  return <Loading />;
};

// Memoized component to prevent unnecessary re-renders
const RoleBasedRedirect = memo(RoleBasedRedirectComponent);

export default RoleBasedRedirect;
