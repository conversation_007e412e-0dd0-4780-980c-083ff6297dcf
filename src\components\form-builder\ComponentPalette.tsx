import { FormComponentType } from "@/lib/schemas/form-schemas";

import { Card, CardContent } from "@/components/ui/card";
import {
  CalendarIcon,
  ClockIcon,
  TableIcon,
  TextIcon,
  HashIcon,
  ListIcon,
  CheckSquareIcon,
  CircleIcon,
  GripVertical,
  Layers,
  FolderOpen,
  InfoIcon,
} from "lucide-react";
import { useDraggable } from "@dnd-kit/core";

interface ComponentPaletteProps {
  onAddComponent: (type: FormComponentType) => void;
  // No need for useDndContext prop as it will always use the parent DndContext
}

interface ComponentOption {
  type: FormComponentType;
  label: string;
  icon: React.ReactNode;
  description: string;
}

// Draggable component for palette items
function DraggableComponentItem({
  option,
  onAddComponent,
}: {
  readonly option: ComponentOption;
  readonly onAddComponent: (type: FormComponentType) => void;
}) {
  const { attributes, listeners, setNodeRef, isDragging } = useDraggable({
    id: `palette-${option.type}`,
    data: {
      type: option.type,
      source: "palette",
    },
  });

  return (
    <Card
      ref={setNodeRef}
      className={`cursor-grab transition-colors hover:border-primary ${
        isDragging ? "opacity-50" : ""
      }`}
      onClick={() => onAddComponent(option.type)}
      {...listeners}
      {...attributes}
    >
      <CardContent className="flex items-center p-3">
        <div className="mr-3 flex h-9 w-9 items-center justify-center rounded-md bg-muted">
          {option.icon}
        </div>
        <div className="flex-1">
          <h4 className="text-sm font-medium">{option.label}</h4>
          <p className="text-xs text-muted-foreground">{option.description}</p>
        </div>
        <GripVertical className="h-4 w-4 text-muted-foreground flex-shrink-0" />
      </CardContent>
    </Card>
  );
}

export default function ComponentPalette({
  onAddComponent,
}: Readonly<ComponentPaletteProps>) {
  const componentOptions: ComponentOption[] = [
    // Layout components
    {
      type: "step",
      label: "Form Step",
      icon: <Layers className="h-5 w-5" />,
      description: "Add a new step for multi-step forms",
    },
    {
      type: "section",
      label: "Form Section",
      icon: <FolderOpen className="h-5 w-5" />,
      description: "Collapsible section to group related fields",
    },
    // Input components
    {
      type: "text",
      label: "Text Input",
      icon: <TextIcon className="h-5 w-5" />,
      description: "Single line text input field",
    },
    {
      type: "number",
      label: "Number Input",
      icon: <HashIcon className="h-5 w-5" />,
      description: "Numeric input field with optional constraints",
    },
    {
      type: "date",
      label: "Date Picker",
      icon: <CalendarIcon className="h-5 w-5" />,
      description: "Date selection field",
    },
    {
      type: "datetime",
      label: "Date & Time",
      icon: <ClockIcon className="h-5 w-5" />,
      description: "Date and time selection field",
    },
    {
      type: "select",
      label: "Dropdown",
      icon: <ListIcon className="h-5 w-5" />,
      description: "Dropdown selection with configurable options",
    },
    {
      type: "checkbox",
      label: "Checkbox",
      icon: <CheckSquareIcon className="h-5 w-5" />,
      description: "Single or multiple checkbox options",
    },
    {
      type: "radio",
      label: "Radio Buttons",
      icon: <CircleIcon className="h-5 w-5" />,
      description: "Mutually exclusive radio button options",
    },
    {
      type: "datagrid",
      label: "Data Grid",
      icon: <TableIcon className="h-5 w-5" />,
      description: "Excel-like grid for tabular data entry",
    },
    {
      type: "infoText",
      label: "Information Text",
      icon: <InfoIcon className="h-5 w-5" />,
      description: "Display informative text to guide users",
    },
  ];

  return (
    <div className="space-y-4 pb-4 h-full">
      <h3 className="text-sm font-medium">Available Components</h3>
      <div className="grid grid-cols-1 gap-2">
        {componentOptions.map((option) => (
          <DraggableComponentItem
            key={option.type}
            option={option}
            onAddComponent={onAddComponent}
          />
        ))}
      </div>
    </div>
  );
}
