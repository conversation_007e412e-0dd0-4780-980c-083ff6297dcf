import { useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, Link } from "react-router-dom";
import { FormProvider } from "react-hook-form";
import { useFormSubmission } from "@/hooks/useFormSubmission";
import { evaluateConditionalRendering } from "@/lib/utils/component-utils";
import { getComponentValidationRules } from "@/lib/utils/zod-validation-utils";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle, Loader2, Save } from "lucide-react";
import { Loading } from "@/components/ui/loading";
import { useToast } from "@/hooks/use-toast";
import RenderComponent from "@/components/form-builder/form-components/RenderComponent";
import StepProgress from "@/components/form-builder/form-components/StepProgress";
import StepNavigation from "@/components/form-builder/form-components/StepNavigation";
import { FormStateProvider, useFormState } from "@/contexts/FormStateContext";
import { useFormDataLoaderInitial } from "@/hooks/useFormDataLoader";


// Form Status Message Component
interface FormStatusMessageProps {
  isSubmitted: boolean;
  isValid: boolean;
  message: string;
}

const FormStatusMessage = ({
  isSubmitted,
  isValid,
  message,
}: FormStatusMessageProps) => {
  if (!isSubmitted || !message) return null;

  return (
    <Alert variant={isValid ? "default" : "destructive"}>
      <AlertCircle className="h-4 w-4" />
      <AlertDescription>{message}</AlertDescription>
    </Alert>
  );
};

export function FormSubmissionPageContent({ formId, projectRef }: Readonly<{
  formId?: string;
  projectRef?: string;
}>) {

  const { toast } = useToast();

  const {
    dispatch,
    state,
  } = useFormState();
  // Use form submission hook
  const {
    methods,
    isLoading,
    isSaving,
    isSubmitting,
    formStatus,
    steps,
    isMultiStep,
    currentStep,
    currentStepData,
    nextStep,
    prevStep,
    saveProgress,
    submitForm,
  } = useFormSubmission({
    formId: formId ?? "",
    dispatch,
    state,
    projectRef
  });

  // Extract methods from react-hook-form
  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    setValue,
    watch,
  } = methods;


  useEffect(() => {
    const subscription = watch(async (value, { name }) => {
      if (name && value[name]) {
        dispatch({
          type: "UPDATE_VALUE",
          payload: { name, value: value[name] },
        });
      }

    });
    return () => subscription.unsubscribe();
  }, [watch]);
  // Handle form submission with validation
  const onSubmit = async () => {
    const success = await submitForm();
    if (success) {
      toast({
        title: "Form submitted successfully",
        description: "Thank you for your submission!",
        variant: "success",
      });
    } else {
      toast({
        title: "Submission failed",
        description: "Please check the form for errors and try again.",
        variant: "destructive",
      });
    }
  };


  // Handle save progress with better user feedback
  const handleSaveProgress = async () => {
    // Show saving toast immediately for better user feedback
    toast({
      title: "Saving progress...",
      description: "Please wait while we save your progress.",
      variant: "default",
    });

    try {
      const success = await saveProgress();

      if (success) {
        toast({
          title: "Progress saved",
          description: "Your progress has been saved. You can continue later.",
          variant: "success",
        });
      } else {
        toast({
          title: "Save failed",
          description: "Failed to save your progress. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error in handleSaveProgress:", error);
      toast({
        title: "Save failed",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handle validation errors
  const onError = (errors: any) => {
    toast({
      title: "Validation errors",
      description: "Please fix the errors in the form before submitting.",
      variant: "destructive",
    });

    // Scroll to the first error
    const firstErrorKey = Object.keys(errors)[0];
    const firstErrorElement = document.getElementsByName(firstErrorKey)[0];
    if (firstErrorElement) {
      firstErrorElement.scrollIntoView({ behavior: "smooth", block: "center" });
    }
  };

  // Show loading state
  if (isLoading) {
    return <Loading message="Loading form..." />;
  }



  // Handle case when form is not found
  if (!state.form) {
    return (
      <div className="flex flex-col items-center justify-center py-10 text-center">
        <p className="text-muted-foreground">
          Form not found or you don't have permission to access it.
        </p>
        <Button asChild className="mt-4">
          <Link to="/projects">Back to Projects</Link>
        </Button>
      </div>
    );
  }


  return (

    <div className="space-y-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-3xl font-bold tracking-tight">{state.form.name}</h1>
      </div>

      <Card className="w-full">
        <CardHeader>
          <div className="flex items-start justify-between">
            <div>
              <CardTitle>{state.form.name}</CardTitle>
              {state.form.description && (
                <CardDescription>{state.form.description}</CardDescription>
              )}
            </div>
            <Button
              type="button"
              variant="outline"
              onClick={handleSaveProgress}
              disabled={isSaving}
              className="ml-4 self-start"
            >
              {isSaving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save Progress
                </>
              )}
            </Button>
          </div>
          {isMultiStep && (
            <StepProgress
              currentStep={currentStep}
              totalSteps={steps.length}
              stepLabel={currentStepData.label}
              stepDescription={currentStepData.description}
            />
          )}

        </CardHeader>

        <CardContent>
          <FormProvider {...methods}>
            <form
              onSubmit={(e) => e.preventDefault()}
              className="space-y-6"
            >
              <FormStatusMessage
                isSubmitted={formStatus.isSubmitted}
                isValid={formStatus.isValid}
                message={formStatus.message}
              />

              {currentStepData.components.map((component) => {
                // Check if this component should be rendered based on conditional rules
                const shouldRender = evaluateConditionalRendering(
                  component,
                  watch
                );

                if (!shouldRender) {
                  return null; // Skip rendering this component
                }

                return (
                  <div key={component.id} className="space-y-2">
                    <div className="space-y-1">
                      {component.type !== "step" &&
                        component.type !== "section" &&
                        component.type !== "infoText" && (
                          <Label
                            htmlFor={component.id}
                            className="flex items-center gap-1"
                          >
                            {component.label}
                            {component.required && (
                              <span className="text-destructive">*</span>
                            )}
                          </Label>
                        )}

                      <RenderComponent
                        component={component}
                        register={register}
                        control={control}
                        errors={errors}
                        setValue={setValue}
                        watch={watch}
                        validationRules={getComponentValidationRules(component)}
                        allComponents={state.form!.components}
                        mode="submission"
                        calculatedValues={state.calculatedValues}
                      />

                      {errors[component.name] && (
                        <div className="text-sm text-destructive flex items-center gap-1 mt-1">
                          <AlertCircle className="h-4 w-4" />
                          <span>
                            {errors[component.name]?.message as string}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}

              <StepNavigation
                currentStep={currentStep}
                totalSteps={steps.length}
                onNext={nextStep}
                onPrevious={prevStep}
                isMultiStep={isMultiStep}
                isLoading={isSubmitting}
                onSubmit={handleSubmit(onSubmit, onError)}
              />
            </form>
          </FormProvider>
        </CardContent>

        <CardFooter className="flex justify-center text-sm text-muted-foreground">
          <div>
            You can save your progress and navigate freely between steps. Validation only occurs when you submit the form.
          </div>
        </CardFooter>
      </Card>
    </div>



  );
}

export default function FormSubmissionPage() {

  const { formId, projectRef } = useParams<{
    formId?: string;
    projectRef?: string;
  }>();
  const { isLoading, form, submission } = useFormDataLoaderInitial({ formId, projectRef });
  if (isLoading) {
    return <Loading message="Loading form..." />;
  }
  if (!form) {
    return <Loading message="Loading form..." />;
  }
  if (!submission) {
    return <Loading message="Loading submission..." />;
  }



  return (
    <FormStateProvider initialForm={form} initialSubmission={submission}>
      <FormSubmissionPageContent formId={formId} projectRef={projectRef} />
    </FormStateProvider>
  )
}
