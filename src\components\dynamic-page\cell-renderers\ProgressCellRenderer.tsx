import React from "react";
import { Row } from "@tanstack/react-table";
import { ProgressBar } from "@/components/ui/formatters/progress-bar";
import { ColumnConfig } from "@/lib/types/page-config";

interface ProgressCellRendererProps {
  row: Row<unknown>;
  columnId: string;
  configColumn: ColumnConfig;
}

export const ProgressCellRenderer: React.FC<ProgressCellRendererProps> = ({
  row,
  columnId,
  configColumn,
}) => {
  const value = row.getValue(columnId);
  const numValue = Number(value);
  return value !== undefined ? (
    <ProgressBar
      progress={numValue}
      showLabel={configColumn.formatOptions?.showLabel ?? true}
      size={configColumn.formatOptions?.size ?? "md"}
      animated={configColumn.formatOptions?.animated ?? numValue < 100}
      labelPosition="right"
      variant={(configColumn.formatOptions?.variant as any) ?? "default"}
    />
  ) : null;
};
