# Docker Compose override file for development
# This file is automatically loaded by docker-compose and provides development-specific configurations

services:
  app:
    # Override for development
    environment:
      - NODE_ENV=development
      - CHOKIDAR_USEPOLLING=true  # Enable polling for file watching in Docker
    volumes:
      # Mount source code for hot reload
      - .:/app
      - /app/node_modules
    stdin_open: true
    tty: true

  # Development-only services can be added here
  # For example, a development database, redis, etc.
