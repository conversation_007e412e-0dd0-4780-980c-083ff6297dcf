import { useState, useMemo } from "react";
import { useForm, UseFormReturn } from "react-hook-form";
import { FormSchema, FormComponent } from "@/lib/schemas/form-schemas";
import {
  isStepComponent,
  getComponentValidationRules,
} from "@/lib/utils/zod-validation-utils";
import { useFormState } from "@/contexts/FormStateContext";
import { useCalculationEngine } from "./useCalculationEngine";
import { organizeComponentsIntoSteps } from "@/lib/utils/form-structure-utils";
import { evaluateConditionalRendering } from "@/lib/utils/component-utils";
import { validateAllCells } from "./useDataGridValidation";
import { indicesToExcel } from "@/lib/utils/grid-utils";

interface UseFormPreviewReturn {
  methods: UseFormReturn;
  formStatus: {
    isSubmitted: boolean;
    isValid: boolean;
    message: string;
  };
  setFormStatus: React.Dispatch<
    React.SetStateAction<{
      isSubmitted: boolean;
      isValid: boolean;
      message: string;
    }>
  >;
  onSubmit: (
    data: Record<string, any>
  ) => Promise<{ isValid: boolean; errors: any }>;
  steps: {
    id: string;
    label: string;
    description?: string;
    icon?: string;
    components: FormComponent[];
  }[];
  isMultiStep: boolean;
}

/**
 * Custom hook for form preview functionality
 */
export function useFormPreview(schema: FormSchema): UseFormPreviewReturn {
  const methods = useForm({
    mode: "onSubmit",
    criteriaMode: "all",
  });

  const [formStatus, setFormStatus] = useState<{
    isSubmitted: boolean;
    isValid: boolean;
    message: string;
  }>({
    isSubmitted: false,
    isValid: false,
    message: "",
  });

  const { state } = useFormState();
  const { evaluateValidationExpression } = useCalculationEngine(
    schema,
    methods.getValues()
  );

  const steps = useMemo(() => {
    const stepComponents = schema.components.filter(
      (component) => isStepComponent(component) && !component.parentId
    );

    if (stepComponents.length === 0) {
      const topLevelComponents = schema.components.filter(
        (component) => !component.parentId
      );
      return [
        {
          id: "default-step",
          label: "Form",
          description: "Default form",
          components: topLevelComponents,
        },
      ];
    }

    return stepComponents.map((step) => {
      const directChildren = schema.components.filter(
        (component) => component.parentId === step.id
      );
      if (isStepComponent(step)) {
        return {
          id: step.id,
          label: step.label,
          description: step.description,
          icon: step.icon,
          components: directChildren,
        };
      }
      return {
        id: step.id,
        label: step.label,
        components: directChildren,
      };
    });
  }, [schema.components]);

  const isMultiStep = steps.length > 1;

  const convertStructuredToFlat = (
    structuredValue: any,
    component?: any
  ): Record<string, any> => {
    const flatValue: Record<string, any> = {};
    if (!structuredValue.rows || !Array.isArray(structuredValue.rows)) {
      return flatValue;
    }
    const columnHeaders = structuredValue.metadata?.columnHeaders || [];
    structuredValue.rows.forEach((row: any, rowIndex: number) => {
      if (row.cells && typeof row.cells === "object") {
        Object.keys(row.cells).forEach((columnHeader) => {
          const cell = row.cells[columnHeader];
          const colIndex = columnHeaders.indexOf(columnHeader);
          if (colIndex >= 0) {
            // Convert to Excel-style cell ID first
            const excelCellId = indicesToExcel(rowIndex + 1, colIndex + 1);

            // Get custom cell ID if component is provided, otherwise use Excel coordinate
            let cellId = excelCellId;
            if (component?.cells?.[excelCellId]) {
              cellId = component.cells[excelCellId].id || excelCellId;
            }

            flatValue[cellId] = cell.value || "";
          }
        });
      }
    });
    return flatValue;
  };

  const validateField = (
    component: any,
    value: any
  ): { isValid: boolean; error?: string } => {
    try {
      if (component.type === "datagrid" && value) {
        const flatValue =
          value?.flat || convertStructuredToFlat(value, component);
        const errors = validateAllCells(
          component,
          flatValue,
          methods.getValues(),
          evaluateValidationExpression
        );
        const isValid = Object.keys(errors).length === 0;
        return {
          isValid,
          error: isValid
            ? undefined
            : Object.entries(errors)
                .map(([key, message]) => `Cell ${key}: ${message}`)
                .join(", "),
        };
      }

      const validationRules = getComponentValidationRules(component);

      if (
        validationRules.required &&
        (value === undefined || value === null || value === "")
      ) {
        return {
          isValid: false,
          error: `${component.label || component.name} is required`,
        };
      }

      if (validationRules.pattern && value) {
        if (
          validationRules.pattern.message &&
          validationRules.pattern.message.toLowerCase().includes("email")
        ) {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(value)) {
            return {
              isValid: false,
              error:
                validationRules.pattern.message ||
                "Please enter a valid email address",
            };
          }
        } else {
          if (!validationRules.pattern.value.test(value)) {
            return {
              isValid: false,
              error:
                validationRules.pattern.message ||
                `${component.label || component.name} format is invalid`,
            };
          }
        }
      }

      if (
        validationRules.minLength &&
        value &&
        value.length < validationRules.minLength.value
      ) {
        return {
          isValid: false,
          error:
            validationRules.minLength.message ||
            `${component.label || component.name} must be at least ${
              validationRules.minLength.value
            } characters`,
        };
      }

      if (
        validationRules.maxLength &&
        value &&
        value.length > validationRules.maxLength.value
      ) {
        return {
          isValid: false,
          error:
            validationRules.maxLength.message ||
            `${component.label || component.name} must be no more than ${
              validationRules.maxLength.value
            } characters`,
        };
      }

      if (
        validationRules.min &&
        value !== undefined &&
        value !== null &&
        Number(value) < validationRules.min.value
      ) {
        return {
          isValid: false,
          error:
            validationRules.min.message ||
            `${component.label || component.name} must be at least ${
              validationRules.min.value
            }`,
        };
      }

      if (
        validationRules.max &&
        value !== undefined &&
        value !== null &&
        Number(value) > validationRules.max.value
      ) {
        return {
          isValid: false,
          error:
            validationRules.max.message ||
            `${component.label || component.name} must be no more than ${
              validationRules.max.value
            }`,
        };
      }

      return { isValid: true };
    } catch (error) {
      console.warn(`Error validating field ${component.name}:`, error);
      return { isValid: true };
    }
  };

  const validateAllSteps = async (): Promise<{
    isValid: boolean;
    errors: any;
  }> => {
    if (!state.form) return { isValid: true, errors: {} };

    const steps = organizeComponentsIntoSteps(state.form);
    const allErrors: any = {};
    const formValues = methods.getValues();
    const { watch } = methods;

    for (const step of steps) {
      const allStepComponents = [...step.components];
      step.components
        .filter((component) => component.type === "section")
        .forEach((section) => {
          const childComponents = state.form!.components.filter(
            (c) => c.parentId === section.id
          );
          allStepComponents.push(...childComponents);
        });

      for (const component of allStepComponents) {
        if (
          component.type === "step" ||
          component.type === "section" ||
          !component.name
        ) {
          continue;
        }

        try {
          const shouldRender = evaluateConditionalRendering(component, watch);
          if (!shouldRender) {
            continue;
          }
        } catch (error) {
          console.warn(
            `Error evaluating conditional rendering for ${component.name}:`,
            error
          );
        }

        const fieldValue = formValues[component.name];
        const validationResult = validateField(component, fieldValue);

        if (!validationResult.isValid) {
          allErrors[component.name] = {
            type: "manual",
            message: validationResult.error,
          };
        }
      }
    }

    return {
      isValid: Object.keys(allErrors).length === 0,
      errors: allErrors,
    };
  };

  const onSubmit = async (_data: Record<string, any>) => {
    const validationResult = await validateAllSteps();

    if (!validationResult.isValid) {
      const errorCount = Object.keys(validationResult.errors).length;
      Object.keys(validationResult.errors).forEach((fieldName) => {
        methods.setError(fieldName, validationResult.errors[fieldName]);
      });
      setFormStatus({
        isSubmitted: true,
        isValid: false,
        message: `Please fix ${errorCount} validation error${
          errorCount > 1 ? "s" : ""
        } before submitting.`,
      });
    } else {
      setFormStatus({
        isSubmitted: true,
        isValid: true,
        message: "Form submitted successfully!",
      });
    }
    return validationResult;
  };

  return {
    methods,
    formStatus,
    setFormStatus,
    onSubmit,
    steps,
    isMultiStep,
  };
}
