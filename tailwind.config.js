/** @type {import('tailwindcss').Config} */
export default {
  darkMode: "class",
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        "desnz-blue": "#003479",
        "desnz-navy": "#182D4C",
        "desnz-cyan": "#33DDEC",
        "desnz-sky": "#62C3F4",
        warning: {
          DEFAULT: "var(--warning)",
          foreground: "var(--warning-foreground)",
        },
        info: {
          DEFAULT: "var(--info)",
          foreground: "var(--info-foreground)",
        },
        success: {
          DEFAULT: "#10b981",
          foreground: "#ffffff",
        },
        schema: {
          DEFAULT: "#36b23d",
          foreground: "#ffffff",
        },
      },
      fontFamily: {
        sans: ["Proxima Nova", "Arial", "sans-serif"],
      },
      lineHeight: {
        body: "1.25rem",
      },
    },
  },
  plugins: [require("@tailwindcss/vite"), require("tw-animate-css")],
};
