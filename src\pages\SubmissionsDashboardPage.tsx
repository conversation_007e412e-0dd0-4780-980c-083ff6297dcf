import { Link } from "react-router-dom";
import { useSubmissions } from "@/hooks/useSubmissions";
import { ApplicationStatus, SubmissionStatus } from "@/lib/types/submission";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { CustomBadge } from "@/components/ui/custom-badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Search, Calendar, FileText, RefreshCw } from "lucide-react";

// Format date for display
const formatDate = (dateString?: string) => {
  if (!dateString) return "N/A";
  const date = new Date(dateString);
  return date.toLocaleDateString("en-GB", {
    day: "numeric",
    month: "short",
    year: "numeric",
  });
};

// Get badge variant based on submission status
const getStatusBadgeVariant = (status: SubmissionStatus | ApplicationStatus) => {
  switch (status) {
    case "draft":
      return "outline";
    case "DRAFT":
      return "outline";
    case "SUBMITTED":
      return "default";
    case "submitted":
      return "default";
    case "approved":
      return "success";
    case "rejected":
      return "destructive";
    case "returned":
      return "warning";
    default:
      return "outline";
  }
};

// Submission Card Component
const SubmissionCard = ({ submission }: { submission: any }) => {
  return (
    <Card className="transition-all hover:shadow-md">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="truncate">{submission.formName}</CardTitle>
          <CustomBadge variant={getStatusBadgeVariant(submission.status)}>
            {submission.status.charAt(0).toUpperCase() + submission.status.slice(1)}
          </CustomBadge>
        </div>
        <CardDescription>Applicant: {submission.applicantName}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="flex items-center text-sm">
            <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
            <span>Created: {formatDate(submission.createdAt)}</span>
          </div>
          <div className="flex items-center text-sm">
            <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
            <span>Updated: {formatDate(submission.updatedAt)}</span>
          </div>
          {submission.submittedAt && (
            <div className="flex items-center text-sm">
              <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
              <span>Submitted: {formatDate(submission.submittedAt)}</span>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter>
        <Button asChild variant="outline" className="w-full">
          <Link to={`/submissions/${submission.formId}`}>View Submission</Link>
        </Button>
      </CardFooter>
    </Card>
  );
};

// Search and Filter Bar Component
interface SearchAndFilterBarProps {
  searchQuery: string;
  onSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  statusFilter: ApplicationStatus | "all";
  onStatusFilterChange: (status: ApplicationStatus | "all") => void;
  onRefresh: () => void;
  isLoading: boolean;
}

const SearchAndFilterBar = ({
  searchQuery,
  onSearchChange,
  statusFilter,
  onStatusFilterChange,
  onRefresh,
  isLoading,
}: SearchAndFilterBarProps) => {
  return (
    <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
      <div className="relative flex-1">
        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          type="search"
          placeholder="Search submissions..."
          className="pl-8"
          value={searchQuery}
          onChange={onSearchChange}
        />
      </div>
      <Tabs
        defaultValue={statusFilter}
        className="w-full sm:w-auto"
        onValueChange={(value) => onStatusFilterChange(value as ApplicationStatus | "all")}
      >
        <TabsList className="grid w-full grid-cols-3 sm:grid-cols-6">
          <TabsTrigger value="all">All</TabsTrigger>
          <TabsTrigger value="DRAFT">Draft</TabsTrigger>
          <TabsTrigger value="SUBMITTED">Submitted</TabsTrigger>
          {/* <TabsTrigger value="approved">Approved</TabsTrigger>
          <TabsTrigger value="rejected">Rejected</TabsTrigger>
          <TabsTrigger value="returned">Returned</TabsTrigger> */}
        </TabsList>
      </Tabs>
      <Button
        variant="outline"
        size="icon"
        onClick={onRefresh}
        disabled={isLoading}
      >
        <RefreshCw className="h-4 w-4" />
      </Button>
    </div>
  );
};

// Submissions List Component
interface SubmissionsListProps {
  submissions: any[];
  isLoading: boolean;
  searchQuery: string;
}

const SubmissionsList = ({
  submissions,
  isLoading,
  searchQuery,
}: SubmissionsListProps) => {
  if (isLoading) {
    return <SubmissionsSkeleton />;
  }

  if (submissions.length === 0) {
    return <EmptyState searchQuery={searchQuery} />;
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {submissions.map((submission) => (
        <SubmissionCard key={submission.id} submission={submission} />
      ))}
    </div>
  );
};

// Loading Skeleton Component
const SubmissionsSkeleton = () => (
  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
    {[1, 2, 3].map((i) => (
      <Card key={i} className="overflow-hidden">
        <CardHeader className="gap-2">
          <Skeleton className="h-5 w-1/2" />
          <Skeleton className="h-4 w-full" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-24 w-full" />
        </CardContent>
        <CardFooter>
          <Skeleton className="h-4 w-1/3" />
        </CardFooter>
      </Card>
    ))}
  </div>
);

// Empty State Component
interface EmptyStateProps {
  searchQuery: string;
}

const EmptyState = ({ searchQuery }: EmptyStateProps) => (
  <Card className="flex flex-col items-center justify-center p-6 text-center">
    <div className="mb-4 rounded-full bg-muted p-3">
      <FileText className="h-6 w-6 text-muted-foreground" />
    </div>
    <CardTitle className="mb-2">No submissions found</CardTitle>
    <CardDescription>
      {searchQuery
        ? `No submissions match "${searchQuery}"`
        : "There are no submissions available with the selected filters."}
    </CardDescription>
  </Card>
);

// Main Page Component
export default function SubmissionsDashboardPage() {
  // Use our custom hook for submissions data and filtering
  const {
    filteredSubmissions,
    isLoading,
    searchQuery,
    statusFilter,
    handleSearchChange,
    handleStatusFilterChange,
    refreshSubmissions,
  } = useSubmissions();

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Submissions</h1>
      </div>

      <SearchAndFilterBar
        searchQuery={searchQuery}
        onSearchChange={handleSearchChange}
        statusFilter={statusFilter}
        onStatusFilterChange={handleStatusFilterChange}
        onRefresh={refreshSubmissions}
        isLoading={isLoading}
      />

      <SubmissionsList
        submissions={filteredSubmissions}
        isLoading={isLoading}
        searchQuery={searchQuery}
      />
    </div>
  );
}
