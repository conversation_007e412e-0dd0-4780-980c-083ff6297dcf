import { SortingState } from "@tanstack/react-table";

// Define types for our table data
export interface Person extends Record<string, unknown> {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  age: number;
  status: "active" | "inactive" | "pending";
  role: string;
  visits: number;
  progress: number;
  createdAt: string;
}

// Define types for pagination
export interface PaginationState {
  pageIndex: number;
  pageSize: number;
}

// Define types for filtering
export interface ColumnFilter {
  id: string;
  value: string;
}

export interface TableFilter {
  globalFilter: string;
  columnFilters: ColumnFilter[];
}

// Define types for server response
export interface TableData<T> {
  data: T[];
  pageCount: number;
  totalCount: number;
}

// Generate a large dataset of mock data
const generateMockData = (count: number): Person[] => {
  const roles = ["Admin", "User", "Manager", "Developer", "Designer"];
  const statuses: Array<"active" | "inactive" | "pending"> = [
    "active",
    "inactive",
    "pending",
  ];

  return Array.from({ length: count }).map((_, index) => {
    const id = `P${(index + 1).toString().padStart(3, "0")}`;
    const firstName = `First${index + 1}`;
    const lastName = `Last${index + 1}`;
    const email = `person${index + 1}@example.com`;
    const age = Math.floor(Math.random() * 40) + 20; // 20-60
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const role = roles[Math.floor(Math.random() * roles.length)];
    const visits = Math.floor(Math.random() * 100);
    const progress = Math.floor(Math.random() * 100);
    const createdAt = new Date(
      Date.now() - Math.floor(Math.random() * 1000 * 60 * 60 * 24 * 365)
    ).toISOString();

    return {
      id,
      firstName,
      lastName,
      email,
      age,
      status,
      role,
      visits,
      progress,
      createdAt,
    };
  });
};

// Create a large dataset
const mockPersons = generateMockData(1000);

// Simulate API delay
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

// Helper function to sort data
const sortData = <T>(data: T[], sorting: SortingState): T[] => {
  if (!sorting.length) return data;

  return [...data].sort((a, b) => {
    for (const sort of sorting) {
      const { id, desc } = sort;
      const aValue = (a as any)[id];
      const bValue = (b as any)[id];

      // Handle different data types
      if (typeof aValue === "string" && typeof bValue === "string") {
        const comparison = aValue.localeCompare(bValue);
        if (comparison !== 0) {
          return desc ? -comparison : comparison;
        }
      } else if (typeof aValue === "number" && typeof bValue === "number") {
        if (aValue !== bValue) {
          return desc ? bValue - aValue : aValue - bValue;
        }
      } else if (aValue instanceof Date && bValue instanceof Date) {
        const timeA = aValue.getTime();
        const timeB = bValue.getTime();
        if (timeA !== timeB) {
          return desc ? timeB - timeA : timeA - timeB;
        }
      } else {
        // Fallback for other types
        const valueA = String(aValue);
        const valueB = String(bValue);
        const comparison = valueA.localeCompare(valueB);
        if (comparison !== 0) {
          return desc ? -comparison : comparison;
        }
      }
    }
    return 0;
  });
};

// Helper function to filter data
const filterData = <T extends Record<string, unknown>>(
  data: T[],
  filter: TableFilter
): T[] => {
  let filtered = [...data];

  // Apply global filter
  if (filter.globalFilter) {
    const searchTerm = filter.globalFilter.toLowerCase();
    filtered = filtered.filter((item) => {
      return Object.values(item).some((value) => {
        if (value === null || value === undefined) return false;
        return String(value).toLowerCase().includes(searchTerm);
      });
    });
  }

  // Apply column filters
  if (filter.columnFilters.length) {
    filter.columnFilters.forEach((columnFilter) => {
      if (columnFilter.value) {
        const searchTerm = columnFilter.value.toLowerCase();
        filtered = filtered.filter((item) => {
          const value = (item as any)[columnFilter.id];
          if (value === null || value === undefined) return false;
          return String(value).toLowerCase().includes(searchTerm);
        });
      }
    });
  }

  return filtered;
};

// Mock table service
export const TableService = {
  // Get paginated, sorted, and filtered data
  getTableData: async <T extends Record<string, unknown>>(
    data: T[],
    pagination: PaginationState,
    sorting: SortingState,
    filter: TableFilter
  ): Promise<TableData<T>> => {
    await delay(500); // Simulate network delay

    // Apply filters first
    let filteredData = filterData(data, filter);

    // Then sort the filtered data
    const sortedData = sortData(filteredData, sorting);

    // Calculate pagination
    const { pageIndex, pageSize } = pagination;
    const start = pageIndex * pageSize;
    const end = start + pageSize;
    const paginatedData = sortedData.slice(start, end);

    return {
      data: paginatedData,
      pageCount: Math.ceil(filteredData.length / pageSize),
      totalCount: filteredData.length,
    };
  },

  // Get person data
  getPersons: async (
    pagination: PaginationState,
    sorting: SortingState,
    filter: TableFilter
  ): Promise<TableData<Person>> => {
    return TableService.getTableData<Person>(
      mockPersons,
      pagination,
      sorting,
      filter
    );
  },
};
