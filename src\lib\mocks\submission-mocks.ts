import { SubmissionMeta, SubmissionStatus } from "@/lib/types/submission";

/**
 * Mock submissions data for development
 */
export const mockSubmissions: SubmissionMeta[] = [
  {
    id: "submission-1",
    formId: "form-1",
    formName: "Project Application Form",
    applicantId: "user-1",
    applicantName: "<PERSON>",
    status: "submitted" as SubmissionStatus,
    createdAt: "2023-02-10T09:30:00Z",
    updatedAt: "2023-02-10T10:45:00Z",
    submittedAt: "2023-02-10T10:45:00Z",
  },
  {
    id: "submission-2",
    formId: "form-1",
    formName: "Project Application Form",
    applicantId: "user-2",
    applicantName: "<PERSON>",
    status: "draft" as SubmissionStatus,
    createdAt: "2023-02-15T14:20:00Z",
    updatedAt: "2023-02-15T15:10:00Z",
    submittedAt: undefined,
  },
  {
    id: "submission-3",
    formId: "form-2",
    formName: "Funding Request Form",
    applicantId: "user-1",
    applicantName: "<PERSON>",
    status: "submitted" as SubmissionStatus,
    createdAt: "2023-03-20T11:00:00Z",
    updatedAt: "2023-03-20T11:30:00Z",
    submittedAt: "2023-03-20T11:30:00Z",
  },
  {
    id: "submission-4",
    formId: "form-2",
    formName: "Funding Request Form",
    applicantId: "user-3",
    applicantName: "Alice Johnson",
    status: "draft" as SubmissionStatus,
    createdAt: "2023-03-25T16:45:00Z",
    updatedAt: "2023-03-25T17:20:00Z",
    submittedAt: undefined,
  },
];
