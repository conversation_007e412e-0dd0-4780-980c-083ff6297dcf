# Docker Setup Guide

This guide explains how to run the HNES Form Builder application using <PERSON><PERSON> and Docker Compose.

## Prerequisites

- Docker (version 20.10 or higher)
- Docker Compose (version 2.0 or higher)

## Quick Start

### Development Mode

1. **Clone the repository and navigate to the project directory**

2. **Copy environment file**

   ```bash
   cp .env.docker .env.local
   ```

3. **Start the development environment**

   ```bash
   docker-compose up app
   ```

   This will start:

   - Frontend application accessible on http://localhost:3000
   - The app will connect to your existing backend on port 8080
   - Hot reload is enabled for development

4. **Access the application**
   - Frontend: http://localhost:3000 (accessible from your host machine)
   - Your existing backend: http://localhost:8080
   - Cognito authentication will redirect to http://localhost:3000/callback

### Production Mode

1. **Build and run production containers**

   ```bash
   docker-compose --profile production up app-prod
   ```

2. **Access the application**
   - Frontend: http://localhost:80

## Available Services

### Frontend Application (`app`)

- **Development**: Hot reload enabled with volume mounts
- **Port**: 3000
- **Target**: React development server with Vite
- **Backend Connection**: Connects to host backend via `host.docker.internal:8080`

### Mock API (`api-mock`)

- **Purpose**: Optional mock API endpoints for development (when backend is not available)
- **Port**: 8081
- **Configuration**: nginx serving static JSON files
- **Usage**: Start with `docker-compose --profile mock-api up api-mock`

### Production Frontend (`app-prod`)

- **Purpose**: Production-optimized build served by nginx
- **Port**: 80
- **Features**: Gzipped assets, caching headers, security headers
- **Backend Connection**: Connects to host backend via `host.docker.internal:8080`

## Environment Variables

The application uses the following environment variables:

### API Configuration

- `VITE_API_URL`: Backend API URL
- `VITE_API_MODE`: API mode (mock/real)
- `VITE_API_BASE_URL`: API base path

### AWS Cognito Configuration

- `VITE_COGNITO_HOSTED_UI_URL`: Cognito hosted UI URL
- `VITE_COGNITO_CLIENT_ID`: Cognito client ID
- `VITE_COGNITO_USER_POOL_ID`: Cognito user pool ID
- `VITE_COGNITO_REGION`: AWS region
- `VITE_COGNITO_REDIRECT_URI`: OAuth redirect URI

## Docker Commands

### Development Commands

```bash
# Start frontend only (connects to your existing backend)
docker-compose up app

# Start in detached mode
docker-compose up -d app

# Start with mock API (if you don't have backend running)
docker-compose --profile mock-api up

# View logs
docker-compose logs -f

# Stop services
docker-compose down

# Rebuild containers
docker-compose up --build app

# Run specific service
docker-compose up app
```

### Production Commands

```bash
# Build production image
docker-compose build app-prod

# Run production container
docker-compose --profile production up app-prod

# Run in detached mode
docker-compose --profile production up -d app-prod
```

### Utility Commands

```bash
# Execute shell in running container
docker-compose exec app sh

# View container logs
docker-compose logs app

# Remove all containers and volumes
docker-compose down -v

# Remove all images
docker-compose down --rmi all
```

## File Structure

```
├── Dockerfile                 # Multi-stage Docker build
├── docker-compose.yml         # Main compose configuration
├── docker-compose.override.yml # Development overrides
├── .dockerignore              # Docker build context exclusions
├── nginx.conf                 # Production nginx configuration
├── docker-entrypoint.sh       # Runtime environment injection
├── env.template.js            # Environment template
├── .env.docker               # Docker environment template
└── docker/
    ├── nginx-api.conf         # Mock API nginx config
    └── api-mock/
        └── health.json        # Mock API health endpoint
```

## Troubleshooting

### Common Issues

1. **Port conflicts**

   ```bash
   # Check if ports are in use
   netstat -tulpn | grep :3000
   netstat -tulpn | grep :8080
   ```

2. **Permission issues on Windows**

   - Ensure Docker Desktop is running
   - Check file sharing settings in Docker Desktop

3. **Hot reload not working**

   - Ensure `CHOKIDAR_USEPOLLING=true` is set in development
   - Check volume mounts in docker-compose.override.yml

4. **Environment variables not loading**

   - Verify .env.local file exists and has correct values
   - Check docker-compose.yml environment section

5. **Cannot access app on localhost:3000**

   - Ensure Vite is configured with `host: true` in vite.config.ts
   - Check that port 3000 is properly mapped in docker-compose.yml
   - Verify the container is running: `docker-compose ps`

6. **Cognito authentication redirect issues**
   - Ensure `VITE_COGNITO_REDIRECT_URI=http://localhost:3000/callback` in .env.local
   - The app must be accessible on localhost:3000 for Cognito redirects to work

### Debugging

```bash
# Check container status
docker-compose ps

# View detailed logs
docker-compose logs --tail=100 app

# Inspect container
docker-compose exec app sh
```

## Performance Optimization

### Development

- Use volume mounts for hot reload
- Enable polling for file watching
- Use override files for dev-specific config

### Production

- Multi-stage builds for smaller images
- nginx for serving static files
- Gzip compression enabled
- Asset caching configured
- Security headers included

## Security Considerations

- Environment variables are injected at runtime
- Security headers configured in nginx
- CORS properly configured for API
- No sensitive data in Docker images
- Use .dockerignore to exclude unnecessary files
