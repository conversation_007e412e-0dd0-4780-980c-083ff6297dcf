import { memo, useRef, useEffect, useCallback } from "react";
import { useVirtualizer } from "@tanstack/react-virtual";
import { TableCell, TableRow } from "@/components/ui/table";
import GridCellInput from "./GridCellInput";
import {
  indicesToExcel,
  getCellDisplayValue,
  isCellHeader,
  evaluateRowConditionalRendering,
} from "@/lib/utils/grid-utils";
import { GridRowConditionalRendering } from "@/lib/schemas/form-schemas";

// Import VirtualItem type from TanStack Virtual
import type { VirtualItem as TanStackVirtualItem } from "@tanstack/react-virtual";

interface VirtualizedGridProps {
  rows: number;
  columns: number;
  cells: Record<string, any>;
  getCellValue: (rowIndex: number, colIndex: number) => string;
  getCellError: (rowIndex: number, colIndex: number) => string | undefined;
  onCellChange: (rowIndex: number, colIndex: number, value: string) => void;
  rowHeight?: number;
  headerHeight?: number;
  conditionalRows?: GridRowConditionalRendering[];
  mode?: "edit" | "preview" | "submission";
  calculatedValues: Record<string, any>;
}

/**
 * A virtualized grid component for efficient rendering of large data grids
 */
function VirtualizedGrid({
  rows,
  columns,
  cells,
  getCellValue,
  getCellError,
  onCellChange,
  rowHeight = 40,
  headerHeight = 40,
  conditionalRows,
  mode = "edit",
  calculatedValues,
}: Readonly<VirtualizedGridProps>) {
  const parentRef = useRef<HTMLDivElement>(null);

  // Update parent width on resize
  useEffect(() => {
    if (!parentRef.current) return;

    // Add resize observer
    const resizeObserver = new ResizeObserver(() => {
      // This is just to trigger a re-render when the parent size changes
    });
    resizeObserver.observe(parentRef.current);

    return () => {
      if (parentRef.current) {
        resizeObserver.unobserve(parentRef.current);
      }
    };
  }, []);

  // Skip the header row (row 0)
  const dataRows = rows - 1;

  // Create row virtualizer
  const rowVirtualizer = useVirtualizer({
    count: dataRows,
    getScrollElement: () => parentRef.current,
    estimateSize: () => rowHeight,
    overscan: 5,
  });

  // Create column virtualizer (skip the first column for row headers)
  const columnVirtualizer = useVirtualizer({
    horizontal: true,
    count: columns - 1,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 120, // Estimate column width
    overscan: 2,
  });

  // Render a virtualized cell
  const renderVirtualCell = useCallback(
    (rowIndex: number, colIndex: number) => {
      // Adjust indices (add 1 to rowIndex for header row, add 1 to colIndex for row header column)
      const actualRowIndex = rowIndex + 1;
      const actualColIndex = colIndex + 1;

      const isHeader = isCellHeader(cells, actualRowIndex, actualColIndex);
      const cellCoord = indicesToExcel(actualRowIndex, actualColIndex);

      return (
        <TableCell
          key={`cell-${cellCoord}`}
          className={`p-1 ${isHeader ? "bg-muted/30" : ""
            } border-b border-r hover:bg-muted/10`}
          style={{
            height: rowHeight,
            width: 120, // Fixed width for simplicity
          }}
        >
          <GridCellInput
            rowIndex={actualRowIndex}
            colIndex={actualColIndex}
            cellCoord={cellCoord}
            value={getCellValue(actualRowIndex, actualColIndex)}
            error={getCellError(actualRowIndex, actualColIndex)}
            cells={cells}
            onChange={onCellChange}
            calculatedValues={calculatedValues}
          />
        </TableCell>
      );
    },
    [cells, getCellValue, getCellError, onCellChange, rowHeight]
  );

  // Render header row
  const renderHeaderRow = useCallback(() => {
    return (
      <TableRow
        style={{
          height: headerHeight,
          width: "100%",
          position: "sticky",
          top: 0,
          zIndex: 1,
          backgroundColor: "var(--background)",
          boxShadow: "0 1px 2px rgba(0, 0, 0, 0.05)",
        }}
      >
        {/* Row header column */}
        <TableCell
          className="font-medium bg-muted/70 sticky left-0 z-10 border-b border-r"
          style={{
            height: headerHeight,
            width: 60,
          }}
        >
          {/* Empty corner cell */}
        </TableCell>

        {/* Column headers */}
        {columnVirtualizer
          .getVirtualItems()
          .map((virtualColumn: TanStackVirtualItem) => {
            const colIndex = virtualColumn.index + 1; // Add 1 for row header column
            const headerText =
              getCellDisplayValue(cells, 0, colIndex) ||
              String.fromCharCode(65 + colIndex);
            const cellCoord = indicesToExcel(0, colIndex);

            return (
              <TableCell
                key={`header-${cellCoord}`}
                className="font-bold bg-muted/70 border-b"
                style={{
                  height: headerHeight,
                  width: virtualColumn.size,
                  transform: `translateX(${virtualColumn.start}px)`,
                  position: "absolute",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  padding: "0 8px",
                }}
              >
                {headerText}
              </TableCell>
            );
          })}
      </TableRow>
    );
  }, [cells, columnVirtualizer, headerHeight]);

  // Calculate a responsive height based on the number of rows
  // Min height of 200px, max height of 500px
  // For each row, add 40px (rowHeight) up to the max
  const calculatedHeight = Math.min(
    Math.max(200, (rows - 1) * rowHeight + headerHeight),
    500
  );

  return (
    <div
      ref={parentRef}
      className="overflow-auto border rounded-md shadow-sm"
      style={{
        height: calculatedHeight, // Responsive height based on number of rows
        width: "100%",
        position: "relative",
      }}
    >
      <div
        style={{
          height: `${rowVirtualizer.getTotalSize()}px`,
          width: `${columnVirtualizer.getTotalSize()}px`,
          position: "relative",
        }}
      >
        {/* Header row */}
        {renderHeaderRow()}

        {/* Data rows */}
        {rowVirtualizer
          .getVirtualItems()
          .map((virtualRow: TanStackVirtualItem) => {
            const rowIndex = virtualRow.index + 1; // Add 1 for header row
            const rowHeaderText =
              getCellDisplayValue(cells, rowIndex, 0) || `${rowIndex}`;

            // Check if this row should be rendered based on conditional rules
            // In edit mode, always show all rows
            // In preview/submission modes, apply conditional rendering
            const shouldRenderRow =
              mode === "edit" ||
              evaluateRowConditionalRendering(
                rowIndex, // This is already 1-based (rowIndex 1 is the first data row)
                conditionalRows,
                getCellValue
              );

            // Skip rendering this row if it shouldn't be shown
            if (!shouldRenderRow) {
              return null;
            }

            return (
              <TableRow
                key={`row-${rowIndex}`}
                style={{
                  height: virtualRow.size,
                  transform: `translateY(${virtualRow.start}px)`,
                  position: "absolute",
                  width: "100%",
                }}
              >
                {/* Row header */}
                <TableCell
                  className="font-medium bg-muted/70 sticky left-0 z-10 border-r"
                  style={{
                    height: virtualRow.size,
                    width: 60,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  {rowHeaderText}
                </TableCell>

                {/* Row cells */}
                {columnVirtualizer
                  .getVirtualItems()
                  .map((virtualColumn: TanStackVirtualItem) => {
                    return renderVirtualCell(
                      virtualRow.index,
                      virtualColumn.index
                    );
                  })}
              </TableRow>
            );
          })
          .filter(Boolean)}
      </div>
    </div>
  );
}

export default memo(VirtualizedGrid);
