// Environment variables for development
// This file is served by Vite in development mode
// In production, this file is generated by docker-entrypoint.sh

window.ENV = {
  VITE_API_MODE: "real",
  VITE_API_BASE_URL: "http://**********:8080/api/v1",
  VITE_COGNITO_HOSTED_UI_URL: undefined,
  VITE_COGNITO_CLIENT_ID: undefined,
  VITE_COGNITO_USER_POOL_ID: undefined,
  VITE_COGNITO_REGION: undefined,
  VITE_COGNITO_REDIRECT_URI: "http://localhost:3000/callback",
  VITE_SESSION_INACTIVITY_TIMEOUT_MINUTES: "60",
  VITE_SESSION_WARNING_TIME_MINUTES: "5",
  VITE_BLACK_LOGO_URL: undefined,
  VITE_WHITE_LOGO_URL: undefined,
  VITE_SCHEME_NAME: undefined,
  VITE_COOCKIES_FILE_URL: undefined,
};
