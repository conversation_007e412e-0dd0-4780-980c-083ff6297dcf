import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

interface LoadingProps {
  message?: string;
  className?: string;
  iconClassName?: string;
  showMessage?: boolean;
}

export function Loading({
  message = "Loading...",
  className,
  iconClassName,
  showMessage = true,
}: LoadingProps) {
  return (
    <div
      className={cn(
        "flex flex-col items-center justify-center py-20",
        className
      )}
    >
      <Loader2
        className={cn("h-10 w-10 animate-spin text-primary", iconClassName)}
      />
      {showMessage && <p className="mt-4 text-muted-foreground">{message}</p>}
    </div>
  );
}
