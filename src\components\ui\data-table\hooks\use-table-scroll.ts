import { useEffect, useRef } from "react";

interface UseTableScrollProps {
  pinnedColumns: Record<string, number>;
}

/**
 * Custom hook to handle table scrolling effects
 */
export function useTableScroll({ pinnedColumns }: UseTableScrollProps) {
  // Reference to the table container for scroll handling
  const tableContainerRef = useRef<HTMLDivElement>(null);

  // Store scroll timer for debouncing
  const scrollTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Add scroll event listener to force repaint of pinned columns
  useEffect(() => {
    const tableContainer = tableContainerRef.current;
    if (!tableContainer || Object.keys(pinnedColumns).length === 0) return;

    // Create a throttled scroll handler to improve performance
    let lastScrollTime = 0;
    const scrollThreshold = 16; // ~60fps

    const handleScroll = () => {
      const now = Date.now();
      if (now - lastScrollTime < scrollThreshold) return;
      lastScrollTime = now;

      // Force a repaint of the pinned columns by toggling a CSS property
      const pinnedElements = tableContainer.querySelectorAll(
        '.table-cell[data-pinned="true"]'
      );

      // Clear any hover-related classes during scroll
      tableContainer.querySelectorAll(".column-hovered").forEach((cell) => {
        cell.classList.remove("column-hovered");
      });

      // Add a class to the container during scrolling
      tableContainer.classList.add("is-scrolling");

      // Use debounced approach to remove the class after scrolling stops
      if (scrollTimerRef.current) {
        clearTimeout(scrollTimerRef.current);
      }
      scrollTimerRef.current = setTimeout(() => {
        tableContainer.classList.remove("is-scrolling");
      }, 150);

      pinnedElements.forEach((el) => {
        // This forces a repaint without changing the visual appearance
        el.classList.add("force-repaint");

        // Use RAF for better performance
        requestAnimationFrame(() => {
          el.classList.remove("force-repaint");
        });
      });
    };

    tableContainer.addEventListener("scroll", handleScroll, { passive: true });

    return () => {
      tableContainer.removeEventListener("scroll", handleScroll);
    };
  }, [pinnedColumns]);

  // Add event listeners to handle hover effects on pinned columns
  useEffect(() => {
    const tableContainer = tableContainerRef.current;
    if (!tableContainer || Object.keys(pinnedColumns).length === 0) return;

    // Function to handle mouse enter on pinned cells
    const handleMouseEnter = (e: MouseEvent) => {
      // We're using CSS for hover effects now, so this is simplified
      // Just ensure we're not in a scrolling state
      const target = e.target as HTMLElement;
      const pinnedCell = target.closest('.table-cell[data-pinned="true"]');

      if (pinnedCell && tableContainer.scrollLeft > 0) {
        // Get the column ID from the content div
        const contentDiv = pinnedCell.querySelector("[data-column-id]");
        if (contentDiv) {
          const columnId = contentDiv.getAttribute("data-column-id");

          // Add a class to all cells in this column to ensure proper styling during scroll
          if (columnId) {
            tableContainer
              .querySelectorAll(
                `.table-cell[data-pinned="true"] [data-column-id="${columnId}"]`
              )
              .forEach((cell) => {
                cell.closest(".table-cell")?.classList.add("column-hovered");
              });
          }
        }
      }
    };

    // Function to handle mouse leave on pinned cells
    const handleMouseLeave = () => {
      // Remove any hover-related classes
      tableContainer.querySelectorAll(".column-hovered").forEach((cell) => {
        cell.classList.remove("column-hovered");
      });
    };

    // Add event listeners
    tableContainer.addEventListener("mouseover", handleMouseEnter, {
      capture: true,
    });
    tableContainer.addEventListener("mouseout", handleMouseLeave, {
      capture: true,
    });

    return () => {
      tableContainer.removeEventListener("mouseover", handleMouseEnter, {
        capture: true,
      });
      tableContainer.removeEventListener("mouseout", handleMouseLeave, {
        capture: true,
      });
    };
  }, [pinnedColumns]);

  return {
    tableContainerRef,
  };
}
