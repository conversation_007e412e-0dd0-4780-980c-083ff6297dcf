import { memo } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ValidationEditor } from "../ValidationEditor";
import { OptionsEditor } from "../OptionsEditor";
import { Info } from "lucide-react";

interface GridCellsTabProps {
  columnHeaders: string[];
  rowNumbers: number[];
  gridConfig: any; // Using any for brevity, but should be properly typed
  component: any; // Using any for brevity, but should be properly typed
}

/**
 * Tab for configuring grid cells
 */
const GridCellsTab = memo(function GridCellsTab({
  columnHeaders,
  rowNumbers,
  gridConfig,
  component,
}: GridCellsTabProps) {
  // Helper functions for cell styling
  const getSelectedCellClassName = (): string => {
    return "bg-primary/20";
  };

  const getHeaderCellClassName = (): string => {
    return "bg-muted";
  };

  const getRegularCellClassName = (): string => {
    return "";
  };

  // Get cell class name based on cell type and selection state
  const getCellClassName = (
    isSelected: boolean,
    rowIndex: number,
    colIndex: number
  ): string => {
    // Use a more functional approach to determine the class name
    return determineClassName(isSelected, rowIndex, colIndex);
  };

  // Helper function to determine the appropriate class name
  const determineClassName = (
    isSelected: boolean,
    rowIndex: number,
    colIndex: number
  ): string => {
    // Priority: selection state first, then cell type
    if (isSelected) {
      return getSelectedCellClassName();
    }

    // Check if it's a header cell
    if (gridConfig.isCellHeader(rowIndex, colIndex)) {
      return getHeaderCellClassName();
    }

    // Default case for regular cells
    return getRegularCellClassName();
  };

  return (
    <div className="space-y-4">
      <div className="border rounded-md p-4">
        <h4 className="text-sm font-medium mb-2">Select a cell to configure</h4>
        <div className="overflow-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr>
                <th className="border p-1 w-8"></th>
                {columnHeaders.map((header, colIndex) => (
                  <th
                    key={`table-header-${header}-${colIndex}`}
                    className="border p-1 text-center"
                  >
                    {header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {rowNumbers.map((rowNum, rowIndex) => (
                <tr key={`table-row-${rowNum}-${rowIndex}`}>
                  <td className="border p-1 text-center font-medium">
                    {rowNum}
                  </td>
                  {columnHeaders.map((_, colIndex) => {
                    const cellId = gridConfig.indicesToExcel(
                      rowIndex,
                      colIndex
                    );
                    const isSelected = gridConfig.selectedCell === cellId;
                    return (
                      <td
                        key={`cell-${cellId}`}
                        className={`border p-0 text-center ${getCellClassName(
                          isSelected,
                          rowIndex,
                          colIndex
                        )}`}
                      >
                        <button
                          type="button"
                          className="w-full h-full p-1 cursor-pointer"
                          onClick={() =>
                            gridConfig.handleCellSelect(rowIndex, colIndex)
                          }
                          aria-label={`Select Cell ${cellId}`}
                          title={`Cell ${cellId}`}
                        >
                          {gridConfig.getCellValue(rowIndex, colIndex) ?? cellId}
                        </button>
                      </td>
                    );
                  })}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {gridConfig.selectedCell && (
        <div className="border rounded-md p-4 space-y-4">
          <h4 className="text-sm font-medium">
            Cell {gridConfig.selectedCell} Properties
          </h4>

          {(() => {
            // Convert Excel-style coordinate to row/col indices
            const colLetter = gridConfig.selectedCell.charAt(0);
            const rowNumber = parseInt(gridConfig.selectedCell.substring(1));
            const rowIndex = rowNumber - 1;
            const colIndex = colLetter.charCodeAt(0) - 65;
            return (
              <div className="space-y-4" key={`cell-props-${gridConfig.selectedCell}`}>
                <div className="grid gap-2">
                  <Label htmlFor="cellId">Cell ID</Label>
                  <Input
                    id="cellId"
                    key={`cellId-${gridConfig.selectedCell}`}
                    value={gridConfig.getCellId(rowIndex, colIndex)}
                    onChange={(e) =>
                      gridConfig.handleCellIdChange(
                        rowIndex,
                        colIndex,
                        e.target.value
                      )
                    }
                    placeholder={`Default: ${gridConfig.indicesToExcel(rowIndex, colIndex)}`}
                  />
                  <p className="text-xs text-muted-foreground">
                    Custom identifier for this cell. Defaults to Excel-style coordinate (e.g., A1, B2).
                  </p>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="cellHeader"
                    checked={gridConfig.isCellHeader(rowIndex, colIndex)}
                    onCheckedChange={(checked) =>
                      gridConfig.handleCellTypeChange(
                        rowIndex,
                        colIndex,
                        checked ? "header" : "data"
                      )
                    }
                  />
                  <Label htmlFor="cellHeader">Header cell</Label>
                </div>

                {gridConfig.isCellHeader(rowIndex, colIndex) && (
                  <div className="grid gap-2">
                    <Label htmlFor="cellValue">Value</Label>
                    <Input
                      id="cellValue"
                      value={gridConfig.getCellValue(rowIndex, colIndex)}
                      onChange={(e) =>
                        gridConfig.handleCellValueChange(
                          rowIndex,
                          colIndex,
                          e.target.value
                        )
                      }
                      placeholder="Header text"
                    />
                  </div>
                )}

                {!gridConfig.isCellHeader(rowIndex, colIndex) && (
                  <>
                    <div className="flex justify-between items-center">
                      <div className="grid gap-2 flex-1 mr-2">
                        <Label htmlFor="cellInputType">Input Type</Label>
                        <Select
                          value={gridConfig.getCellInputType(
                            rowIndex,
                            colIndex
                          )}
                          onValueChange={(value) =>
                            gridConfig.handleCellInputTypeChange(
                              rowIndex,
                              colIndex,
                              value
                            )
                          }
                        >
                          <SelectTrigger id="cellInputType">
                            <SelectValue placeholder="Select input type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="text">Text</SelectItem>
                            <SelectItem value="number">Number</SelectItem>
                            <SelectItem value="select">Select</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        className="mt-6"
                        onClick={() =>
                          gridConfig.applyCellConfigToColumn(rowIndex, colIndex)
                        }
                        aria-label="Apply this cell's configuration to all cells in this column"
                      >
                        Apply to Column
                      </Button>
                    </div>

                    {gridConfig.getCellInputType(rowIndex, colIndex) ===
                      "number" && (
                        <>
                          <div className="grid grid-cols-2 gap-4">
                            <div className="grid gap-2">
                              <Label htmlFor="cellMin">Min Value</Label>
                              <Input
                                id="cellMin"
                                type="number"
                                value={
                                  gridConfig.getCellMin(rowIndex, colIndex) ?? ""
                                }
                                onChange={(e) =>
                                  gridConfig.handleCellMinChange(
                                    rowIndex,
                                    colIndex,
                                    e.target.value === ""
                                      ? undefined
                                      : Number(e.target.value)
                                  )
                                }
                              />
                            </div>
                            <div className="grid gap-2">
                              <Label htmlFor="cellMax">Max Value</Label>
                              <Input
                                id="cellMax"
                                type="number"
                                value={
                                  gridConfig.getCellMax(rowIndex, colIndex) ?? ""
                                }
                                onChange={(e) =>
                                  gridConfig.handleCellMaxChange(
                                    rowIndex,
                                    colIndex,
                                    e.target.value === ""
                                      ? undefined
                                      : Number(e.target.value)
                                  )
                                }
                              />
                            </div>
                          </div>
                          <div className="grid gap-2">
                            <Label htmlFor="cellStep">Step</Label>
                            <Input
                              id="cellStep"
                              type="number"
                              value={
                                gridConfig.getCellStep(rowIndex, colIndex) ?? ""
                              }
                              onChange={(e) =>
                                gridConfig.handleCellStepChange(
                                  rowIndex,
                                  colIndex,
                                  e.target.value === ""
                                    ? undefined
                                    : Number(e.target.value)
                                )
                              }
                            />
                          </div>
                          <div className="grid gap-2">
                            <Label htmlFor="cellUnit">Unit</Label>
                            <Input
                              id="cellUnit"
                              value={gridConfig.getCellUnit(rowIndex, colIndex)}
                              onChange={(e) =>
                                gridConfig.handleCellUnitChange(
                                  rowIndex,
                                  colIndex,
                                  e.target.value
                                )
                              }
                              placeholder="e.g., kg, m, °C"
                            />
                          </div>
                          <p className="text-xs text-muted-foreground flex items-center gap-1">
                            <Info className="w-3 h-3" />
                            <span>Sets input options (e.g., min, max). Not a validation rule.</span>
                          </p>
                        </>
                      )}

                    {gridConfig.getCellInputType(rowIndex, colIndex) ===
                      "select" && (
                        <div className="space-y-4">
                          <Label>Options</Label>
                          <OptionsEditor
                            options={gridConfig.getCellOptions(
                              rowIndex,
                              colIndex
                            )}
                            onChange={(options) =>
                              gridConfig.handleCellOptionsChange(
                                rowIndex,
                                colIndex,
                                options
                              )
                            }
                          />
                        </div>
                      )}
                  </>
                )}

                {gridConfig.isCellHeader(rowIndex, colIndex) && (
                  <div className="grid gap-2">
                    <Label htmlFor="cellUnit">Unit</Label>
                    <Input
                      id="cellUnit"
                      value={gridConfig.getCellUnit(rowIndex, colIndex)}
                      onChange={(e) =>
                        gridConfig.handleCellUnitChange(
                          rowIndex,
                          colIndex,
                          e.target.value
                        )
                      }
                      placeholder="e.g., kg, m, °C"
                    />
                  </div>
                )}

                <div className="space-y-2">
                  <Label>Validations</Label>
                  <ValidationEditor
                    component={{
                      ...component,
                      type: gridConfig.getCellInputType(
                        rowIndex,
                        colIndex
                      ),
                      validations: gridConfig.getCellValidations(
                        rowIndex,
                        colIndex
                      ),
                    }}
                    onChange={(validations) =>
                      gridConfig.handleCellValidationChange(
                        rowIndex,
                        colIndex,
                        validations
                      )
                    }
                  />
                </div>
              </div>
            );
          })()}
        </div>
      )}
    </div>
  );
});

export default GridCellsTab;
