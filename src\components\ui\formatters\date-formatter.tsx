interface DateFormatterProps {
  dateString: string;
  format?: "short" | "medium" | "long";
  showTime?: boolean;
}

/**
 * Component for formatting dates
 */
export function DateFormatter({
  dateString,
  format = "medium",
  showTime = false,
}: Readonly<DateFormatterProps>) {
  if (!dateString) return null;

  try {
    const date = new Date(dateString);

    // Check if date is valid
    if (isNaN(date.getTime())) {
      return <span>{dateString}</span>;
    }

    // Format based on the specified format
    switch (format) {
      case "short":
        return (
          <span>
            {showTime ? date.toLocaleString() : date.toLocaleDateString()}
          </span>
        );
      case "long":
        return (
          <span>
            {showTime
              ? date.toLocaleString(undefined, {
                  weekday: "long",
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                  hour: "2-digit",
                  minute: "2-digit",
                  second: "2-digit",
                })
              : date.toLocaleDateString(undefined, {
                  weekday: "long",
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                })}
          </span>
        );
      case "medium":
      default:
        return (
          <span>
            {showTime
              ? date.toLocaleString(undefined, {
                  year: "numeric",
                  month: "short",
                  day: "numeric",
                  hour: "2-digit",
                  minute: "2-digit",
                })
              : date.toLocaleDateString(undefined, {
                  year: "numeric",
                  month: "short",
                  day: "numeric",
                })}
          </span>
        );
    }
  } catch (error) {
    console.error("Error formatting date:", error);
    return <span>{dateString}</span>;
  }
}

export default DateFormatter;
