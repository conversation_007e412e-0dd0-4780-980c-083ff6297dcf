/**
 * Common pagination parameters for API requests
 */
export interface PaginationParams {
  page: number;
  size: number;
  sort?: string[];
}

/**
 * Common sorting parameters for API requests
 */
export interface SortingParams {
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

/**
 * Common filtering parameters for API requests
 */
export interface FilterParams {
  [key: string]: string | number | boolean | string[] | number[] | undefined;
}

/**
 * Combined query parameters for API requests
 */
export type QueryParams = Partial<PaginationParams> & FilterParams;

/**
 * Paginated response structure
 */
export interface PaginatedResponse<T> {
  data: T[];
  meta: {
    totalCount: number;
    pageCount: number;
    currentPage: number;
    pageSize: number;
  };
}

/**
 * Permission status for individual actions
 */
export type PermissionStatus = "UNAUTHORIZED" | "ENABLED" | "DISABLED";

/**
 * Permissions object for entity actions
 */
export interface EntityPermissions {
  [actionId: string]: PermissionStatus;
}

/**
 * Entity with permissions - extends any entity type with permissions
 */
export interface EntityWithPermissions {
  permissions: EntityPermissions;
}

export type EntityWithPermissionsType<T> = T & EntityWithPermissions;

export interface GetAllResponses<T> {
  page: PageableObject;
  size: number;
  content: EntityWithPermissionsType<T>[];
  number: number;
  sort: SortObject;
  first: boolean;
  last: boolean;
  numberOfElements: number;
  empty: boolean;
}

export interface ErrorResponse {
  code: string;
  message: string;
  status: number;
  details: string;
}

/**
 * Navigation item returned by backend API
 */
export interface NavigationItem {
  id: string;
  name: string;
  href: string;
  icon: string; // Icon name/identifier (e.g., "file-text", "users", "settings")
  order: number;
  parentId?: string; // For nested navigation support
  permissions: {
    view: PermissionStatus;
  };
}

/**
 * Navigation API response structure
 */
export interface NavigationResponse {
  items: NavigationItem[];
}

/**
 * Global permissions for pages and actions
 */
export interface GlobalPermissions {
  [pageId: string]: {
    create: PermissionStatus;
    list: PermissionStatus;
  };
}

/**
 * Global permissions API response structure
 */
export interface GlobalPermissionsResponse {
  [pageId: string]: {
    create: PermissionStatus;
    list: PermissionStatus;
  };
}

export interface DeleteResponses<T> {
  status: number;
  message: string;
  data: T;
  errors: any;
  timestamp: string;
}

export interface PageableObject {
  number: number;
  size: number;
  totalElements: number;
  totalPages: number;
}

export interface SortObject {
  sorted: boolean;
  unsorted: boolean;
  empty: boolean;
}

/**
 * API error codes
 */
export enum ApiErrorCode {
  BAD_REQUEST = "BAD_REQUEST",
  UNAUTHORIZED = "UNAUTHORIZED",
  FORBIDDEN = "FORBIDDEN",
  NOT_FOUND = "NOT_FOUND",
  CONFLICT = "CONFLICT",
  INTERNAL_SERVER_ERROR = "INTERNAL_SERVER_ERROR",
  SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE",
  NETWORK_ERROR = "NETWORK_ERROR",
  UNKNOWN_ERROR = "UNKNOWN_ERROR",
}

/**
 * API error response
 */
export interface ApiErrorResponse {
  message: string;
  code: ApiErrorCode | string;
  status: number;
  details?: Record<string, any>;
}

/**
 * API success response
 */
export interface ApiSuccessResponse<T> {
  data: T;
  message?: string;
}

/**
 * Generic API response
 */
export type ApiResponse<T> = ApiSuccessResponse<T> | ApiErrorResponse;

/**
 * Query key factory for consistent key structure
 */
export const queryKeys = {
  all: ["api"] as const,

  // Forms
  forms: () => [...queryKeys.all, "forms"] as const,
  form: (id: string) => [...queryKeys.forms(), id] as const,

  // Submissions
  submissions: () => [...queryKeys.all, "submissions"] as const,
  submission: (id: string) => [...queryKeys.submissions(), id] as const,

  // Projects
  projects: () => [...queryKeys.all, "projects"] as const,
  project: (id: string) => [...queryKeys.projects(), id] as const,

  // Funding Rounds
  fundingRounds: () => [...queryKeys.all, "funding-rounds"] as const,
  fundingRound: (id: string) => [...queryKeys.fundingRounds(), id] as const,

  // Custom query key factory
  custom: (scope: string, ...args: any[]) =>
    [...queryKeys.all, scope, ...args] as const,
};
