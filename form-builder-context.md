# Project Brief

A single-page application (SPA) designed to empower non-technical users to create, edit, and manage complex forms using a visual form builder interface. The builder supports drag-and-drop UI elements and produces JSON schema for persistence and backend integration.

## Key Features

1. **Form Listing & Management**

   - List of existing forms filtered by `draft` or `active` status
   - Search bar to filter forms by name
   - Create new form button
   - Edit button per form, opening form details to update

2. **Form Builder**

   - Drag-and-drop zone with the following form components:

     - Input: text, number, date, datetime
     - Select, Checkbox, Radio
     - Table/Data Grid (Excel-like configuration)

   - Click to edit each component to define:

     - Label, placeholder, unit (for numbers)
     - Validations (e.g., required, min, max,custom validation message)
     - Conditional rendering rules

   - **Data Grid Special Input**

     - Excel-style format: columns A-Z, rows A-Z
     - Configurable headers (e.g., A-1: 'baseline', A-2: 'target')
     - Configurable row names (e.g., B-1: 'fuel', C-1: 'biofuel')
     - Editable cells (e.g., B-2: number input with validations + unit)

3. **Form Editor Tabs**

   - **Editor Tab**: build the form visually
   - **Preview Tab**: uses React Hook Form to render preview, supports form submission and displays resulting form data
   - **JSON Schema Tab**: displays the JSON representation of the form
   - Support for generating the editor from an existing JSON schema

---

# Product Context

This tool solves the challenge of enabling non-technical users (e.g., business analysts, admins) to design and configure data-collection forms without requiring developer intervention. It removes technical barriers while allowing developers and systems to consume clean JSON-based form definitions.

Key business problems being solved:

- Manual form development cycles
- Miscommunication between business and dev teams
- Difficulty updating form structures over time

---

# System Patterns

## Architecture

- React SPA with strict TypeScript setup
- Component-based design with Single Responsibility Principle (SRP)
- Form builder structured with editor/preview/schema views

## Design Patterns

- Presentational and container components
- State management via local component state and form libraries (e.g., React Hook Form)
- Modular and reusable UI elements (e.g., input types, dialog configs)

## Directory and Naming Conventions

- Filenames: `kebab-case`
- Components: `PascalCase`
- Variables & functions: `camelCase`

## Configurations

- **tsconfig.json**: strict mode enabled, modern module resolution, consistent casing
- **Prettier**: formatting only, 2-space tabs, trailing commas, 100-char line width
- **ESLint**: no `any`, no unused vars, strict rules , React 19 compatible

---

# Tech Context

## Core Stack

- **React 19** (with new hooks & compiler)
- **React Router** (with lazy loaded routes)
- **TypeScript** (strict mode)
- **Tailwind CSS 4** (utility-first styling)
- **ShadCN UI** (accessible, themeable UI components)
- **Lucide React** (lightweight icons)
- **React Hook Form** (form state & validation)

## Tooling & Linting

- ESLint for live linting with strict rules
- Prettier for consistent code formatting
- Vite or Next.js as potential bundlers

## Constraints

- No third-party design systems beyond Tailwind/ShadCN
- Clean DX and minimal runtime dependencies
- Support for future integration with persistence layer (e.g., Firebase, Supabase, API backend)

---

# Progress

## Completed

- Core documentation (this set of markdown files)
- Feature planning (form list, form builder, JSON schema editor)
- Tech stack finalized

## In Progress

- UI layout and drag-and-drop form builder UI
- JSON schema generation and live form preview
- Table/DataGrid design interface

## Outstanding Tasks

- Finalize editable dialog for each form element
- Build preview/submit view powered by React Hook Form
- Implement JSON schema ↔ visual form sync logic
- Implement persistence (save/load schema)
