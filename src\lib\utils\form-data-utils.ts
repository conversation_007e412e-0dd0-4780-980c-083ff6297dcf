import { FormComponent } from "@/lib/schemas/form-schemas";

/**
 * Utility functions for processing form data
 */

/**
 * Checks if a value is a structured DataGrid
 *
 * @param val - The value to check
 * @returns True if the value is a structured DataGrid
 */
export const isStructuredDataGrid = (val: any): boolean =>
  val && typeof val === "object" && "rows" in val && "metadata" in val;

/**
 * Finds a DataGrid component by name in a list of components
 *
 * @param name - The component name to find
 * @param components - The list of components to search
 * @returns The DataGrid component or undefined if not found
 */
export const findDataGridComponent = (
  name: string,
  components: FormComponent[]
): FormComponent | undefined =>
  components.find((c) => c.name === name && c.type === "datagrid");

/**
 * Prepares form data for loading into a form
 *
 * @param formData - The raw form data
 * @param components - The form components
 * @returns Processed form data ready for loading
 */
export const prepareFormDataForLoading = (
  formData: Record<string, any>,
  components: FormComponent[]
): Record<string, any> => {
  if (!formData || typeof formData !== "object") {
    return {};
  }

  const processedData: Record<string, any> = { ...formData };

  // Process each field in the form data
  Object.keys(formData).forEach((fieldName) => {
    const value = formData[fieldName];

    // Handle DataGrid components
    if (isStructuredDataGrid(value)) {
      const dataGridComponent = findDataGridComponent(fieldName, components);
      if (dataGridComponent) {
        // DataGrid component found, keep the structured data
        processedData[fieldName] = value;
      }
    }

    // Handle checkbox groups (arrays)
    else if (Array.isArray(value)) {
      const component = components.find((c) => c.name === fieldName);
      if (component?.type === "checkbox" && component.multiple) {
        // Checkbox group, keep the array
        processedData[fieldName] = value;
      }
    }

    // Handle date fields
    else if (typeof value === "string" && /^\d{4}-\d{2}-\d{2}/.test(value)) {
      const component = components.find((c) => c.name === fieldName);
      if (component?.type === "date" || component?.type === "datetime") {
        // Date field, keep the ISO string
        processedData[fieldName] = value;
      }
    }
  });

  return processedData;
};

/**
 * Formats a field name for display
 *
 * @param name - The field name to format
 * @returns Formatted field name
 */
export const formatFieldName = (name: string): string => {
  // Handle special cases
  if (name === "resourceMatrix") return "Resource Matrix";

  // Convert camelCase to Title Case with spaces
  return (
    name
      // Insert a space before all uppercase letters
      .replace(/([A-Z])/g, " $1")
      // Replace underscores with spaces
      .replace(/_/g, " ")
      // Capitalize the first letter
      .replace(/^./, (str) => str.toUpperCase())
      // Trim any extra spaces
      .trim()
  );
};

/**
 * Checks if a value is an array of checkbox values
 *
 * @param val - The value to check
 * @returns True if the value is a checkbox array
 */
export const isCheckboxArray = (val: any): boolean =>
  Array.isArray(val) &&
  val.every(
    (item) =>
      typeof item === "string" ||
      typeof item === "boolean" ||
      typeof item === "number"
  );

/**
 * Gets the parent step for a component
 *
 * @param componentId - The component ID
 * @param components - The list of all components
 * @returns The parent step component or undefined if not found
 */
export const getParentStep = (
  componentId: string,
  components: FormComponent[]
): FormComponent | undefined => {
  const component = components.find((c) => c.id === componentId);
  if (!component) return undefined;

  // If this is a step, return it
  if (component.type === "step") return component;

  // If no parent, return undefined
  if (!component.parentId) return undefined;

  // Recursively find the parent step
  return getParentStep(component.parentId, components);
};
