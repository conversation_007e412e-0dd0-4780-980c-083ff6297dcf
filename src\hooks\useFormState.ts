import { useState } from "react";

interface FormStatus {
  isSubmitted: boolean;
  isValid: boolean;
  message: string;
}

interface UseFormStateReturn {
  formStatus: FormStatus;
  setFormStatus: (status: FormStatus) => void;
}

/**
 * Hook for managing form state
 */
export function useFormState(): UseFormStateReturn {
  // Form and submission state

  // Form status state
  const [formStatus, setFormStatus] = useState<FormStatus>({
    isSubmitted: false,
    isValid: false,
    message: "",
  });

  return {
    formStatus,
    setFormStatus,
  };
}
