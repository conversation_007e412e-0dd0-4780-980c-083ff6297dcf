# Changelog Guide

This document provides guidelines for maintaining the project's changelog.

## Purpose of a Changelog

A changelog is a file that contains a curated, chronologically ordered list of notable changes for each version of a project. It helps users and contributors understand what significant changes have been made between each release (or version) of the project.

## Format

Our changelog follows the [Keep a Changelog](https://keepachangelog.com/) format, which organizes changes into the following categories:

- **Added**: New features introduced in the release
- **Changed**: Changes to existing functionality
- **Deprecated**: Features that will be removed in upcoming releases
- **Removed**: Features removed in this release
- **Fixed**: Bug fixes
- **Security**: Vulnerability fixes

## Principles

1. **Changelogs are for humans**, not machines. Write in clear, understandable language.
2. **Each version should have its own entry**.
3. **Group changes by type** (Added, Changed, etc.).
4. **Newest releases come first**. The most recent version appears at the top.
5. **Mention whether you follow Semantic Versioning**. We do!

## How to Add Entries

### Using Conventional Commits

The changelog is automatically generated from conventional commit messages. When you create commits using the Conventional Commits format, they will be automatically categorized and added to the changelog.

To create a commit that follows the Conventional Commits specification, use:

```bash
npm run commit
```

This will guide you through creating a properly formatted commit message.

### Generating the Changelog

To update the changelog with new entries:

```bash
# Update the changelog with new entries
npm run changelog

# Regenerate the entire changelog
npm run changelog:all
```

### Manual Editing

While the changelog is automatically generated, you can still edit it manually if needed. However, keep in mind that your changes might be overwritten when the changelog is regenerated.

If you need to make manual edits:

1. Find the [Unreleased] section at the top of the file
2. Locate the appropriate subsection (Features, Bug Fixes, etc.)
3. Make your changes following the existing format
4. Keep entries concise but descriptive

**Note:** It's generally better to create a new conventional commit rather than manually editing the changelog.

## Releasing a New Version

When it's time to release a new version:

1. Make sure all your changes are committed using conventional commit messages
2. Use the npm version command:

   ```bash
   npm version patch  # for bug fixes
   npm version minor  # for new features
   npm version major  # for breaking changes
   ```

3. This will automatically:
   - Update the version in package.json
   - Generate a changelog from conventional commits
   - Create a git commit with the new version
   - Create a git tag for the version

## Example Entry

Here's an example of a good changelog entry generated from conventional commits:

```markdown
## [1.0.0] - 2023-10-15

### Features

- Add data grid component with Excel-style configuration ([abc123](https://gitlab.dom.tti/gemserv/Scheme-Manager-Frontend/commit/abc123))
- Add support for conditional rendering in form components ([def456](https://gitlab.dom.tti/gemserv/Scheme-Manager-Frontend/commit/def456))

### Performance Improvements

- Improve form builder drag-and-drop interface performance ([ghi789](https://gitlab.dom.tti/gemserv/Scheme-Manager-Frontend/commit/ghi789))

### Bug Fixes

- Fix form validation error messages not displaying correctly ([jkl012](https://gitlab.dom.tti/gemserv/Scheme-Manager-Frontend/commit/jkl012))
- Fix form submission in Safari browsers ([mno345](https://gitlab.dom.tti/gemserv/Scheme-Manager-Frontend/commit/mno345))

### BREAKING CHANGES

- Update React to version 19.0.0 ([pqr678](https://gitlab.dom.tti/gemserv/Scheme-Manager-Frontend/commit/pqr678))
```

## Tips for Writing Good Commit Messages

Since the changelog is generated from commit messages, it's important to write good conventional commit messages:

1. **Be specific**: "fix: form submission bug" is too vague. "fix: form submission failure with special characters" is better.
2. **Use the right type**: Choose the appropriate type (feat, fix, docs, etc.) for your changes.
3. **Add scope when relevant**: Use scopes to indicate which part of the codebase is affected (e.g., `feat(auth):`, `fix(data-grid):`).
4. **Reference issues**: Include "Closes #123" or "Fixes #456" in the commit footer to link to issues.
5. **Mark breaking changes**: Always use the "BREAKING CHANGE:" prefix in the footer for breaking changes.
6. **Keep the subject line short**: Aim for 50-72 characters in the subject line.
7. **Use imperative mood**: Write "add feature" not "added feature" or "adds feature".

## References

- [Conventional Commits](https://www.conventionalcommits.org/)
- [Keep a Changelog](https://keepachangelog.com/)
- [Semantic Versioning](https://semver.org/)
- [conventional-changelog](https://github.com/conventional-changelog/conventional-changelog)
- [commitizen](https://github.com/commitizen/cz-cli)
- [commitlint](https://commitlint.js.org/)
