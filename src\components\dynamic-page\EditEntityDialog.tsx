import { useState, useEffect } from "react";
import { <PERSON><PERSON>, DialogContent } from "@/components/ui/dialog";
import {
  FormPageConfig,
  ListPageConfig,
  PageConfig,
} from "@/lib/types/page-config";
import DynamicFormPage from "./DynamicFormPage";
import { EntityService } from "@/lib/services/entity-service";
import { Loading } from "@/components/ui/loading";

interface EditEntityDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  config: PageConfig;
  entityId: string;
  onSuccess?: () => void;
}

/**
 * Reusable component for editing an entity in a dialog
 * Can be used from both list view and details view
 */
export function EditEntityDialog({
  open,
  onOpenChange,
  config,
  entityId,
  onSuccess,
}: Readonly<EditEntityDialogProps>) {
  const [isLoading, setIsLoading] = useState(true);
  const [entityData, setEntityData] = useState<Record<string, any> | null>(
    null
  );

  // Get the appropriate form config based on the config type
  const getFormConfig = (): FormPageConfig => {
    if ("type" in config) {
      const configWithType = config as { type: string };

      if (configWithType.type === "list") {
        // For list pages, use the embedded createFormConfig
        const listConfig = config as ListPageConfig;
        if (listConfig.createFormConfig) {
          return listConfig.createFormConfig;
        } else {
          console.error("List config is missing createFormConfig");
          return config as unknown as FormPageConfig;
        }
      } else if (configWithType.type === "form") {
        // For form pages, use the config directly
        return config as FormPageConfig;
      } else {
        // Fallback for unknown config types
        console.error("Unknown config type:", configWithType.type);
        return config as unknown as FormPageConfig;
      }
    } else {
      // If no type property, assume it's already a FormPageConfig
      return config as FormPageConfig;
    }
  };

  const formConfig = getFormConfig();

  // Log the form config being used

  // Load entity data when dialog opens
  useEffect(() => {
    const loadEntityData = async () => {
      if (open && entityId) {
        try {
          setIsLoading(true);

          const data = await EntityService.getEntityById(
            config.entityName,
            config.endpoints.get,
            entityId
          );

          setEntityData(data);
        } catch (error) {
          console.error("Error loading entity data:", error);
        } finally {
          setIsLoading(false);
        }
      }
    };

    loadEntityData();
  }, [open, entityId, config.entityName]);

  // Handle form submission success
  const handleFormSuccess = () => {
    onOpenChange(false);

    // Call the onSuccess callback if provided
    if (onSuccess) {
      onSuccess();
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">

        {isLoading ? (
          <Loading message={`Loading ${config.entityName} data...`} />
        ) : (
          <DynamicFormPage
            config={formConfig}
            entityId={entityId}
            initialData={entityData || {}}
            onSuccess={handleFormSuccess}
            onCancel={() => onOpenChange(false)}
          />
        )}
      </DialogContent>
    </Dialog>
  );
}

export default EditEntityDialog;
