/**
 * Component for displaying parameter validation errors
 */
export function ParameterErrorDisplay({ 
  errors, 
  onRetry 
}: { 
  errors: string[]; 
  onRetry?: () => void; 
}) {
  return (
    <div className="flex flex-col items-center justify-center p-8">
      <h2 className="text-2xl font-bold mb-4 text-destructive">Parameter Error</h2>
      <div className="text-muted-foreground mb-6 space-y-2">
        {errors.map((error, index) => (
          <p key={index} className="text-center">{error}</p>
        ))}
      </div>
      {onRetry && (
        <button 
          onClick={onRetry}
          className="px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90"
        >
          Retry
        </button>
      )}
    </div>
  );
}
