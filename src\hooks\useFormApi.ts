import { useApiQuery, useApiMutation, useInvalidateQueries } from "./useApi";
import { queryKeys } from "@/lib/types/api";
import { FormMeta, FormSchema, FormStatus } from "@/lib/types/form";

/**
 * Hook for fetching all forms with optional filtering
 */
export function useForms(status?: FormStatus) {
  const queryKey = status
    ? [...queryKeys.forms(), { status }]
    : queryKeys.forms();

  return useApiQuery<FormMeta[]>(
    queryKey,
    "/forms",
    { status },
    {
      // Keep forms data fresh for a shorter time (2 minutes)
      staleTime: 2 * 60 * 1000,
    }
  );
}

/**
 * Hook for fetching a single form by ID
 */
export function useForm(id: string) {
  return useApiQuery<FormSchema>(
    queryKeys.form(id),
    `/forms/${id}`,
    undefined,
    {
      // Don't fetch if no ID is provided
      enabled: !!id,
    }
  );
}

/**
 * Hook for creating a new form
 */
export function useCreateForm() {
  const { invalidateQueries } = useInvalidateQueries();

  return useApiMutation<FormSchema, Partial<FormSchema>>("/forms", "POST", {
    onSuccess: () => {
      // Invalidate forms list queries when a new form is created
      invalidateQueries(queryKeys.forms());
    },
    meta: {
      // Custom success message
      successMessage: "Form created successfully",
    },
  });
}

/**
 * Hook for updating an existing form
 */
export function useUpdateForm(id: string) {
  const { invalidateQueries } = useInvalidateQueries();

  return useApiMutation<FormSchema, Partial<FormSchema>>(
    `/forms/${id}`,
    "PUT",
    {
      onSuccess: () => {
        // Invalidate both the forms list and the specific form
        invalidateQueries(queryKeys.forms());
        invalidateQueries(queryKeys.form(id));
      },
      meta: {
        // Custom success message
        successMessage: "Form updated successfully",
      },
    }
  );
}

/**
 * Hook for deleting a form
 */
export function useDeleteForm() {
  const { invalidateQueries } = useInvalidateQueries();

  return useApiMutation<void, string>(
    `/forms`, // The ID will be appended in the mutation function
    "DELETE",
    {
      // Override the default mutation function
      onMutate: async (id: string) => {
        const response = await fetch(`/api/forms/${id}`, {
          method: "DELETE",
        });

        if (!response.ok) {
          throw new Error("Failed to delete form");
        }
      },
      onSuccess: () => {
        // Invalidate forms list queries when a form is deleted
        invalidateQueries(queryKeys.forms());
      },
      meta: {
        // Custom success message
        successMessage: "Form deleted successfully",
      },
    }
  );
}

/**
 * Hook for publishing a form
 */
export function usePublishForm(id: string) {
  const { invalidateQueries } = useInvalidateQueries();

  return useApiMutation<FormSchema, void>(`/forms/${id}/publish`, "POST", {
    onSuccess: () => {
      // Invalidate both the forms list and the specific form
      invalidateQueries(queryKeys.forms());
      invalidateQueries(queryKeys.form(id));
    },
    meta: {
      // Custom success message
      successMessage: "Form published successfully",
    },
  });
}
