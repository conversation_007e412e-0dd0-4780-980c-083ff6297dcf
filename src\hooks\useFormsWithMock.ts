import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { FormMeta, FormSchema, FormStatus } from "@/lib/types/form";
import { queryKeys } from "@/lib/types/api";
import { useToast } from "@/components/ui/use-toast";
import { apiClient } from "@/lib/api/api-client";

/**
 * Hook for fetching all forms with optional filtering
 * This hook uses either the mock API or real API based on the current mode
 */
export function useForms(status?: FormStatus) {
  const { toast } = useToast();

  const queryKey = status
    ? [...queryKeys.forms(), { status }]
    : queryKeys.forms();

  return useQuery({
    queryKey,
    queryFn: async () => {
      try {
        const response = await apiClient.get<FormMeta[]>("/forms", {
          params: { status },
        });
        return response.data;
      } catch (error) {
        toast({
          title: "Error",
          description:
            error instanceof Error ? error.message : "Failed to fetch forms",
          variant: "destructive",
        });
        throw error;
      }
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

/**
 * Hook for fetching a single form by ID
 */
export function useForm(id: string) {
  const { toast } = useToast();

  return useQuery({
    queryKey: queryKeys.form(id),
    queryFn: async () => {
      try {
        const response = await apiClient.get<FormSchema>(`/forms/${id}`);
        return response.data;
      } catch (error) {
        toast({
          title: "Error",
          description:
            error instanceof Error ? error.message : "Failed to fetch form",
          variant: "destructive",
        });
        throw error;
      }
    },
    enabled: !!id,
  });
}

/**
 * Hook for creating a new form
 */
export function useCreateForm() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (formData: Partial<FormSchema>) => {
      try {
        const response = await apiClient.post<FormSchema>("/forms", formData);
        return response.data;
      } catch (error) {
        toast({
          title: "Error",
          description:
            error instanceof Error ? error.message : "Failed to create form",
          variant: "destructive",
        });
        throw error;
      }
    },
    onSuccess: () => {
      // Invalidate forms list queries when a new form is created
      queryClient.invalidateQueries({ queryKey: queryKeys.forms() });

      toast({
        title: "Success",
        description: "Form created successfully",
      });
    },
  });
}

/**
 * Hook for updating an existing form
 */
export function useUpdateForm(id: string) {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (formData: Partial<FormSchema>) => {
      try {
        const response = await apiClient.put<FormSchema>(
          `/forms/${id}`,
          formData
        );
        return response.data;
      } catch (error) {
        toast({
          title: "Error",
          description:
            error instanceof Error ? error.message : "Failed to update form",
          variant: "destructive",
        });
        throw error;
      }
    },
    onSuccess: () => {
      // Invalidate both the forms list and the specific form
      queryClient.invalidateQueries({ queryKey: queryKeys.forms() });
      queryClient.invalidateQueries({ queryKey: queryKeys.form(id) });

      toast({
        title: "Success",
        description: "Form updated successfully",
      });
    },
  });
}

/**
 * Hook for deleting a form
 */
export function useDeleteForm() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: string) => {
      try {
        await apiClient.delete(`/forms/${id}`);
      } catch (error) {
        toast({
          title: "Error",
          description:
            error instanceof Error ? error.message : "Failed to delete form",
          variant: "destructive",
        });
        throw error;
      }
    },
    onSuccess: () => {
      // Invalidate forms list queries when a form is deleted
      queryClient.invalidateQueries({ queryKey: queryKeys.forms() });

      toast({
        title: "Success",
        description: "Form deleted successfully",
      });
    },
  });
}
