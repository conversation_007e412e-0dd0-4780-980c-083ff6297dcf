# Permission System Example

## Example API Response

Here's an example of how the backend should structure the API response for the Projects list with the new centralized permission system:

```json
{
  "content": [
    {
      "id": "proj-001",
      "projectRef": "REF-2024-001",
      "projectName": "Green Energy Initiative",
      "applicationType": "CAPITAL",
      "status": "DRAFT",
      "applicantOrganisation": "EcoTech Solutions",
      "permissions": {
        "view": "enabled",
        "edit": "enabled",
        "delete": "disabled",
        "application": "enabled",
        "attach": "unauthorized",
        "documents": "enabled"
      }
    },
    {
      "id": "proj-002", 
      "projectRef": "REF-2024-002",
      "projectName": "Smart City Infrastructure",
      "applicationType": "REVENUE",
      "status": "SUBMITTED",
      "applicantOrganisation": "Urban Tech Ltd",
      "permissions": {
        "view": "enabled",
        "edit": "disabled",
        "delete": "unauthorized",
        "application": "disabled",
        "attach": "enabled",
        "documents": "enabled"
      }
    },
    {
      "id": "proj-003",
      "projectRef": "REF-2024-003", 
      "projectName": "Healthcare Modernization",
      "applicationType": "CAPITAL",
      "status": "APPROVED",
      "applicantOrganisation": "MedTech Innovations",
      "permissions": {
        "view": "enabled",
        "edit": "unauthorized",
        "delete": "unauthorized", 
        "application": "unauthorized",
        "attach": "enabled",
        "documents": "enabled"
      }
    }
  ],
  "page": {
    "number": 0,
    "size": 10,
    "totalElements": 3,
    "totalPages": 1
  }
}
```

## Expected UI Behavior

Based on the above API response, here's how the actions would be rendered for each project:

### Project 1 (Green Energy Initiative - DRAFT status)
- **View**: ✅ Button rendered and enabled
- **Edit**: ✅ Button rendered and enabled  
- **Delete**: ⚪ Button rendered but disabled (grayed out)
- **Go to Application**: ✅ Button rendered and enabled
- **Attach User**: ❌ Button not rendered (unauthorized)
- **Go to Documents**: ✅ Button rendered and enabled

### Project 2 (Smart City Infrastructure - SUBMITTED status)
- **View**: ✅ Button rendered and enabled
- **Edit**: ⚪ Button rendered but disabled (grayed out)
- **Delete**: ❌ Button not rendered (unauthorized)
- **Go to Application**: ⚪ Button rendered but disabled (grayed out)
- **Attach User**: ✅ Button rendered and enabled
- **Go to Documents**: ✅ Button rendered and enabled

### Project 3 (Healthcare Modernization - APPROVED status)
- **View**: ✅ Button rendered and enabled
- **Edit**: ❌ Button not rendered (unauthorized)
- **Delete**: ❌ Button not rendered (unauthorized)
- **Go to Application**: ❌ Button not rendered (unauthorized)
- **Attach User**: ✅ Button rendered and enabled
- **Go to Documents**: ✅ Button rendered and enabled

## Business Logic Examples

The backend can implement complex business logic for permissions:

### Status-Based Permissions
```javascript
// Example backend logic
function getProjectPermissions(project, user) {
  const permissions = {
    view: "enabled", // Always allow viewing
    documents: "enabled" // Always allow document access
  };

  switch (project.status) {
    case "DRAFT":
      permissions.edit = "enabled";
      permissions.delete = user.role === "admin" ? "enabled" : "disabled";
      permissions.application = "enabled";
      permissions.attach = user.role === "admin" ? "enabled" : "unauthorized";
      break;
      
    case "SUBMITTED":
      permissions.edit = "disabled"; // Can't edit submitted projects
      permissions.delete = "unauthorized"; // Can't delete submitted projects
      permissions.application = "disabled"; // Application is locked
      permissions.attach = "enabled"; // Can still manage users
      break;
      
    case "APPROVED":
      permissions.edit = "unauthorized"; // No editing approved projects
      permissions.delete = "unauthorized"; // No deleting approved projects
      permissions.application = "unauthorized"; // Application is finalized
      permissions.attach = "enabled"; // Can manage project team
      break;
  }

  return permissions;
}
```

### Role-Based Permissions
```javascript
function applyRoleBasedPermissions(permissions, user, project) {
  if (user.role === "applicant" && project.ownerId !== user.id) {
    // Applicants can only view their own projects
    return {
      view: "enabled",
      edit: "unauthorized",
      delete: "unauthorized",
      application: "unauthorized",
      attach: "unauthorized",
      documents: "enabled"
    };
  }
  
  if (user.role === "assessor") {
    // Assessors can view and manage users but not edit project details
    permissions.edit = "unauthorized";
    permissions.delete = "unauthorized";
    permissions.application = "disabled";
  }
  
  return permissions;
}
```

## Testing the Implementation

To test the new permission system:

1. **Mock API Response**: Update your API to return the permissions object
2. **Test Different Scenarios**: Create projects with different statuses and permission combinations
3. **Verify UI Behavior**: Ensure buttons are rendered according to their permission status
4. **Test Interactions**: Confirm disabled buttons don't trigger actions
5. **Check Fallback**: Verify the system works when no permissions are provided

## Migration Checklist

- [ ] Backend API updated to include permissions object
- [ ] Frontend types updated (✅ Complete)
- [ ] ActionsCellRenderer updated (✅ Complete)
- [ ] Entity service handles permissions (✅ Complete)
- [ ] Documentation created (✅ Complete)
- [ ] Testing completed
- [ ] Deployment coordinated between frontend and backend teams
