# TypeScript Migration Guide

This guide explains how to migrate from the legacy type definitions to the new Zod schema-based type system.

## Why Migrate?

The new Zod schema-based type system offers several advantages:

1. **Runtime Validation**: Zod schemas provide runtime validation in addition to compile-time type checking.
2. **Self-Documenting**: Zod schemas are self-documenting and provide better error messages.
3. **Type Inference**: You can infer TypeScript types from Zod schemas, ensuring your types and validation are always in sync.
4. **Composability**: Zod schemas are highly composable, making it easy to build complex validation rules.
5. **Improved Type Safety**: The new system eliminates `any` types and provides more specific type definitions.

## Migration Steps

### 1. Import from the New Location

Replace imports from `@/lib/types/form` with imports from `@/lib/schemas/form-schemas`:

```typescript
// Before
import { FormComponent, TextComponent, ValidationRule } from "@/lib/types/form";

// After
import { 
  FormComponent, 
  TextComponent, 
  ValidationRule,
  formComponentSchema,
  textComponentSchema
} from "@/lib/schemas/form-schemas";
```

### 2. Use Zod Schemas for Validation

Replace manual validation with Zod schema validation:

```typescript
// Before
import { validateValue, getValidationRules } from "@/lib/utils/validation-utils";

const rules = getValidationRules(component);
const error = validateValue(value, rules);

// After
import { createComponentValidationSchema } from "@/lib/utils/zod-validation-utils";

const schema = createComponentValidationSchema(component);
try {
  schema.parse(value); // Will throw if validation fails
  // Value is valid
} catch (err) {
  if (err instanceof z.ZodError) {
    const error = err.errors[0].message;
    // Handle validation error
  }
}
```

### 3. Use Type Guards for Type Narrowing

Replace type assertions with type guards:

```typescript
// Before
if (component.type === "text") {
  const textComponent = component as TextComponent;
  // Use textComponent
}

// After
import { isTextComponent } from "@/lib/utils/zod-validation-utils";

if (isTextComponent(component)) {
  // component is now typed as TextComponent
  // Use component directly
}
```

### 4. Use Generic Components and Hooks

Replace non-generic components and hooks with generic versions:

```typescript
// Before
function FormField({ name, label, children }) {
  // ...
}

// After
import { FormField } from "@/components/form-builder/GenericFormComponent";

<FormField<MyFormValues>
  name="email"
  label="Email Address"
  required
  error={errors.email?.message}
>
  <Input {...register("email")} />
</FormField>
```

### 5. Use the Generic Form Hook

Replace custom form handling with the generic form hook:

```typescript
// Before
const [formData, setFormData] = useState({});
const [errors, setErrors] = useState({});
const [isSubmitting, setIsSubmitting] = useState(false);

const handleSubmit = async (e) => {
  e.preventDefault();
  setIsSubmitting(true);
  try {
    // Validate and submit form
  } catch (error) {
    // Handle error
  } finally {
    setIsSubmitting(false);
  }
};

// After
import { useGenericForm } from "@/hooks/useGenericForm";
import { z } from "zod";

// Define form schema
const formSchema = z.object({
  name: z.string().min(2),
  email: z.string().email(),
  // ...
});

// Infer type from schema
type FormValues = z.infer<typeof formSchema>;

// Use generic form hook
const {
  methods,
  isSubmitting,
  handleSubmit,
  formStatus
} = useGenericForm<FormValues, ApiResponse>({
  validationSchema: formSchema,
  defaultValues: { name: "", email: "" },
  onSubmit: async (data) => {
    // Submit form data
    return await api.submitForm(data);
  },
  onSuccess: (response) => {
    // Handle success
  }
});
```

### 6. Use the Typed Form Component Hook

Replace manual component state management with the typed form component hook:

```typescript
// Before
const [component, setComponent] = useState({
  id: "name",
  type: "text",
  label: "Name",
  // ...
});
const [value, setValue] = useState("");

// After
import { useTypedFormComponent } from "@/hooks/useTypedFormComponent";

const textComponent = {
  id: "name",
  type: "text" as const, // Important: use const assertion
  label: "Name",
  name: "name",
  // ...
};

const {
  value,
  handleValueChange,
  error,
  componentProps
} = useTypedFormComponent(textComponent, setTextComponent);
```

## Example: Complete Form with Zod Validation

Here's a complete example of a form using the new Zod schema-based approach:

```typescript
import { z } from "zod";
import { useGenericForm } from "@/hooks/useGenericForm";
import { GenericFormComponent, FormField } from "@/components/form-builder/GenericFormComponent";

// Define form schema
const userFormSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  age: z.coerce.number().min(18, "You must be at least 18 years old"),
  role: z.enum(["user", "admin", "editor"]),
  termsAccepted: z.literal(true, {
    errorMap: () => ({ message: "You must accept the terms" })
  })
});

// Infer type from schema
type UserFormValues = z.infer<typeof userFormSchema>;

function UserForm() {
  const {
    methods,
    isSubmitting,
    formStatus,
    handleSubmit
  } = useGenericForm<UserFormValues, { success: boolean }>({
    validationSchema: userFormSchema,
    defaultValues: {
      name: "",
      email: "",
      age: undefined,
      role: "user",
      termsAccepted: false
    },
    onSubmit: async (data) => {
      // Submit form data
      await api.createUser(data);
      return { success: true };
    }
  });
  
  const { register, formState: { errors } } = methods;
  
  return (
    <GenericFormComponent
      title="User Registration"
      methods={methods}
      isSubmitting={isSubmitting}
      formStatus={formStatus}
      onSubmit={handleSubmit}
    >
      <FormField<UserFormValues>
        name="name"
        label="Name"
        required
        error={errors.name?.message}
      >
        <Input {...register("name")} />
      </FormField>
      
      {/* Other form fields */}
    </GenericFormComponent>
  );
}
```

## Need Help?

If you encounter any issues during migration, refer to the following resources:

1. Check the TypedFormDemo component for a complete example
2. Review the Zod documentation at https://zod.dev
3. Explore the form-schemas.ts and zod-validation-utils.ts files for reference
