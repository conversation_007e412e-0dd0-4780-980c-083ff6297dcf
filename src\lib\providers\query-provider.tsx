import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactNode, useState } from "react";

interface QueryProviderProps {
  readonly children: ReactNode;
}

/**
 * QueryProvider component that wraps the application with TanStack Query's QueryClientProvider
 *
 * This provider initializes a QueryClient with sensible defaults for the application:
 * - Stale time of 5 minutes for queries to reduce unnecessary refetches
 * - Cache time of 10 minutes to keep data in memory longer
 * - 3 retry attempts for failed queries with exponential backoff
 */
export function QueryProvider({ children }: QueryProviderProps) {
  // Use useState with a function to ensure the QueryClient is only created once
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            // Keep data fresh for 5 minutes before considering it stale
            staleTime: 5 * 60 * 1000,

            // Keep data in cache for 10 minutes before garbage collection
            gcTime: 10 * 60 * 1000,

            // Retry failed queries 3 times with exponential backoff
            retry: 3,

            // Don't refetch on window focus in development to avoid disrupting debugging
            refetchOnWindowFocus: process.env.NODE_ENV === "production",

            // Don't use error boundaries for queries
            // useErrorBoundary: false, // Removed as it's not in the type
          },
          mutations: {
            // Retry failed mutations once
            retry: 1,
          },
        },
      })
  );

  return (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
}
