/**
 * Utility functions for date formatting and manipulation
 */

/**
 * Format a date string for display in standard format (e.g., "15 Jan 2023")
 * 
 * @param dateString - The date string to format
 * @param includeTime - Whether to include time in the formatted date
 * @returns Formatted date string or "N/A" if the date is invalid
 */
export const formatDate = (dateString?: string, includeTime = false): string => {
  if (!dateString) return "N/A";
  
  try {
    const date = new Date(dateString);
    
    // Check if date is valid
    if (isNaN(date.getTime())) {
      return "N/A";
    }
    
    const options: Intl.DateTimeFormatOptions = {
      day: "numeric",
      month: "short",
      year: "numeric",
    };
    
    if (includeTime) {
      options.hour = "2-digit";
      options.minute = "2-digit";
    }
    
    return date.toLocaleDateString("en-GB", options);
  } catch (error) {
    console.error("Error formatting date:", error);
    return "N/A";
  }
};

/**
 * Get a formatted date string for the current date
 * 
 * @param includeTime - Whether to include time in the formatted date
 * @returns Formatted current date string
 */
export const getCurrentFormattedDate = (includeTime = false): string => {
  return formatDate(new Date().toISOString(), includeTime);
};

/**
 * Format a date for use in date input fields (YYYY-MM-DD)
 * 
 * @param dateString - The date string to format
 * @returns Formatted date string for input fields or empty string if invalid
 */
export const formatDateForInput = (dateString?: string): string => {
  if (!dateString) return "";
  
  try {
    const date = new Date(dateString);
    
    // Check if date is valid
    if (isNaN(date.getTime())) {
      return "";
    }
    
    return date.toISOString().split("T")[0];
  } catch (error) {
    console.error("Error formatting date for input:", error);
    return "";
  }
};

/**
 * Format a date for use in datetime-local input fields (YYYY-MM-DDThh:mm)
 * 
 * @param dateString - The date string to format
 * @returns Formatted datetime string for input fields or empty string if invalid
 */
export const formatDateTimeForInput = (dateString?: string): string => {
  if (!dateString) return "";
  
  try {
    const date = new Date(dateString);
    
    // Check if date is valid
    if (isNaN(date.getTime())) {
      return "";
    }
    
    // Format as YYYY-MM-DDThh:mm
    return date.toISOString().slice(0, 16);
  } catch (error) {
    console.error("Error formatting datetime for input:", error);
    return "";
  }
};
