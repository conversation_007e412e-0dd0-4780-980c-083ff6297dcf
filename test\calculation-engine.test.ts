import { renderHook } from "@testing-library/react";
import { useCalculationEngine } from "../src/hooks/useCalculationEngine";
import { FormSchema } from "../src/lib/schemas/form-schemas";
import { FormSubmission } from "../src/lib/types/submission";

describe("useCalculationEngine", () => {
  it("should return true for a valid expression", () => {
    const formSchema: FormSchema = {
      id: "test-form",
      name: "Test Form",
      components: [{ id: "component1", type: "text", label: "Component 1" }],
    };

    const formValues: FormSubmission["data"] = {
      component1: "10",
    };

    const { result } = renderHook(() =>
      useCalculationEngine(formSchema, formValues)
    );

    const validationResult = result.current.evaluateValidationExpression(
      "SELF > 5",
      "component1",
      formValues
    );

    expect(validationResult).toBe(true);
  });

  it("should return false for an invalid expression", () => {
    const formSchema: FormSchema = {
      id: "test-form",
      name: "Test Form",
      components: [{ id: "component1", type: "text", label: "Component 1" }],
    };

    const formValues: FormSubmission["data"] = {
      component1: "3",
    };

    const { result } = renderHook(() =>
      useCalculationEngine(formSchema, formValues)
    );

    const validationResult = result.current.evaluateValidationExpression(
      "SELF > 5",
      "component1",
      formValues
    );

    expect(validationResult).toBe(false);
  });
});
