import { memo } from "react";
import { SectionComponent } from "@/lib/types/form";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { FolderOpen } from "lucide-react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

interface SectionConfigTabContentProps {
  component: SectionComponent;
  onChange: <K extends keyof SectionComponent>(field: K, value: SectionComponent[K]) => void;
}

/**
 * Configuration tab content for Section components
 */
export const SectionConfigTabContent = memo(function SectionConfigTabContent({
  component,
  onChange,
}: SectionConfigTabContentProps) {
  return (
    <div className="space-y-4 pt-4">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h4 className="text-sm font-medium">Section Configuration</h4>
          <div className="flex items-center space-x-2">
            <FolderOpen className="h-5 w-5 text-muted-foreground" />
          </div>
        </div>

        <div className="rounded-md border p-4 space-y-4">
          <p className="text-sm text-muted-foreground">
            Sections group related form fields together and can be
            collapsed to save space.
          </p>

          <div className="flex items-center space-x-2">
            <Switch
              id="collapsible"
              checked={component.collapsible ?? true}
              onCheckedChange={(checked) =>
                onChange("collapsible", checked)
              }
            />
            <Label htmlFor="collapsible">Make section collapsible</Label>
          </div>

          {component.collapsible && (
            <div className="flex items-center space-x-2">
              <Switch
                id="defaultCollapsed"
                checked={component.defaultCollapsed ?? false}
                onCheckedChange={(checked) =>
                  onChange("defaultCollapsed", checked)
                }
              />
              <Label htmlFor="defaultCollapsed">
                Collapsed by default
              </Label>
            </div>
          )}

          <div className="mt-4">
            <Accordion type="single" collapsible className="w-full">
              <AccordionItem value="preview">
                <AccordionTrigger>Section Preview</AccordionTrigger>
                <AccordionContent>
                  <div className="p-2 rounded-md bg-muted/50">
                    <p className="text-sm">{component.label}</p>
                    {component.description && (
                      <p className="text-xs text-muted-foreground mt-1">
                        {component.description}
                      </p>
                    )}
                  </div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>
        </div>
      </div>
    </div>
  );
});

export default SectionConfigTabContent;
