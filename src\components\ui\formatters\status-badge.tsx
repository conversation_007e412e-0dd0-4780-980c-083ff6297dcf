import { EnhancedBadge } from "@/components/ui/enhanced-badge";
import { AlertCircle, CheckCircle2, Clock, FileText, Loader2, XCircle, } from "lucide-react";

interface StatusBadgeProps {
  status: string;
  size?: "sm" | "md" | "lg";
  showIcon?: boolean;
}

/**
 * Enhanced component for displaying status badges with icons
 */
export function StatusBadge({
  status,
  size = "md",
  showIcon = true,
}: Readonly<StatusBadgeProps>) {
  const statusLower = status.toLowerCase();

  // Get badge variant based on status
  const getBadgeVariant = (status: string) => {
    if (
      status === "active" ||
      status === "published" ||
      status === "approved" ||
      status === "completed" ||
      status === "success" ||
      status === "submitted" ||
      status === "application submitted"
    ) {
      return "default";
    }
    if (
      status === "inactive" ||
      status === "rejected" ||
      status === "failed" ||
      status === "error"
    ) {
      return "destructive";
    }
    if (
      status === "pending" ||
      status === "in progress" ||
      status === "processing"
    ) {
      return "secondary";
    }
    if (status === "draft" || status === "application draft") {
      return "outline";
    }
    if (status === "warning" || status === "archived") {
      return "warning";
    }
    if (status === "info") {
      return "info";
    }
    return "outline";
  };

  // Get icon based on status
  const getIcon = () => {
    if (
      statusLower === "active" ||
      statusLower === "published" ||
      statusLower === "approved" ||
      statusLower === "completed" ||
      statusLower === "success" ||
      statusLower === "submitted" ||
      statusLower === "application submitted"
    ) {
      return <CheckCircle2 className="h-3.5 w-3.5 mr-1" />;
    }
    if (
      statusLower === "inactive" ||
      statusLower === "rejected" ||
      statusLower === "failed" ||
      statusLower === "error"
    ) {
      return <XCircle className="h-3.5 w-3.5 mr-1" />;
    }
    if (statusLower === "pending" || statusLower === "processing") {
      return <Clock className="h-3.5 w-3.5 mr-1" />;
    }
    if (statusLower === "in progress") {
      return <Loader2 className="h-3.5 w-3.5 mr-1 animate-spin" />;
    }
    if (statusLower === "draft" || statusLower === "application draft") {
      return <FileText className="h-3.5 w-3.5 mr-1" />;
    }
    if (statusLower === "warning") {
      return <AlertCircle className="h-3.5 w-3.5 mr-1" />;
    }
    return null;
  };

  // Get size classes
  const getSizeClasses = () => {
    switch (size) {
      case "sm":
        return "text-xs px-2 py-0.5";
      case "lg":
        return "text-sm px-3 py-1";
      case "md":
      default:
        return "text-xs px-2.5 py-0.5";
    }
  };

  return (
    <EnhancedBadge
      variant={getBadgeVariant(statusLower)}
      className={`flex items-center ${getSizeClasses()}`}
    >
      {showIcon && getIcon()}
      {status}
    </EnhancedBadge>
  );
}

export default StatusBadge;
