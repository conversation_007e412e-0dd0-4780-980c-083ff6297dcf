import React from 'react';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { FormComponent, Calculation } from '@/lib/schemas/form-schemas';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Code } from 'lucide-react';

interface CalculationEditorProps {
    component: FormComponent;
    onChange: (calculation: Calculation) => void;
}

export const CalculationEditor: React.FC<CalculationEditorProps> = ({ component, onChange }) => {
    const handleFormulaChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
        onChange({
            ...component.calculation,
            formula: event.target.value,
        });
    };

    return (
        <div className="space-y-4">
            <div>
                <Label htmlFor="calculation-formula">Formula</Label>
                <Textarea
                    id="calculation-formula"
                    value={component.calculation?.formula || ''}
                    onChange={handleFormulaChange}
                    placeholder="e.g., fieldId1 + fieldId2"
                />
            </div>
            <Alert>
                <Code className="h-4 w-4" />
                <AlertTitle>How to write calculations</AlertTitle>
                <AlertDescription>
                    <p>You can perform calculations using other fields or fixed values.</p>
                    <ul className="list-disc list-inside space-y-1 mt-2">
                        <li>Use field IDs to reference other fields: <code>fieldId1 * fieldId2</code></li>
                        <li>Use functions for grid calculations: <code>SUM(grid1.A2:A10)</code></li>
                        <li>Combine fields and numbers: <code>(field1 + field2) / 100</code></li>
                    </ul>
                </AlertDescription>
            </Alert>
        </div>
    );
};