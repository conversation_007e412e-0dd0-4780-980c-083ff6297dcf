import { EntityService } from "./entity-service";

/**
 * Generate sample projects for demo purposes
 */
export function generateSampleProjects(count: number = 20) {
  const statuses = [
    "active",
    "inactive",
    "pending",
    "completed",
    "in progress",
    "draft",
    "on-hold",
  ];

  const priorities = ["high", "medium", "low"];

  const projects = [];

  for (let i = 1; i <= count; i++) {
    const progress = Math.floor(Math.random() * 101); // 0-100
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - Math.floor(Math.random() * 90)); // Random date in the past 90 days

    const endDate = new Date(startDate);
    endDate.setDate(endDate.getDate() + Math.floor(Math.random() * 180) + 30); // 30-210 days after start date

    projects.push({
      id: `PRJ-${i.toString().padStart(4, "0")}`,
      name: `Project ${i}`,
      description: `This is a sample project ${i} for demonstration purposes.`,
      status: statuses[Math.floor(Math.random() * statuses.length)],
      priority: priorities[Math.floor(Math.random() * priorities.length)],
      progress,
      budget: Math.floor(Math.random() * 100000) + 10000, // 10,000 - 110,000
      completion: progress, // Same as progress for this example
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
    });
  }

  return projects;
}

/**
 * Generate sample funding rounds for demo purposes
 */
export function generateSampleFundingRounds(count: number = 10) {
  const fundingRounds = [];

  for (let i = 1; i <= count; i++) {
    // Create open date (random date in the past 180 days)
    const openDate = new Date();
    openDate.setDate(openDate.getDate() - Math.floor(Math.random() * 180));

    // Create submission deadline (30-90 days after open date)
    const submissionDeadline = new Date(openDate);
    submissionDeadline.setDate(
      submissionDeadline.getDate() + Math.floor(Math.random() * 60) + 30
    );

    fundingRounds.push({
      id: `FR-${i.toString().padStart(3, "0")}`,
      roundNumber: `Round ${i}`,
      openDate: openDate.toISOString(),
      submissionDeadline: submissionDeadline.toISOString(),
    });
  }

  return fundingRounds;
}

/**
 * Initialize entity storage with sample data
 */
// Flag to track if storage has been initialized
let isStorageInitialized = false;

export function initializeEntityStorage() {
  // Only initialize once
  if (isStorageInitialized) {
    return {
      alreadyInitialized: true,
    };
  }

  // Initialize Projects
  const sampleProjects = generateSampleProjects(20);

  // Initialize Funding Rounds
  const sampleFundingRounds = generateSampleFundingRounds(10);

  // Check if entities already exist to prevent duplicate initialization
  try {
    EntityService.initializeEntityStorage("Project", sampleProjects);
    EntityService.initializeEntityStorage("FundingRound", sampleFundingRounds);
    isStorageInitialized = true;
  } catch (error) {
    console.error("Error initializing entity storage:", error);
  }

  // Add more entity initializations here as needed

  // Return the initialized data for debugging
  return {
    projectsCount: sampleProjects.length,
    fundingRoundsCount: sampleFundingRounds.length,
    firstProject: sampleProjects[0],
    firstFundingRound: sampleFundingRounds[0],
    isInitialized: isStorageInitialized,
  };
}

export default {
  initializeEntityStorage,
  generateSampleProjects,
  generateSampleFundingRounds,
};
