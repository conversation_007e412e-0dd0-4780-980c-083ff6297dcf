import {
  DataGridCell,
  DataGridCellInputType,
  SelectOption,
  GridRowConditionalRendering,
} from "@/lib/schemas/form-schemas";

/**
 * Convert row/col indices to Excel-style coordinate (e.g., A1, B2)
 */
export const indicesToExcel = (rowIndex: number, colIndex: number): string => {
  const colLetter = String.fromCharCode(65 + colIndex); // A, B, C, ...
  const rowNumber = rowIndex + 1; // 1, 2, 3, ...
  return `${colLetter}${rowNumber}`;
};

/**
 * Convert Excel-style coordinate to row/col indices
 */
export const excelToIndices = (excelCoord: string): [number, number] => {
  const colLetter = excelCoord.charAt(0);
  const rowNumber = parseInt(excelCoord.substring(1));

  return [rowNumber - 1, colLetter.charCodeAt(0) - 65];
};

/**
 * Get cell configuration from component cells
 */
export const getCellConfig = (
  cells: Record<string, DataGridCell>,
  rowIndex: number,
  colIndex: number
): DataGridCell | undefined => {
  const cellId = indicesToExcel(rowIndex, colIndex);
  return cells[cellId];
};

/**
 * Check if cell is a header
 */
export const isCellHeader = (
  cells: Record<string, DataGridCell>,
  rowIndex: number,
  colIndex: number
): boolean => {
  const cell = getCellConfig(cells, rowIndex, colIndex);
  return cell?.type === "header";
};

/**
 * Get cell display value (from configuration or default)
 */
export const getCellDisplayValue = (
  cells: Record<string, DataGridCell>,
  rowIndex: number,
  colIndex: number
): string => {
  const cell = getCellConfig(cells, rowIndex, colIndex);
  if (cell?.value) {
    return cell.value;
  }

  // Default column headers (A, B, C, ...)
  if (rowIndex === 0) {
    return String.fromCharCode(65 + colIndex);
  }

  // Default row headers (1, 2, 3, ...)
  if (colIndex === 0) {
    return `${rowIndex}`;
  }

  return "";
};

/**
 * Get custom cell ID or fall back to Excel coordinate
 */
export const getCellCustomId = (
  cells: Record<string, DataGridCell>,
  rowIndex: number,
  colIndex: number
): string => {
  const excelCoord = indicesToExcel(rowIndex, colIndex);
  const cell = cells[excelCoord];
  return cell?.id || excelCoord;
};

/**
 * Find cell configuration by custom ID
 */
export const findCellByCustomId = (
  cells: Record<string, DataGridCell>,
  customId: string
): { cell: DataGridCell; excelCoord: string } | null => {
  // First check if the customId is directly an Excel coordinate
  if (cells[customId]) {
    return { cell: cells[customId], excelCoord: customId };
  }

  // Otherwise search through all cells to find one with matching custom ID
  for (const [excelCoord, cell] of Object.entries(cells)) {
    if (cell.id === customId) {
      return { cell, excelCoord };
    }
  }

  return null;
};

/**
 * Create mapping between Excel coordinates and custom IDs
 */
export const mapExcelToCustomIds = (
  cells: Record<string, DataGridCell>
): Record<string, string> => {
  const mapping: Record<string, string> = {};

  for (const [excelCoord, cell] of Object.entries(cells)) {
    mapping[excelCoord] = cell.id || excelCoord;
  }

  return mapping;
};

/**
 * Create reverse mapping from custom IDs to Excel coordinates
 */
export const mapCustomIdsToExcel = (
  cells: Record<string, DataGridCell>
): Record<string, string> => {
  const mapping: Record<string, string> = {};

  for (const [excelCoord, cell] of Object.entries(cells)) {
    const customId = cell.id || excelCoord;
    mapping[customId] = excelCoord;
  }

  return mapping;
};

/**
 * Get cell unit
 */
export const getCellUnit = (
  cells: Record<string, DataGridCell>,
  rowIndex: number,
  colIndex: number
): string | undefined => {
  const cell = getCellConfig(cells, rowIndex, colIndex);
  return cell?.unit;
};

/**
 * Get cell input type
 */
export const getCellInputType = (
  cells: Record<string, DataGridCell>,
  rowIndex: number,
  colIndex: number
): DataGridCellInputType => {
  const cell = getCellConfig(cells, rowIndex, colIndex);
  return cell?.inputType ?? "text";
};

/**
 * Get cell options
 */
export const getCellOptions = (
  cells: Record<string, DataGridCell>,
  rowIndex: number,
  colIndex: number
): SelectOption[] => {
  const cell = getCellConfig(cells, rowIndex, colIndex);
  return cell?.options || [];
};

/**
 * Get cell min value
 */
export const getCellMin = (
  cells: Record<string, DataGridCell>,
  rowIndex: number,
  colIndex: number
): number | undefined => {
  const cell = getCellConfig(cells, rowIndex, colIndex);
  return cell?.min;
};

/**
 * Get cell max value
 */
export const getCellMax = (
  cells: Record<string, DataGridCell>,
  rowIndex: number,
  colIndex: number
): number | undefined => {
  const cell = getCellConfig(cells, rowIndex, colIndex);
  return cell?.max;
};

/**
 * Get cell step value
 */
export const getCellStep = (
  cells: Record<string, DataGridCell>,
  rowIndex: number,
  colIndex: number
): number | undefined => {
  const cell = getCellConfig(cells, rowIndex, colIndex);
  return cell?.step;
};

/**
 * Evaluates a single condition for a cell value
 *
 * @param cellValue - The value of the cell to evaluate
 * @param operator - The operator to use for evaluation
 * @param conditionValue - The value to compare against
 * @returns True if the condition is met
 */
const evaluateSingleCondition = (
  cellValue: string,
  operator: string,
  conditionValue: any
): boolean => {
  // Special handling for empty/notEmpty operators
  if (operator === "empty") {
    const isEmpty =
      cellValue === undefined || cellValue === null || cellValue === "";

    if (isEmpty) {
      return true;
    }
    return false;
  }

  if (operator === "notEmpty") {
    const isNotEmpty =
      cellValue !== undefined && cellValue !== null && cellValue !== "";

    if (isNotEmpty) {
      return true;
    }
    return false;
  }

  // Handle other operators
  let conditionMet = false;

  switch (operator) {
    case "equals": {
      conditionMet = cellValue === conditionValue;

      break;
    }
    case "notEquals": {
      conditionMet = cellValue !== conditionValue;

      break;
    }
    case "contains": {
      conditionMet =
        typeof cellValue === "string" && cellValue.includes(conditionValue);

      break;
    }
    case "greaterThan": {
      conditionMet = Number(cellValue) > Number(conditionValue);

      break;
    }
    case "lessThan": {
      conditionMet = Number(cellValue) < Number(conditionValue);

      break;
    }
    default:
      conditionMet = true;
  }

  return conditionMet;
};

/**
 * Gets conditions that target a specific row
 *
 * @param rowIndex - The row index to get conditions for
 * @param conditionalRows - Array of conditional rendering rules
 * @returns Array of conditions targeting the row
 */
const getConditionsForRow = (
  rowIndex: number,
  conditionalRows: GridRowConditionalRendering[] | undefined
): GridRowConditionalRendering[] => {
  if (!conditionalRows || conditionalRows.length === 0) {
    return [];
  }

  return conditionalRows.filter(
    (condition) => condition.targetRowIndex === rowIndex
  );
};

/**
 * Evaluates a condition and handles errors
 *
 * @param condition - The condition to evaluate
 * @param getCellValue - Function to get cell values
 * @returns True if the condition is met or if there's an error
 */
const evaluateConditionSafely = (
  condition: GridRowConditionalRendering,
  getCellValue: (rowIndex: number, colIndex: number) => string
): boolean => {
  const { cellId, operator, value } = condition;

  try {
    // Convert cellId to indices and get cell value
    const [condRowIndex, condColIndex] = excelToIndices(cellId);
    const cellValue = getCellValue(condRowIndex, condColIndex);

    // Evaluate the condition
    return evaluateSingleCondition(cellValue, operator, value);
  } catch (error) {
    console.error(
      `Error evaluating row condition for cellId ${cellId}:`,
      error
    );
    return true; // On error, render the row
  }
};

/**
 * Evaluates if a row should be rendered based on conditional rules
 *
 * @param rowIndex - The row index to evaluate (1-based, where 1 is the first data row)
 * @param conditionalRows - Array of conditional rendering rules for rows
 * @param getCellValue - Function to get cell values
 * @returns True if the row should be rendered
 */
export const evaluateRowConditionalRendering = (
  rowIndex: number,
  conditionalRows: GridRowConditionalRendering[] | undefined,
  getCellValue: (rowIndex: number, colIndex: number) => string
): boolean => {
  // Early returns for simple cases
  // If no conditional rendering is defined or it's a header row, always render
  if (!conditionalRows || conditionalRows.length === 0 || rowIndex === 0) {
    return true;
  }

  // Get conditions for this row
  const rowConditions = getConditionsForRow(rowIndex, conditionalRows);

  // If no conditions target this row, always render it
  if (rowConditions.length === 0) {
    return true;
  }

  // Check if any condition is met
  const anyConditionMet = rowConditions.some((condition) =>
    evaluateConditionSafely(condition, getCellValue)
  );

  // If any condition was met, render the row
  if (anyConditionMet) {
    return true;
  }

  return false;
};
