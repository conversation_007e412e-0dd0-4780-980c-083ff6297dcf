import { FormComponent } from "@/lib/schemas/form-schemas";
import { transformToFlat } from "./datagrid-transformer";

/**
 * Utility functions for processing form data
 */

/**
 * Checks if a value is a structured DataGrid
 */
export const isStructuredDataGrid = (val: any): boolean =>
  val && typeof val === "object" && "rows" in val && "metadata" in val;

/**
 * Finds a DataGrid component by name in a list of components
 */
export const findDataGridComponent = (
  name: string,
  components: FormComponent[]
): FormComponent | undefined =>
  components.find((c) => c.name === name && c.type === "datagrid");

/**
 * Convert data grid data to custom ID format for final submission
 */
const convertDataGridToCustomIdFormat = (
  componentData: any,
  component: FormComponent
): Record<string, any> => {
  // Type guard to ensure component is a DataGrid component
  if (component.type !== "datagrid") {
    return componentData;
  }

  // If we have flat+structured format, use the flat data (which should already be in custom ID format)
  if (componentData.flat && componentData.structured) {
    return componentData.flat;
  }

  // If we have structured data, we need to convert it back to flat custom ID format
  if (componentData.rows && componentData.metadata) {
    return transformToFlat(componentData, component as any);
  }

  // If it's already flat data, assume it's in the correct format
  return componentData;
};

/**
 * Process form data to ensure data grid values are properly formatted
 * For final submission, data grids will be in custom ID format: { "custom-id": "value" }
 */
export const processFormData = (
  formData: Record<string, any>,
  components: FormComponent[]
): Record<string, any> => {
  // Create a copy of the form data
  const processedData = { ...formData };

  // Find all DataGrid components
  const dataGridComponents = components.filter(
    (component) => component.type === "datagrid"
  );

  // For each DataGrid component, convert to custom ID format for submission
  dataGridComponents.forEach((component) => {
    const componentName = component.name;
    const componentData = processedData[componentName];

    // Skip if no data found for this component
    if (!componentData || typeof componentData !== "object") {
      return;
    }

    try {
      // Convert to custom ID format for final submission
      processedData[componentName] = convertDataGridToCustomIdFormat(
        componentData,
        component
      );
    } catch (error: unknown) {
      // Log the error but continue processing
      console.error(
        `Error converting DataGrid data to custom ID format for ${componentName}:`,
        error
      );

      // Keep the original data if transformation fails
      // This ensures we don't lose data even if transformation fails
    }
  });

  return processedData;
};

/**
 * Detect if flat data is in old Excel coordinate format
 */
const isExcelCoordinateFormat = (flatData: Record<string, any>): boolean => {
  const keys = Object.keys(flatData);
  // Check if most keys match Excel coordinate pattern (A1, B2, etc.)
  const excelPattern = /^[A-Z]+\d+$/;
  const excelKeys = keys.filter((key) => excelPattern.test(key));
  return excelKeys.length > keys.length * 0.5; // More than 50% are Excel coordinates
};

/**
 * Convert old Excel coordinate format to custom ID format
 */
const convertExcelToCustomIdFormat = (
  excelData: Record<string, any>,
  component: FormComponent
): Record<string, any> => {
  const customIdData: Record<string, any> = {};

  // Type guard to ensure component is a DataGrid component
  if (component.type !== "datagrid") {
    return excelData;
  }

  for (const [excelCoord, value] of Object.entries(excelData)) {
    // Get custom ID for this Excel coordinate, fallback to Excel coordinate
    const cellConfig = component.cells?.[excelCoord];
    const customId = cellConfig?.id || excelCoord;
    customIdData[customId] = value;
  }

  return customIdData;
};

/**
 * Prepares form data for loading into the form
 * Handles special cases like DataGrid components and backward compatibility
 */
export const prepareFormDataForLoading = (
  formData: Record<string, any>,
  components: FormComponent[],
  setValue: (name: string, value: any, options?: any) => void
): void => {
  // Set each field value with the saved data
  Object.entries(formData).forEach(([key, value]) => {
    // Special handling for DataGrid components
    const component = findDataGridComponent(key, components);

    if (component && component.type === "datagrid") {
      try {
        let processedValue = value;

        // Handle structured data format
        if (isStructuredDataGrid(value)) {
          // Convert structured data to flat format for the grid
          const flatData = transformToFlat(value, component);
          processedValue = {
            flat: flatData,
            structured: value,
          };
        }
        // Handle flat data format (could be old Excel format or new custom ID format)
        else if (typeof value === "object" && value !== null) {
          // Check if it's old Excel coordinate format and convert if needed
          if (isExcelCoordinateFormat(value)) {
            const customIdData = convertExcelToCustomIdFormat(value, component);
            processedValue = customIdData;
          } else {
            // Assume it's already in custom ID format
            processedValue = value;
          }
        }

        // Set the value in the format that DataGrid component expects
        setValue(key, processedValue, { shouldDirty: true, shouldTouch: true });
      } catch (error) {
        console.error(`Error processing DataGrid data for ${key}:`, error);
        // Fall back to setting the value directly
        setValue(key, value, { shouldDirty: true, shouldTouch: true });
      }
    } else {
      // For regular fields, just set the value directly
      setValue(key, value, { shouldDirty: true, shouldTouch: true });
    }
  });
};
