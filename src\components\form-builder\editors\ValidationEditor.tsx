import { useState, useEffect } from "react";
import {
  FormComponent,
  FormComponentValidation,
  ValidationRule,
} from "@/lib/schemas/form-schemas";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Code, Plus, X } from "lucide-react";

interface ValidationEditorProps {
  readonly component: FormComponent;
  readonly onChange: (validations: FormComponentValidation[]) => void;
  readonly allComponents?: FormComponent[];
}

/**
 * Component for editing validation rules for form components
 */
export function ValidationEditor({
  component,
  onChange,
  allComponents,
}: ValidationEditorProps) {
  const validations = component.validations || [];
  const [newValidation, setNewValidation] = useState<{
    rule: ValidationRule;
    value: string;
    message: string;
    expression: string;
    reference?: string;
  }>({
    rule: "required",
    value: "",
    message: "",
    expression: "",
  });
  const [valueType, setValueType] = useState<"value" | "reference">("value");

  useEffect(() => {
    setValueType("value");
  }, [newValidation.rule]);

  // Get available validation rules based on component type
  const getAvailableRules = (): ValidationRule[] => {
    const commonRules: ValidationRule[] = ["required", "expression"];

    switch (component.type) {
      case "text":
        return [
          ...commonRules,
          "minLength",
          "maxLength",
          "pattern",
          "email",
          "url",
        ];
      case "number":
        return [...commonRules, "min", "max"];
      case "date":
      case "datetime":
        return [...commonRules, "min", "max"];
      case "select":
      case "radio":
      case "checkbox":
        return [...commonRules];
      default:
        return commonRules;
    }
  };

  const handleAddValidation = () => {
    // Skip if message is empty
    if (!newValidation.message.trim()) return;

    const needsValue =
      newValidation.rule === "min" ||
      newValidation.rule === "max" ||
      newValidation.rule === "minLength" ||
      newValidation.rule === "maxLength" ||
      newValidation.rule === "pattern" ||
      newValidation.rule === "greater" ||
      newValidation.rule === "less";

    if (needsValue) {
      if (valueType === "value" && !newValidation.value) return;
      if (valueType === "reference" && !newValidation.reference) return;
    }

    if (newValidation.rule === "expression" && !newValidation.expression) {
      return;
    }

    // Create the validation object
    const validation: FormComponentValidation = {
      rule: newValidation.rule,
      message: newValidation.message,
    };

    if (needsValue) {
      if (valueType === "reference") {
        validation.reference = newValidation.reference;
      } else {
        validation.value = newValidation.value;
      }
    } else if (newValidation.rule === "expression") {
      validation.expression = newValidation.expression;
    }

    // Add the validation to the list
    onChange([...validations, validation]);

    // Reset the form
    setNewValidation({
      rule: "required",
      value: "",
      message: "",
      expression: "",
      reference: undefined,
    });
  };

  const handleRemoveValidation = (index: number) => {
    const newValidations = [...validations];
    newValidations.splice(index, 1);
    onChange(newValidations);
  };

  const availableRules = getAvailableRules();

  // Helper function to get the appropriate label for validation value input
  const getValidationValueLabel = (rule: ValidationRule): string => {
    if (
      rule === "min" ||
      rule === "max" ||
      rule === "greater" ||
      rule === "less"
    ) {
      return "Value";
    } else if (rule === "minLength" || rule === "maxLength") {
      return "Length";
    } else {
      return "Pattern";
    }
  };

  const needsValueInput =
    newValidation.rule === "min" ||
    newValidation.rule === "max" ||
    newValidation.rule === "minLength" ||
    newValidation.rule === "maxLength" ||
    newValidation.rule === "pattern" ||
    newValidation.rule === "greater" ||
    newValidation.rule === "less";

  const needsExpressionInput = newValidation.rule === "expression";

  const isDateRule =
    (component.type === "date" || component.type === "datetime") &&
    (newValidation.rule === "min" ||
      newValidation.rule === "max" ||
      newValidation.rule === "greater" ||
      newValidation.rule === "less");

  const canReferenceField =
    newValidation.rule === "greater" || newValidation.rule === "less";

  useEffect(() => {
    if (!canReferenceField) {
      setValueType("value");
    }
  }, [newValidation.rule, canReferenceField]);


  const referenceOptions = allComponents?.filter(
    (c) =>
      c.id !== component.id &&
      (c.type === "date" || c.type === "datetime")
  );

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium">Validation Rules</h4>

      </div>


      <div className="grid gap-2">
        {newValidation.rule === 'pattern' && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Cheat Sheet</CardTitle>
              <CardDescription>Quick reference guide for common regex syntax and modifiers.</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <ul className="list-disc pl-4 grid gap-2">
                  <li>^ Start of string</li>
                  <li>$ End of string</li>
                  <li>\b Word boundary</li>
                  <li>\d Digit</li>
                  <li>\D Not a digit</li>
                  <li>\w Word character</li>
                  <li>\W Not a word character</li>
                  <li>\s Whitespace</li>
                  <li>\S Not whitespace</li>
                </ul>
                <ul className="list-disc pl-4 grid gap-2">
                  <li>* Zero or more</li>
                  <li>+ One or more</li>
                  <li>? Zero or one</li>
                  <li>( ) Capturing group</li>
                  <li>[ ] Character class</li>
                  <li>n Exactly n times</li>
                  <li>n, At least n times</li>
                  <li>n,m Between n and m times</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        )}
        <div className="grid grid-cols-2 gap-2">
          <div>
            <Label htmlFor="validationRule">Rule Type</Label>
            <Select
              value={newValidation.rule}
              onValueChange={(value) =>
                setNewValidation({
                  ...newValidation,
                  rule: value as ValidationRule,
                })
              }
            >
              <SelectTrigger id="validationRule">
                <SelectValue placeholder="Select rule" />
              </SelectTrigger>
              <SelectContent>
                {availableRules.map((rule) => (
                  <SelectItem key={rule} value={rule}>
                    {rule.charAt(0).toUpperCase() + rule.slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {isDateRule && canReferenceField && (
            <div>
              <Label>Value Type</Label>
              <Select
                value={valueType}
                onValueChange={(v) => setValueType(v as "value" | "reference")}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="value">Value</SelectItem>
                  <SelectItem value="reference">Field Reference</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}
        </div>

        {needsValueInput && valueType === "value" && (
          <div>
            <Label htmlFor="validationValue">
              {getValidationValueLabel(newValidation.rule)}
            </Label>
            <Input
              id="validationValue"
              type={
                isDateRule
                  ? component.type === "datetime"
                    ? "datetime-local"
                    : "date"
                  : newValidation.rule === "minLength" ||
                    newValidation.rule === "maxLength"
                    ? "number"
                    : "text"
              }
              value={newValidation.value}
              onChange={(e) =>
                setNewValidation({ ...newValidation, value: e.target.value })
              }
              placeholder={
                newValidation.rule === "pattern"
                  ? "Regular expression"
                  : "Enter value"
              }
            />
          </div>
        )}

        {needsValueInput && valueType === "reference" && referenceOptions && (
          <div>
            <Label htmlFor="validationReference">Field Reference</Label>
            <Select
              value={newValidation.reference}
              onValueChange={(v) =>
                setNewValidation({ ...newValidation, reference: v })
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a field" />
              </SelectTrigger>
              <SelectContent>
                {referenceOptions.map((opt) => (
                  <SelectItem key={opt.id} value={opt.name}>
                    {opt.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {needsExpressionInput && (
          <div className="space-y-2">
            <Label htmlFor="validationExpression">Expression</Label>
            <Textarea
              id="validationExpression"
              value={newValidation.expression}
              onChange={(e) =>
                setNewValidation({
                  ...newValidation,
                  expression: e.target.value,
                })
              }
              placeholder="e.g., SELF > 100"
            />
            <Alert>
              <Code className="h-4 w-4" />
              <AlertTitle>How to write validation expressions</AlertTitle>
              <AlertDescription>
                <p>
                  Use <code>SELF</code> to reference the current field's value.
                </p>
                <ul className="list-disc list-inside space-y-1 mt-2">
                  <li>
                    Check for a positive value: <code>{"SELF > 0"}</code>
                  </li>
                  <li>
                    Compare with another field:{" "}
                    <code>{"SELF <= fieldId1"}</code>
                  </li>
                  <li>
                    Compare with a grid cell: <code>{"SELF <= grid1.A5"}</code>
                  </li>
                  <li>
                    Complex expression:{" "}
                    <code>SELF == SUM(grid1.B1:B5) * 0.2</code>
                  </li>
                </ul>
              </AlertDescription>
            </Alert>
          </div>
        )}


        <div>
          <Label htmlFor="validationMessage">Error Message</Label>
          <Input
            id="validationMessage"
            value={newValidation.message}
            onChange={(e) =>
              setNewValidation({ ...newValidation, message: e.target.value })
            }
            placeholder="Error message to display"
          />
        </div>
        <div className="flex justify-end mt-4">
          <Button
            variant="outline"
            size="sm"
            onClick={handleAddValidation}
            disabled={
              !newValidation.message ||
              (needsValueInput &&
                valueType === "value" &&
                !newValidation.value) ||
              (needsValueInput &&
                valueType === "reference" &&
                !newValidation.reference) ||
              (needsExpressionInput && !newValidation.expression)
            }
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Rule
          </Button>
        </div>
      </div>

      {validations.length === 0 ? (
        <p className="text-center text-sm text-muted-foreground">
          No validation rules added yet
        </p>
      ) : (
        <div className="space-y-2">
          {validations.map((validation, index) => (
            <Card key={`validation-${validation.rule}-${index}`}>
              <CardContent className="flex items-center p-2">
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">
                      {validation.rule.charAt(0).toUpperCase() +
                        validation.rule.slice(1)}
                    </span>
                    {validation.value !== undefined && (
                      <span className="text-sm text-muted-foreground">
                        {String(validation.value)}
                      </span>
                    )}
                    {validation.reference && (
                      <span className="text-sm text-muted-foreground">
                        Ref: {validation.reference}
                      </span>
                    )}
                    {validation.expression && (
                      <span className="text-sm text-muted-foreground">
                        {validation.expression}
                      </span>
                    )}
                  </div>
                  <p className="text-sm">{validation.message}</p>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handleRemoveValidation(index)}
                  className="ml-2"
                  aria-label="Remove validation rule"
                >
                  <X className="h-4 w-4" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}

export default ValidationEditor;
