import { useEffect, useState } from "react";
import { DynamicPage } from "@/components/dynamic-page/DynamicPage";
import { createListPageConfig } from "@/lib/config/entity-config-registry";
import entityInitializer from "@/lib/services/entity-initializer";


export default function FormsList() {
    // Use state to store the config to ensure it's only created once
    const [formsListConfig] = useState(() => createListPageConfig("Form"));

    // Toast hook for testing

    // Track initialization state
    const [isInitialized, setIsInitialized] = useState(false);

    // Initialize entity storage only once on component mount
    useEffect(() => {
        entityInitializer.initializeEntityStorage();
        setIsInitialized(true);
    }, []);

    if (!formsListConfig) {
        return (
            <div className="p-8 text-center">
                <h2 className="text-2xl font-bold mb-4">Configuration Error</h2>
                <p className="text-muted-foreground">
                    Could not load the configuration for Form entity.
                </p>
            </div>
        );
    }

    // Show loading state until initialization is complete
    if (!isInitialized) {
        return (
            <div className="p-8 text-center">
                <h2 className="text-2xl font-bold mb-4">Loading Data</h2>
                <p className="text-muted-foreground">Initializing project data...</p>
            </div>
        );
    }

    return (
        <div>
            <DynamicPage config={formsListConfig} />

        </div>
    );
}
