import { useState, use<PERSON><PERSON>back, useEffect, lazy, Suspense } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { FormSchema, FormComponent } from "@/lib/schemas/form-schemas";
import {
  Copy,
  Download,
  Upload,
  Check,
  AlertCircle,
  Edit,
  Save,
  X,
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loading } from "@/components/ui/loading";

// Lazy load heavy dependencies
const SyntaxHighlighter = lazy(() =>
  import("react-syntax-highlighter").then((module) => ({
    default: module.Prism,
  }))
);

// Lazy load the style
const loadVscDarkPlus = () =>
  import("react-syntax-highlighter/dist/esm/styles/prism").then(
    (module) => module.vscDarkPlus
  );

// Lazy load the editor
const CodeEditor = lazy(() =>
  import("react-simple-code-editor").then((module) => ({
    default: module.default,
  }))
);

// Lazy load prismjs and JSON language
const loadPrismHighlight = () =>
  import("prismjs").then((module) => {
    // Dynamically import the JSON language after Prism is loaded
    return import("prismjs/components/prism-json").then(() => ({
      highlight: module.highlight,
      languages: module.languages,
    }));
  });

interface JsonViewerProps {
  schema: FormSchema;
  onSchemaImport: (components: FormComponent[]) => void;
  onFullSchemaImport?: (schema: Partial<FormSchema>) => void;
}

export default function JsonViewer({
  schema,
  onSchemaImport,
  onFullSchemaImport,
}: Readonly<JsonViewerProps>) {
  const [copied, setCopied] = useState(false);
  const [importDialogOpen, setImportDialogOpen] = useState(false);
  const [importError, setImportError] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [jsonValue, setJsonValue] = useState<string>("");
  const [editError, setEditError] = useState<string | null>(null);

  // Initialize JSON value when schema changes
  useEffect(() => {
    setJsonValue(JSON.stringify(schema, null, 2));
  }, [schema]);

  // State for lazy-loaded modules
  const [prismHighlight, setPrismHighlight] = useState<any>(null);
  const [vscDarkPlus, setVscDarkPlus] = useState<any>(null);

  // Load dependencies
  useEffect(() => {
    // Load syntax highlighting
    loadPrismHighlight().then((module) => {
      setPrismHighlight(module);
    });

    // Load style
    loadVscDarkPlus().then((style) => {
      setVscDarkPlus(style);
    });
  }, []);

  // Comprehensive validation function
  const validateJsonSchema = useCallback((jsonData: any): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    // Check required keys
    const requiredKeys = ['id', 'name', 'description', 'status', 'components', 'formType', 'projectType', 'version'];
    for (const key of requiredKeys) {
      if (!(key in jsonData)) {
        errors.push(`Missing required field: ${key}`);
      }
    }

    // Validate specific field values
    if (jsonData.id !== undefined && typeof jsonData.id !== 'string') {
      errors.push('ID must be a string');
    }

    if (jsonData.name !== undefined && (typeof jsonData.name !== 'string' || !jsonData.name.trim())) {
      errors.push('Name must be a non-empty string');
    }

    if (jsonData.description !== undefined && typeof jsonData.description !== 'string') {
      errors.push('Description must be a string');
    }

    if (jsonData.version !== undefined && typeof jsonData.version !== 'string') {
      errors.push('Version must be a string');
    }

    // Validate components array
    if (!Array.isArray(jsonData.components)) {
      errors.push('Components must be an array');
    }

    // Validate formType
    if (jsonData.formType !== undefined && !['APPLICATION', 'MR'].includes(jsonData.formType)) {
      errors.push('FormType must be either "APPLICATION" or "MR"');
    }

    // Validate projectType
    if (jsonData.projectType !== undefined && !['CAPITAL', 'REVENUE'].includes(jsonData.projectType)) {
      errors.push('ProjectType must be either "CAPITAL" or "REVENUE"');
    }

    // Validate status
    if (jsonData.status !== undefined && !['DRAFT', 'ACTIVE'].includes(jsonData.status)) {
      errors.push('Status must be either "DRAFT" or "ACTIVE"');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }, []);

  // Copy JSON to clipboard
  const handleCopy = useCallback(() => {
    navigator.clipboard.writeText(jsonValue);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  }, [jsonValue]);

  // Download JSON file
  const handleDownload = useCallback(() => {
    const blob = new Blob([jsonValue], { type: "application/json" });
    const url = URL.createObjectURL(blob);

    const a = document.createElement("a");
    a.href = url;
    a.download = `${schema.name
      .toLowerCase()
      .replace(/\s+/g, "-")}-schema.json`;
    document.body.appendChild(a);
    a.click();

    // Clean up
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [jsonValue, schema.name]);

  // Handle file import
  const handleFileChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setImportError(null);
      const file = e.target.files?.[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = (event) => {
        try {
          const jsonData = JSON.parse(event.target?.result as string);

          // Comprehensive validation
          const validation = validateJsonSchema(jsonData);
          if (!validation.isValid) {
            setImportError(
              `Invalid schema format:\n${validation.errors.join('\n')}`
            );
            return;
          }

          // Update components
          onSchemaImport(jsonData.components);

          // Update form metadata if callback is provided
          if (onFullSchemaImport) {
            const { components, ...metadata } = jsonData;
            onFullSchemaImport(metadata);
          }

          setImportDialogOpen(false);
        } catch (error: unknown) {
          console.error("Error parsing JSON file:", error);
          setImportError(
            "Failed to parse JSON file. Please ensure it's a valid JSON."
          );
        }
      };

      reader.readAsText(file);
    },
    [onSchemaImport, onFullSchemaImport, validateJsonSchema]
  );

  // Toggle edit mode
  const handleToggleEdit = useCallback(() => {
    if (isEditing) {
      // If we're currently editing, try to apply changes
      try {
        const parsedJson = JSON.parse(jsonValue);

        // Comprehensive validation
        const validation = validateJsonSchema(parsedJson);
        if (!validation.isValid) {
          setEditError(
            `Invalid schema format:\n${validation.errors.join('\n')}`
          );
          return;
        }

        // Update components
        onSchemaImport(parsedJson.components);

        // Update form metadata if callback is provided
        if (onFullSchemaImport) {
          const { components, ...metadata } = parsedJson;
          onFullSchemaImport(metadata);
        }

        setEditError(null);
        setIsEditing(false);
      } catch (error: unknown) {
        console.error("Error parsing JSON:", error);
        setEditError("Invalid JSON format. Please check your syntax.");
        return;
      }
    } else {
      // Enter edit mode
      setIsEditing(true);
      setEditError(null);
    }
  }, [isEditing, jsonValue, onSchemaImport, onFullSchemaImport, validateJsonSchema]);

  // Cancel editing
  const handleCancelEdit = useCallback(() => {
    setJsonValue(JSON.stringify(schema, null, 2));
    setIsEditing(false);
    setEditError(null);
  }, [schema]);

  return (
    <div className="space-y-4">
      <div className="flex justify-end space-x-2 mb-2">
        {isEditing ? (
          <>
            <Button
              variant="outline"
              size="sm"
              onClick={handleToggleEdit}
              className="flex items-center gap-1"
            >
              <Save className="h-4 w-4" />
              Save
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleCancelEdit}
              className="flex items-center gap-1"
            >
              <X className="h-4 w-4" />
              Cancel
            </Button>
          </>
        ) : (
          <>
            <Button
              variant="outline"
              size="sm"
              onClick={handleCopy}
              className="flex items-center gap-1"
            >
              {copied ? (
                <Check className="h-4 w-4" />
              ) : (
                <Copy className="h-4 w-4" />
              )}
              {copied ? "Copied!" : "Copy"}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleDownload}
              className="flex items-center gap-1"
            >
              <Download className="h-4 w-4" />
              Download
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleToggleEdit}
              className="flex items-center gap-1"
            >
              <Edit className="h-4 w-4" />
              Edit
            </Button>
            <Dialog open={importDialogOpen} onOpenChange={setImportDialogOpen}>
              <DialogTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1"
                >
                  <Upload className="h-4 w-4" />
                  Import
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle>Import JSON Schema</DialogTitle>
                  <DialogDescription>
                    Upload a JSON file containing a valid form schema.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="schema-file">Schema File</Label>
                    <Input
                      id="schema-file"
                      type="file"
                      accept=".json"
                      onChange={handleFileChange}
                    />
                  </div>
                  {importError && (
                    <div className="flex items-start text-destructive text-sm gap-1">
                      <AlertCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
                      <div className="whitespace-pre-line">{importError}</div>
                    </div>
                  )}
                </div>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setImportDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </>
        )}
      </div>

      {editError && (
        <div className="flex items-start text-destructive text-sm gap-1 mb-2">
          <AlertCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
          <div className="whitespace-pre-line">{editError}</div>
        </div>
      )}

      <div className="rounded-md border overflow-hidden">
        {isEditing ? (
          <Suspense fallback={<Loading message="Loading editor..." />}>
            {prismHighlight ? (
              <CodeEditor
                value={jsonValue}
                onValueChange={setJsonValue}
                highlight={(code) =>
                  prismHighlight.highlight(
                    code,
                    prismHighlight.languages.json,
                    "json"
                  )
                }
                padding={10}
                style={{
                  fontFamily: '"Fira code", "Fira Mono", monospace',
                  fontSize: 14,
                  backgroundColor: "#1E1E1E",
                  color: "#D4D4D4",
                  minHeight: "500px",
                }}
                className="min-h-[500px]"
              />
            ) : (
              <Loading message="Loading editor..." />
            )}
          </Suspense>
        ) : (
          <Suspense
            fallback={<Loading message="Loading syntax highlighter..." />}
          >
            {vscDarkPlus ? (
              <SyntaxHighlighter
                language="json"
                style={vscDarkPlus}
                customStyle={{
                  margin: 0,
                  maxHeight: "500px",
                  fontSize: "0.875rem",
                }}
                showLineNumbers
              >
                {jsonValue}
              </SyntaxHighlighter>
            ) : (
              <Loading message="Loading syntax highlighter..." />
            )}
          </Suspense>
        )}
      </div>
    </div>
  );
}
