import { FormSchema } from "@/lib/schemas/form-schemas";
import { Label } from "@/components/ui/label";
import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { AlertCircle } from "lucide-react";
import { FormProvider } from "react-hook-form";
import { FormStateProvider, useFormState } from "@/contexts/FormStateContext";
import { FormSubmission } from "@/lib/types/submission";
import { useEffect } from "react";
import { useToast } from "@/hooks/use-toast";

// Import custom hooks
import { useFormPreview } from "@/hooks/useFormPreview";
import { useStepNavigation } from "@/hooks/useStepNavigation";

// Import utility functions
import { evaluateConditionalRendering } from "@/lib/utils/component-utils";
import { getComponentValidationRules } from "@/lib/utils/zod-validation-utils";

// Import components
import RenderComponent from "./form-components/RenderComponent";
import FormStatusMessage from "./form-components/FormStatusMessage";
import StepProgress from "./form-components/StepProgress";
import StepNavigation from "./form-components/StepNavigation";

interface FormPreviewProps {
  readonly schema: FormSchema;
}

function FormPreviewContent({ schema }: FormPreviewProps) {
  const { toast } = useToast();
  const {
    methods,
    formStatus,
    setFormStatus,
    onSubmit,
    steps,
    isMultiStep,
  } = useFormPreview(schema);

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    setValue,
    watch,
  } = methods;

  const { currentStep, currentStepData, nextStep, prevStep } =
    useStepNavigation({
      steps,
      methods,
      setFormStatus,
    });

  const onError = (errors: any) => {
    toast({
      title: "Validation errors",
      description: "Please fix the errors in the form before submitting.",
      variant: "destructive",
    });

    // Scroll to the first error
    const firstErrorKey = Object.keys(errors)[0];
    if (firstErrorKey) {
      const firstErrorElement = document.getElementsByName(firstErrorKey)[0];
      if (firstErrorElement) {
        firstErrorElement.scrollIntoView({
          behavior: "smooth",
          block: "center",
        });
      }
    }
  };

  const handleFormSubmit = async (data: Record<string, any>) => {
    const { isValid, errors } = await onSubmit(data);
    if (!isValid) {
      onError(errors);
    }
  };

  const {
    dispatch,
    state: { calculatedValues },
  } = useFormState();

  // Sync react-hook-form state with FormStateContext
  useEffect(() => {
    const subscription = watch((value, { name }) => {
      if (name) {
        dispatch({
          type: "UPDATE_VALUE",
          payload: { name, value: value[name] },
        });
      }
    });
    return () => subscription.unsubscribe();
  }, [watch, dispatch]);


  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{schema.name}</CardTitle>
        {schema.description && (
          <p className="text-muted-foreground">{schema.description}</p>
        )}
        {isMultiStep && (
          <StepProgress
            currentStep={currentStep}
            totalSteps={steps.length}
            stepLabel={currentStepData.label}
            stepDescription={currentStepData.description}
          />
        )}
      </CardHeader>
      <CardContent>
        <FormProvider {...methods}>
          <form
            onSubmit={handleSubmit(handleFormSubmit, onError)}
            className="space-y-6"
          >
            <FormStatusMessage
              isSubmitted={formStatus.isSubmitted}
              isValid={formStatus.isValid}
              message={formStatus.message}
            />
            {currentStepData.components.map((component) => {
              const shouldRender = evaluateConditionalRendering(
                component,
                watch
              );
              if (!shouldRender) {
                return null;
              }
              return (
                <div key={component.id} className="space-y-2">
                  <div className="space-y-1">
                    {component.type !== "step" &&
                      component.type !== "section" &&
                      component.type !== "infoText" && (
                        <Label
                          htmlFor={component.id}
                          className="flex items-center gap-1"
                        >
                          {component.label}
                          {component.required && (
                            <span className="text-destructive">*</span>
                          )}
                        </Label>
                      )}
                    <RenderComponent
                      component={component}
                      register={register}
                      control={control}
                      errors={errors}
                      setValue={setValue}
                      watch={watch}
                      validationRules={getComponentValidationRules(component)}
                      allComponents={schema.components}
                      mode="preview"
                      calculatedValues={calculatedValues}
                    />
                    {errors[component.name] && (
                      <div className="text-sm text-destructive flex items-center gap-1 mt-1">
                        <AlertCircle className="h-4 w-4" />
                        <span>
                          {errors[component.name]?.message as string}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
            <StepNavigation
              currentStep={currentStep}
              totalSteps={steps.length}
              onNext={nextStep}
              onPrevious={prevStep}
              isMultiStep={isMultiStep}
              onSubmit={handleSubmit(handleFormSubmit, onError)}
            />
          </form>
        </FormProvider>
      </CardContent>
      <CardFooter className="text-center text-sm text-muted-foreground">
        This is a preview of how your form will appear to users
      </CardFooter>
    </Card>
  );
}

export default function FormPreview({ schema }: FormPreviewProps) {
  if (schema.components.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-10 text-center">
        <p className="text-muted-foreground">
          No components added yet. Add components to preview the form.
        </p>
      </div>
    );
  }

  const initialSubmission: FormSubmission = {
    id: "preview-submission",
    formId: schema.id,
    version: schema.version,
    data: {},
    status: "DRAFT",
    formSchema: schema,
  };

  return (
    <FormStateProvider
      initialForm={schema}
      initialSubmission={initialSubmission}
    >
      <FormPreviewContent schema={schema} />
    </FormStateProvider>
  );
}
