default:
  tags:
    - scheme-manager-frontend-runner

stages:
  - test
  - sonar
  - docker-build
  - local-deploy
  - push-ecr
  - kube-deploy

variables:
  APP_PORT: "3000"
  IMAGE_TAG: "$CI_COMMIT_SHORT_SHA"
  NODE_VERSION: "20"
  DOCKER_HOST: unix:///var/run/docker.sock
  DOCKER_TLS_CERTDIR: ""
  DOCKER_BUILDKIT: "1"
  QA_ECR_REPO: "619403130511.dkr.ecr.eu-west-2.amazonaws.com/scheme-management-frontend-qa"
  PREPROD_ECR_REPO: "619403130511.dkr.ecr.eu-west-2.amazonaws.com/scheme-management-frontend-preprod"

sonarqube-check:
  stage: sonar
  image: 
    name: sonarsource/sonar-scanner-cli:latest
    entrypoint: [""]
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
    GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script:
      - sonar-scanner
  rules:
      - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"'
  allow_failure: true

# Uncomment and use this if you want frontend tests
# test:
#   stage: test
#   image: node:${NODE_VERSION}-alpine
#   cache:
#     key:
#       files:
#         - package-lock.json
#     paths:
#       - node_modules/
#   script:
#     - npm ci
#     - npm run lint
#     - npm run build
#   rules:
#     - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"'

local-deploy:
  stage: local-deploy
  image: docker:24.0.2
  services:
    - docker:24.0.2-dind
  script:
    - export VITE_API_MODE=${VITE_API_MODE:-real}
    - export VITE_API_BASE_URL=${VITE_API_BASE_URL:-http://**********:8080/api/v1}
    - export VITE_COGNITO_HOSTED_UI_URL=${VITE_COGNITO_HOSTED_UI_URL}
    - export VITE_COGNITO_CLIENT_ID=${VITE_COGNITO_CLIENT_ID}
    - export VITE_COGNITO_USER_POOL_ID=${VITE_COGNITO_USER_POOL_ID}
    - export VITE_COGNITO_REGION=${VITE_COGNITO_REGION}
    - export VITE_COGNITO_REDIRECT_URI=${VITE_COGNITO_REDIRECT_URI:-http://localhost:3000/callback}
    - export VITE_SESSION_INACTIVITY_TIMEOUT_MINUTES=${VITE_SESSION_INACTIVITY_TIMEOUT_MINUTES:-30}
    - export VITE_SESSION_WARNING_TIME_MINUTES=${VITE_SESSION_WARNING_TIME_MINUTES:-5}
    - export VITE_BLACK_LOGO_URL=${VITE_BLACK_LOGO_URL}
    - export VITE_WHITE_LOGO_URL=${VITE_WHITE_LOGO_URL}
    - docker-compose down || true
    - docker-compose --profile production up -d --build app-prod
  after_script:
    - docker-compose ps
    - docker-compose logs --tail=50 app
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'

push-ecr-qa:
  stage: push-ecr
  script:
    - docker build --target production --build-arg APP_PORT=$APP_PORT -t scheme-management-frontend:$IMAGE_TAG .
    - mkdir -p ~/.aws
    - echo "[default]" > ~/.aws/config
    - echo "region = $AWS_REGION" >> ~/.aws/config
    - echo "[default]" > ~/.aws/credentials
    - echo "aws_access_key_id = $AWS_ACCESS_KEY_ID" >> ~/.aws/credentials
    - echo "aws_secret_access_key = $AWS_SECRET_ACCESS_KEY" >> ~/.aws/credentials
    - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $QA_ECR_REPO
    - docker tag scheme-management-frontend:$IMAGE_TAG $QA_ECR_REPO:$IMAGE_TAG
    - docker push $QA_ECR_REPO:$IMAGE_TAG
  rules:
    - if: '$CI_COMMIT_BRANCH == "integration"'

push-ecr-preprod:
  stage: push-ecr
  script:
    - docker build --target production --build-arg APP_PORT=$APP_PORT -t scheme-management-frontend:$IMAGE_TAG .
    - mkdir -p ~/.aws
    - echo "[default]" > ~/.aws/config
    - echo "region = $AWS_REGION" >> ~/.aws/config
    - echo "[default]" > ~/.aws/credentials
    - echo "aws_access_key_id = $AWS_ACCESS_KEY_ID" >> ~/.aws/credentials
    - echo "aws_secret_access_key = $AWS_SECRET_ACCESS_KEY" >> ~/.aws/credentials
    - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $PREPROD_ECR_REPO
    - docker tag scheme-management-frontend:$IMAGE_TAG $PREPROD_ECR_REPO:$IMAGE_TAG
    - docker push $PREPROD_ECR_REPO:$IMAGE_TAG
  rules:
    - if: '$CI_COMMIT_BRANCH == "preprod"'

kube-deploy-qa:
  stage: kube-deploy
  image: bitnami/kubectl:latest
  script:
    - export QA_K8S_TOKEN=$(echo "$QA_K8S_TOKEN" | tr -d '\n' | tr -d '\r')
    - kubectl config set-cluster cluster --server=$QA_K8S_SERVER --insecure-skip-tls-verify=true
    - kubectl config set-credentials deployer --token="$QA_K8S_TOKEN"
    - kubectl config set-context deploy-context --cluster=cluster --user=deployer --namespace=qa-frontend
    - kubectl config use-context deploy-context
    - kubectl set image deployment/frontend frontend=$QA_ECR_REPO:$IMAGE_TAG --namespace=qa-frontend
  only:
    - integration
  when: manual
  environment:
    name: qa-frontend
  needs:
    - job: push-ecr-qa

kube-deploy-preprod:
  stage: kube-deploy
  image: bitnami/kubectl:latest
  script:
    - export PREPROD_K8S_TOKEN=$(echo "$PREPROD_K8S_TOKEN" | tr -d '\n' | tr -d '\r')
    - kubectl config set-cluster cluster --server=$PREPROD_K8S_SERVER --insecure-skip-tls-verify=true
    - kubectl config set-credentials deployer --token="$PREPROD_K8S_TOKEN"
    - kubectl config set-context deploy-context --cluster=cluster --user=deployer --namespace=preprod-frontend
    - kubectl config use-context deploy-context
    - kubectl set image deployment/frontend frontend=$PREPROD_ECR_REPO:$IMAGE_TAG --namespace=preprod-frontend
  only:
    - preprod
  when: manual
  environment:
    name: preprod-frontend
  needs:
    - job: push-ecr-preprod
