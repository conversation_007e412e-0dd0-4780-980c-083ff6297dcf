/**
 * Styles for the data table component
 */

export const dataTableStyles = `
  :root {
    --primary-5: rgba(54, 178, 61, 0.05);
    --primary-10: rgba(54, 178, 61, 0.1);
    --primary-20: rgba(54, 178, 61, 0.2);
    --primary-30: rgba(54, 178, 61, 0.3);
  }

  .force-repaint {
    /* This property doesn't affect appearance but forces a repaint */
    transform: translateZ(0);
  }

  /* More elegant hover effect for pinned columns */
  .table-cell[data-pinned="true"] {
    transition: box-shadow 0.2s ease;
  }

  /* Subtle border highlight for pinned column on hover */
  .table-cell[data-pinned="true"]:hover,
  .table-cell[data-pinned="true"].column-hovered {
    box-shadow: inset 0 0 0 1px var(--primary-20), 4px 0 8px -2px rgba(0,0,0,0.15);
    background-color: var(--background) !important;
  }

  /* Add a subtle gradient to pinned columns for visual distinction */
  .table-cell[data-pinned="true"] {
    /* Solid background with subtle gradient overlay */
    background: var(--background) !important;
    background-image: linear-gradient(to right, var(--background), var(--background) 95%, rgba(54, 178, 61, 0.03) 100%) !important;
    /* Ensure proper spacing */
    margin: 0 !important;
    padding: 0 !important;
    /* Add a pseudo-element for complete coverage */
    position: relative;
  }

  /* Add a subtle right edge highlight */
  .table-cell[data-pinned="true"]::after {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 1px;
    background-color: var(--primary-10);
    opacity: 0.5;
    z-index: 2;
  }

  /* Ensure no gaps between rows */
  .table-row {
    border-collapse: collapse !important;
  }

  /* Ensure content has proper styling */
  .pinned-cell-content {
    position: relative;
    z-index: 1;
    width: 100%;
    transition: all 0.15s ease;
    padding: 8px !important;
  }

  /* Ensure pinned columns don't overflow their container */
  .table-container {
    overflow-x: auto;
    position: relative;
    isolation: isolate;
    border-collapse: collapse !important;
    /* Ensure no gaps in the table */
    border-spacing: 0 !important;
  }

  /* Special styling during scrolling */
  .table-container.is-scrolling .table-cell[data-pinned="true"] {
    /* Add a subtle background during scrolling for better visibility */
    background: var(--background) !important;
  }

  /* Ensure table has no gaps */
  .table {
    border-collapse: collapse !important;
    border-spacing: 0 !important;
  }

  /* Ensure proper clipping for pinned columns */
  .table-cell[data-pinned="true"] {
    overflow: hidden !important;
    /* Extend the clip path by 1px in each direction to prevent gaps */
    clip-path: inset(-1px) !important;
    /* Ensure no gaps between cells */
    padding: 0 !important;
    margin: 0 !important;
  }

  /* Enhanced input styling */
  input[type="text"]:focus::placeholder,
  input[type="search"]:focus::placeholder {
    color: var(--primary-20);
    opacity: 0.7;
  }

  /* Smooth transition for search inputs */
  .group:hover .search-icon {
    color: var(--primary-30);
  }

  /* Add subtle animation for filter inputs */
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(-2px); }
    to { opacity: 1; transform: translateY(0); }
  }

  .filter-input-container {
    animation: fadeIn 0.2s ease-out;
  }
`;
