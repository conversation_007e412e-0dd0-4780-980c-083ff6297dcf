import { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { UseFormReturn } from "react-hook-form";
import { FormSchema } from "@/lib/schemas/form-schemas";
import { FormApplication, FormSubmission } from "@/lib/types/submission";
import { SubmissionService } from "@/lib/services/submission-service";
import { FormService } from "@/lib/services/form-service";
import { User } from "@/lib/types/auth";
import { prepareFormDataForLoading } from "@/lib/utils/form-data-processor";
import { ErrorResponse } from "@/lib/types/api";
import { FormAction } from "@/contexts/FormStateContext";
interface UseFormDataLoaderProps {
  formId: string;
  projectRef?: string;
  submissionId?: string;
  methods: UseFormReturn;
  user: User | null;
  setFormStatus: (status: {
    isSubmitted: boolean;
    isValid: boolean;
    message: string;
  }) => void;
  dispatch: React.Dispatch<FormAction>;
}

interface UseFormDataLoaderInitialProps {
  formId?: string;
  projectRef?: string;
}

interface UseFormDataLoaderReturn {
  form: FormSchema | null;
  submission: FormSubmission | null;
  isLoading: boolean;
}

/**
 * Hook for loading form and submission data
 */
export function useFormDataLoader({
  formId,
  projectRef,
  submissionId,
  methods,
  user,
  setFormStatus,
  dispatch,
}: UseFormDataLoaderProps): UseFormDataLoaderReturn {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [formLocal, setFormLocal] = useState<FormSchema | null>(null);
  const [submissionLocal, setSubmissionLocal] = useState<FormSubmission | null>(
    null
  );

  // Use a ref to track if we've already loaded this form
  const hasLoadedRef = useRef(false);

  // Load form and submission data
  useEffect(() => {
    // Skip if we've already loaded this form
    if (hasLoadedRef.current) {
      return;
    }

    // Set the flag to prevent multiple loads
    hasLoadedRef.current = true;

    // Helper function to update form with submission data
    const updateFormWithSubmissionData = (
      submissionData: FormApplication,
      formSchema: FormSchema
    ) => {
      // Update state with submission data
      dispatch({
        type: "SET_FORM",
        payload: formSchema,
      });
      setFormLocal(formSchema);
      dispatch({
        type: "SET_SUBMISSION",
        payload: {
          id: submissionData.id,
          formId: submissionData.formId,
          projectRef: submissionData.projectRef,
          data: submissionData.formData,
          status: submissionData.status,
          formSchema,
        },
      });
      setSubmissionLocal({
        id: submissionData.id,
        formId: submissionData.formId,
        projectRef: submissionData.projectRef,
        data: submissionData.formData,
        status: submissionData.status,
        formSchema,
      });

      // Pre-populate form with existing data
      methods.reset({});
      prepareFormDataForLoading(
        submissionData.formData,
        formSchema.components,
        methods.setValue
      );

      // Force form state update
      setTimeout(() => methods.trigger(), 100);
    };

    // Load a form by ID and check for draft submissions
    const loadFormAndCheckDrafts = async () => {
      const formData = await FormService.getFormById(formId);

      if (!formData) {
        console.error("Form not found:", formId);
        navigate("/projects");
        return false;
      }

      // Update form state
      dispatch({
        type: "SET_FORM",
        payload: formData,
      });
      setFormLocal(formData);

      // Check for existing draft submissions
      await checkForExistingDrafts(formId, formData);
      return true;
    };

    // Check if user has existing draft submissions for this form
    const checkForExistingDrafts = async (
      formId: string,
      formData: FormSchema
    ) => {
      const fullSubmission = await SubmissionService.getSubmissionById(
        formId,
        projectRef
      );

      if (fullSubmission) {
        updateFormWithSubmissionData(fullSubmission, formData);
      }
    };

    // Main load data function
    const loadData = async () => {
      setIsLoading(true);

      try {
        await loadFormAndCheckDrafts();
      } catch (error) {
        const errorResponse = error as ErrorResponse;
        if (
          errorResponse.code === "404" &&
          errorResponse.details.includes("No saved application for projectRef")
        ) {
          setFormStatus({
            isSubmitted: false,
            isValid: false,
            message: "",
          });
        } else {
          setFormStatus({
            isSubmitted: true,
            isValid: false,
            message: `${errorResponse.details}`,
          });
        }
      } finally {
        setIsLoading(false);
      }
    };

    loadData();

    // Reset the flag when formId changes
    return () => {
      if (formIdRef.current !== formId) {
        hasLoadedRef.current = false;
      }
    };
  }, [formId, submissionId, user, navigate, methods, setFormStatus, dispatch]);

  // Keep track of the current formId to detect changes
  const formIdRef = useRef(formId);
  useEffect(() => {
    if (formIdRef.current !== formId) {
      hasLoadedRef.current = false;
      formIdRef.current = formId;
    }
  }, [formId]);

  return {
    form: formLocal,
    submission: submissionLocal,
    isLoading,
  };
}

export function useFormDataLoaderInitial({
  formId,
  projectRef,
}: UseFormDataLoaderInitialProps): UseFormDataLoaderReturn {
  const [isLoading, setIsLoading] = useState(true);
  const [formLocal, setFormLocal] = useState<FormSchema | null>(null);
  const [submissionLocal, setSubmissionLocal] = useState<FormSubmission | null>(
    null
  );

  const navigate = useNavigate();
  // Use a ref to track if we've already loaded this form
  const hasLoadedRef = useRef(false);

  // Load form and submission data
  useEffect(() => {
    // Skip if we've already loaded this form
    if (hasLoadedRef.current) {
      return;
    }

    // Set the flag to prevent multiple loads
    hasLoadedRef.current = true;

    // Helper function to update form with submission data
    const updateFormWithSubmissionData = (
      submissionData: FormApplication,
      formSchema: FormSchema
    ) => {
      // Update state with submission data

      setFormLocal(formSchema);
      setSubmissionLocal({
        id: submissionData.id,
        formId: submissionData.formId,
        projectRef: submissionData.projectRef,
        data: submissionData.formData,
        status: submissionData.status,
        formSchema,
      });
    };

    // Helper function to clear submission state
    const clearSubmissionState = (formData: FormSchema) => {
      setSubmissionLocal({
        id: "",
        formId: formData?.id || "",
        version: formData?.version,
        data: {},
        status: "DRAFT",
        formSchema: formData,
      });
    };

    // Load a form by ID and check for draft submissions
    const loadFormAndCheckDrafts = async () => {
      if (!formId) {
        console.error("Form not found:", formId);
        navigate("/projects");
        return false;
      }
      const formData = await FormService.getFormById(formId);

      if (!formData) {
        console.error("Form not found:", formId);
        navigate("/projects");
        return false;
      }

      setFormLocal(formData);

      // Check for existing draft submissions
      await checkForExistingDrafts(formData, formId);
      return true;
    };

    // Check if user has existing draft submissions for this form
    const checkForExistingDrafts = async (
      formData: FormSchema,
      formId?: string
    ) => {
      if (!formId) {
        return;
      }
      try {
        const fullSubmission = await SubmissionService.getSubmissionById(
          formId,
          projectRef
        );
        if (fullSubmission) {
          updateFormWithSubmissionData(fullSubmission, formData);
        } else {
          clearSubmissionState(formData);
        }
      } catch (error) {
        const errorResponse = error as ErrorResponse;
        if (
          errorResponse.code === "404" &&
          errorResponse.details.includes("No saved application for projectRef")
        ) {
          clearSubmissionState(formData);
        } else {
          throw error;
        }
      }
    };

    // Main load data function
    const loadData = async () => {
      setIsLoading(true);

      try {
        await loadFormAndCheckDrafts();
      } catch (error) {
        const errorResponse = error as ErrorResponse;
        console.error("Failed to load form:", errorResponse);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();

    // Reset the flag when formId changes
    return () => {
      if (formIdRef.current !== formId) {
        hasLoadedRef.current = false;
      }
    };
  }, [formId, navigate, projectRef, formLocal]);

  // Keep track of the current formId to detect changes
  const formIdRef = useRef(formId);
  useEffect(() => {
    if (formIdRef.current !== formId) {
      hasLoadedRef.current = false;
      formIdRef.current = formId;
    }
  }, [formId]);

  return {
    form: formLocal,
    submission: submissionLocal,
    isLoading,
  };
}
