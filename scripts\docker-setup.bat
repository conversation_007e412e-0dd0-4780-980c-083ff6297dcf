@echo off
setlocal enabledelayedexpansion

echo 🐳 HNES Form Builder Docker Setup
echo ==================================

REM Check if Docker is installed
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not installed. Please install Docker first.
    echo    Visit: https://docs.docker.com/get-docker/
    pause
    exit /b 1
)

REM Check if Docker Compose is installed
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker Compose is not installed. Please install Docker Compose first.
    echo    Visit: https://docs.docker.com/compose/install/
    pause
    exit /b 1
)

echo ✅ Docker and Docker Compose are installed

REM Check if .env.local exists
if not exist ".env.local" (
    echo 📝 Creating .env.local from template...
    copy .env.docker .env.local >nul
    echo ✅ Created .env.local file
    echo 💡 You can edit .env.local to customize environment variables
) else (
    echo ✅ .env.local already exists
)

echo.
echo What would you like to do?
echo 1^) Start development environment ^(connects to existing backend on port 8080^)
echo 2^) Start development with mock API ^(if no backend available^)
echo 3^) Start production environment
echo 4^) Stop all services
echo 5^) Clean up Docker resources
echo 6^) Exit

set /p choice="Enter your choice (1-6): "

if "%choice%"=="1" goto start_dev
if "%choice%"=="2" goto start_dev_mock
if "%choice%"=="3" goto start_prod
if "%choice%"=="4" goto stop_services
if "%choice%"=="5" goto cleanup
if "%choice%"=="6" goto exit_script
goto invalid_choice

:start_dev
echo 🚀 Starting development environment ^(connects to your existing backend on port 8080^)...
docker-compose up --build app
goto end

:start_dev_mock
echo 🚀 Starting development environment with mock API...
docker-compose --profile mock-api up --build
goto end

:start_prod
echo 🚀 Starting production environment...
docker-compose --profile production up --build app-prod
goto end

:stop_services
echo 🛑 Stopping all services...
docker-compose down
goto end

:cleanup
echo 🧹 Cleaning up Docker resources...
docker-compose down -v --rmi local
echo ✅ Cleanup complete
goto end

:invalid_choice
echo ❌ Invalid choice. Please run the script again.
pause
exit /b 1

:exit_script
echo 👋 Goodbye!
goto end

:end
pause
