import { useState, useCallback, useMemo } from "react";
import { z } from "zod";
import {
  isTextComponent,
  isNumberComponent,
  isDateComponent,
  isDateTimeComponent,
  isSelectComponent,
  isCheckboxComponent,
  isRadioComponent,
  isDataGridComponent,
  isStepComponent,
  isSectionComponent,
  isInfoTextComponent,
  createComponentValidationSchema,
} from "@/lib/utils/zod-validation-utils";

// Import types directly from form-schemas
import {
  FormComponent,
  TextComponent,
  NumberComponent,
  DateComponent,
  DateTimeComponent,
  SelectComponent,
  CheckboxComponent,
  RadioComponent,
  DataGridComponent,
  StepComponent,
  SectionComponent,
  InfoTextComponent,
} from "@/lib/schemas/form-schemas";

/**
 * Type-specific component props
 */
type ComponentTypeProps<T extends FormComponent> = {
  component: T;
  onChange: (updatedComponent: T) => void;
  value: any;
  onValueChange: (value: any) => void;
  error?: string;
};

/**
 * Generic hook for handling form components with type safety
 */
export function useTypedFormComponent<T extends FormComponent>(
  component: T,
  onChange: (updatedComponent: T) => void,
  initialValue?: any
) {
  // State for component value
  const [value, setValue] = useState<any>(
    initialValue ?? component.defaultValue ?? ""
  );
  const [error, setError] = useState<string | undefined>(undefined);

  // Create validation schema based on component type
  const validationSchema = useMemo(() => {
    return createComponentValidationSchema(component);
  }, [component]);

  // Handle value change with validation
  const handleValueChange = useCallback(
    (newValue: any) => {
      setValue(newValue);

      // Validate the new value
      try {
        validationSchema.parse(newValue);
        setError(undefined);
      } catch (err) {
        if (err instanceof z.ZodError) {
          setError(err.errors[0].message);
        }
      }
    },
    [validationSchema]
  );

  // Handle component property change
  const handlePropertyChange = useCallback(
    <K extends keyof T>(property: K, propertyValue: T[K]) => {
      onChange({
        ...component,
        [property]: propertyValue,
      } as T);
    },
    [component, onChange]
  );

  // Type-specific props based on component type
  const componentProps = useMemo(() => {
    // Base props that all component types share
    const baseProps = {
      component,
      onChange,
      value,
      onValueChange: handleValueChange,
      error,
    };

    // Return type-specific props based on component type
    if (isTextComponent(component)) {
      return baseProps as unknown as ComponentTypeProps<TextComponent>;
    } else if (isNumberComponent(component)) {
      return baseProps as unknown as ComponentTypeProps<NumberComponent>;
    } else if (isDateComponent(component)) {
      return baseProps as unknown as ComponentTypeProps<DateComponent>;
    } else if (isDateTimeComponent(component)) {
      return baseProps as unknown as ComponentTypeProps<DateTimeComponent>;
    } else if (isSelectComponent(component)) {
      return baseProps as unknown as ComponentTypeProps<SelectComponent>;
    } else if (isCheckboxComponent(component)) {
      return baseProps as unknown as ComponentTypeProps<CheckboxComponent>;
    } else if (isRadioComponent(component)) {
      return baseProps as unknown as ComponentTypeProps<RadioComponent>;
    } else if (isDataGridComponent(component)) {
      return baseProps as unknown as ComponentTypeProps<DataGridComponent>;
    } else if (isStepComponent(component)) {
      return baseProps as unknown as ComponentTypeProps<StepComponent>;
    } else if (isSectionComponent(component)) {
      return baseProps as unknown as ComponentTypeProps<SectionComponent>;
    } else if (isInfoTextComponent(component)) {
      return baseProps as unknown as ComponentTypeProps<InfoTextComponent>;
    }

    // Fallback
    return baseProps as unknown as ComponentTypeProps<T>;
  }, [component, onChange, value, handleValueChange, error]);

  return {
    value,
    setValue,
    error,
    setError,
    handleValueChange,
    handlePropertyChange,
    validationSchema,
    componentProps,
  };
}

/**
 * Type guard to check if a component is of a specific type
 */
export function isComponentOfType<T extends FormComponent["type"]>(
  component: FormComponent,
  type: T
): component is Extract<FormComponent, { type: T }> {
  return component.type === type;
}

/**
 * Type-safe component renderer props
 */
export interface TypedComponentRendererProps {
  component: FormComponent;
  onChange: (updatedComponent: FormComponent) => void;
  value: any;
  onValueChange: (value: any) => void;
  error?: string;
}
