import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { FormSchema, FormComponent } from "@/lib/schemas/form-schemas";
import { createLazyComponent } from "@/components/ui/lazy-component";

// Lazy load components for better code splitting
const FormBuilder = createLazyComponent(
  () => import("@/components/form-builder/FormBuilder")
);

const FormPreview = createLazyComponent(
  () => import("@/components/form-builder/FormPreview")
);

const JsonViewer = createLazyComponent(
  () => import("@/components/form-builder/JsonViewer")
);

interface FormTabsProps {
  schema: FormSchema;
  onComponentsChange: (components: FormComponent[]) => void;
  onSchemaMetadataChange?: (metadata: Partial<FormSchema>) => void;
  useDndContext?: boolean;
}

type TabValue = "editor" | "preview" | "json";

export default function FormTabs({
  schema,
  onComponentsChange,
  onSchemaMetadataChange,
  useDndContext = true,
}: Readonly<FormTabsProps>) {
  // Use state to control the active tab
  const [activeTab, setActiveTab] = useState<TabValue>("editor");

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value as TabValue);
  };

  return (
    <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="editor">Form Editor</TabsTrigger>
        <TabsTrigger value="preview">Preview</TabsTrigger>
        <TabsTrigger value="json">JSON</TabsTrigger>
      </TabsList>

      {/* Always render all tabs but only show the active one */}
      <TabsContent value="editor" className="mt-6 border-none p-0">
        <FormBuilder
          components={schema.components}
          onChange={onComponentsChange}
          useDndContext={useDndContext}
        />
      </TabsContent>

      <TabsContent value="preview" className="mt-6 border-none p-0">
        <FormPreview schema={schema} />
      </TabsContent>

      <TabsContent value="json" className="mt-6 border-none p-0">
        <JsonViewer
          schema={schema}
          onSchemaImport={onComponentsChange}
          onFullSchemaImport={onSchemaMetadataChange}
        />
      </TabsContent>
    </Tabs>
  );
}
