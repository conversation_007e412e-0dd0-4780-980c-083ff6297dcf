import { memo, useMemo } from "react";
import { FormComponent } from "@/lib/types/form";
import {
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import SortableComponentItem from "./SortableComponentItem";

interface ComponentListProps {
  readonly components: FormComponent[];
  readonly selectedComponentId: string | null;
  readonly expandedContainers: Record<string, boolean>;
  readonly onSelectComponent: (id: string, activateEditTab?: boolean) => void;
  readonly onDeleteComponent: (id: string) => void;
  readonly onMoveComponent: (id: string, direction: "up" | "down") => void;
  readonly onToggleExpand: (id: string) => void;
}

function ComponentList({
  components,
  selectedComponentId,
  expandedContainers,
  onSelectComponent,
  onDeleteComponent,
  onMoveComponent,
  onToggleExpand,
}: ComponentListProps) {
  // Memoize the top-level components to prevent unnecessary filtering on re-renders
  const topLevelComponents = useMemo(
    () => components.filter((component) => !component.parentId),
    [components]
  );

  // Memoize the component IDs for SortableContext to prevent unnecessary re-renders
  const componentIds = useMemo(
    () => components.map((component) => component.id),
    [components]
  );

  return (
    <SortableContext
      items={componentIds}
      strategy={verticalListSortingStrategy}
    >
      <div className="space-y-4 pb-6">
        {topLevelComponents.map((component) => (
          <SortableComponentItem
            key={component.id}
            id={component.id}
            component={component}
            allComponents={components}
            isSelected={selectedComponentId === component.id}
            onSelect={onSelectComponent}
            onDelete={onDeleteComponent}
            onMove={onMoveComponent}
            isExpanded={expandedContainers[component.id] !== false}
            onToggleExpand={onToggleExpand}
            expandedContainers={expandedContainers}
          />
        ))}
      </div>
    </SortableContext>
  );
}

// Use memo to prevent unnecessary re-renders of the entire component list
export default memo(ComponentList);
