import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { UseFormReturn } from "react-hook-form";
import { FormApplication } from "@/lib/types/submission";
import { SubmissionService } from "@/lib/services/submission-service";
import { User } from "@/lib/types/auth";
import { processFormData } from "@/lib/utils/form-data-processor";
import { ErrorResponse } from "@/lib/types/api";
import { evaluateConditionalRendering } from "@/lib/utils/component-utils";
import { organizeComponentsIntoSteps } from "@/lib/utils/form-structure-utils";
import { getComponentValidationRules } from "@/lib/utils/zod-validation-utils";
import { FormAction, FormState } from "@/contexts/FormStateContext";
import { useCalculationEngine } from "./useCalculationEngine";
import { validateAllCells } from "./useDataGridValidation";
import { indicesToExcel } from "@/lib/utils/grid-utils";

interface UseFormSubmissionOperationsProps {
  projectRef?: string;
  state: FormState;
  dispatch: React.Dispatch<FormAction>;
  methods: UseFormReturn;
  user: User | null;
  setFormStatus: (status: {
    isSubmitted: boolean;
    isValid: boolean;
    message: string;
  }) => void;
  commitCurrentStepData: () => Promise<boolean>;
}

interface UseFormSubmissionOperationsReturn {
  isSaving: boolean;
  isSubmitting: boolean;
  saveProgress: () => Promise<boolean>;
  submitForm: () => Promise<boolean>;
  getProcessData: () => Promise<Record<string, any>>;
}

/**
 * Hook for form submission operations (save and submit)
 */
export function useFormSubmissionOperations({
  state,
  methods,
  projectRef,
  user,
  setFormStatus,
  commitCurrentStepData,
  dispatch,
}: UseFormSubmissionOperationsProps): UseFormSubmissionOperationsReturn {
  const navigate = useNavigate();
  const [isSaving, setIsSaving] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { evaluateValidationExpression } = useCalculationEngine(
    state.form,
    methods.getValues()
  );

  /**
   * Convert structured DataGrid format to flat format for validation
   * Now uses custom cell IDs instead of Excel coordinates
   */
  const convertStructuredToFlat = (
    structuredValue: any,
    component?: any
  ): Record<string, any> => {
    const flatValue: Record<string, any> = {};

    if (!structuredValue.rows || !Array.isArray(structuredValue.rows)) {
      return flatValue;
    }

    // Get column headers from metadata
    const columnHeaders = structuredValue.metadata?.columnHeaders || [];

    structuredValue.rows.forEach((row: any, rowIndex: number) => {
      if (row.cells && typeof row.cells === "object") {
        // For each cell in the row
        Object.keys(row.cells).forEach((columnHeader) => {
          const cell = row.cells[columnHeader];

          // Find the column index based on the column header
          const colIndex = columnHeaders.indexOf(columnHeader);

          if (colIndex >= 0) {
            // Convert to Excel-style cell ID first
            // rowIndex + 1 because we skip the header row (row 0)
            // colIndex + 1 because we skip the header column (col 0)
            const excelCellId = indicesToExcel(rowIndex + 1, colIndex + 1);

            // Get custom cell ID if component is provided, otherwise use Excel coordinate
            let cellId = excelCellId;
            if (component?.cells?.[excelCellId]) {
              cellId = component.cells[excelCellId].id || excelCellId;
            }

            flatValue[cellId] = cell.value || "";
          }
        });
      }
    });

    return flatValue;
  };

  /**
   * Validate a single field using its validation rules
   */
  const validateField = (
    component: any,
    value: any
  ): { isValid: boolean; error?: string } => {
    try {
      // Handle DataGrid components specially
      if (component.type === "datagrid" && value) {
        const flatValue =
          value?.flat || convertStructuredToFlat(value, component);
        const errors = validateAllCells(
          component,
          flatValue,
          methods.getValues(),
          evaluateValidationExpression
        );
        const isValid = Object.keys(errors).length === 0;
        return {
          isValid,
          error: isValid
            ? undefined
            : Object.entries(errors)
                .map(([key, message]) => `Cell ${key}: ${message}`)
                .join(", "),
        };
      }

      const validationRules = getComponentValidationRules(component);

      // Check required validation
      if (
        validationRules.required &&
        (value === undefined || value === null || value === "")
      ) {
        return {
          isValid: false,
          error: `${component.label || component.name} is required`,
        };
      }

      // Check pattern validation
      if (validationRules.pattern && value) {
        // Special handling for email validation with fallback
        if (
          validationRules.pattern.message &&
          validationRules.pattern.message.toLowerCase().includes("email")
        ) {
          // Use a more robust email validation
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(value)) {
            return {
              isValid: false,
              error:
                validationRules.pattern.message ||
                "Please enter a valid email address",
            };
          }
        } else {
          // Regular pattern validation
          if (!validationRules.pattern.value.test(value)) {
            return {
              isValid: false,
              error:
                validationRules.pattern.message ||
                `${component.label || component.name} format is invalid`,
            };
          }
        }
      }

      // Check min length validation
      if (
        validationRules.minLength &&
        value &&
        value.length < validationRules.minLength.value
      ) {
        return {
          isValid: false,
          error:
            validationRules.minLength.message ||
            `${component.label || component.name} must be at least ${
              validationRules.minLength.value
            } characters`,
        };
      }

      // Check max length validation
      if (
        validationRules.maxLength &&
        value &&
        value.length > validationRules.maxLength.value
      ) {
        return {
          isValid: false,
          error:
            validationRules.maxLength.message ||
            `${component.label || component.name} must be no more than ${
              validationRules.maxLength.value
            } characters`,
        };
      }

      // Check min value validation
      if (
        validationRules.min &&
        value !== undefined &&
        value !== null &&
        Number(value) < validationRules.min.value
      ) {
        return {
          isValid: false,
          error:
            validationRules.min.message ||
            `${component.label || component.name} must be at least ${
              validationRules.min.value
            }`,
        };
      }

      // Check max value validation
      if (
        validationRules.max &&
        value !== undefined &&
        value !== null &&
        Number(value) > validationRules.max.value
      ) {
        return {
          isValid: false,
          error:
            validationRules.max.message ||
            `${component.label || component.name} must be no more than ${
              validationRules.max.value
            }`,
        };
      }

      return { isValid: true };
    } catch (error) {
      console.warn(`Error validating field ${component.name}:`, error);
      return { isValid: true }; // If validation fails, assume valid to avoid blocking submission
    }
  };

  /**
   * Validate all form steps by checking each step's fields using schema validation
   */
  const validateAllSteps = async (): Promise<{
    isValid: boolean;
    errors: any;
    invalidSteps: number[];
  }> => {
    if (!state.form) return { isValid: true, errors: {}, invalidSteps: [] };

    // Get all steps from form structure
    const steps = organizeComponentsIntoSteps(state.form);
    const allErrors: any = {};
    const invalidSteps: number[] = [];

    // Get current form values
    const formValues = methods.getValues();
    const { watch } = methods;

    // Validate each step
    for (let stepIndex = 0; stepIndex < steps.length; stepIndex++) {
      const step = steps[stepIndex];
      let stepHasErrors = false;

      // Get all components for this step (including nested ones)
      const allStepComponents = [...step.components];

      // Add child components from sections
      step.components
        .filter((component) => component.type === "section")
        .forEach((section) => {
          const childComponents = state.form!.components.filter(
            (c) => c.parentId === section.id
          );
          allStepComponents.push(...childComponents);
        });

      // Validate each component in the step
      for (const component of allStepComponents) {
        // Skip non-input components
        if (
          component.type === "step" ||
          component.type === "section" ||
          !component.name
        ) {
          continue;
        }

        // Check if component should be rendered (conditional rendering)
        try {
          const shouldRender = evaluateConditionalRendering(component, watch);
          if (!shouldRender) {
            continue; // Skip validation for hidden components
          }
        } catch (error) {
          console.warn(
            `Error evaluating conditional rendering for ${component.name}:`,
            error
          );
          // Continue with validation if conditional rendering check fails
        }

        // Get the current value for this field
        const fieldValue = formValues[component.name];

        // Validate the field
        const validationResult = validateField(component, fieldValue);

        if (!validationResult.isValid) {
          stepHasErrors = true;
          allErrors[component.name] = {
            type: "manual",
            message: validationResult.error,
          };
        }
      }

      if (stepHasErrors) {
        invalidSteps.push(stepIndex);
      }
    }

    return {
      isValid: invalidSteps.length === 0,
      errors: allErrors,
      invalidSteps,
    };
  };

  const getProcessData = async () => {
    await commitCurrentStepData();
    const formValues = methods.getValues();
    console.log("🚀 ~ getProcessData ~ formValues:", formValues);
    const processedData = processFormData(
      formValues,
      state.form?.components || []
    );
    return processedData;
  };

  /**
   * Save form progress
   */
  const saveProgress = async (): Promise<boolean> => {
    if (!state.form || !user) {
      console.error("Cannot save progress: No form or user");
      return false;
    }

    // Prevent multiple simultaneous save operations
    if (isSaving) {
      return false;
    }

    setIsSaving(true);

    try {
      // 1. Commit the current step's data to ensure we have the latest values
      const processedData = await getProcessData();
      console.log("🚀 ~ saveProgress ~ processedData:", processedData);

      let updatedSubmission: FormApplication;

      // 3. If we don't have a submission yet, create one
      if (!state.submission?.id) {
        const newSubmission = await SubmissionService.createSubmission({
          formId: state.form.id,
          formSchema: state.form,
          applicantId: user.id,
          applicant: user,
          status: "DRAFT",
          data: processedData,
          projectRef,
        });

        updatedSubmission = newSubmission;
      } else {
        // 4. Otherwise, update the existing submission
        updatedSubmission = await SubmissionService.updateSubmission(
          state.form.id,
          state.submission.id,
          {
            data: processedData,
            status: "DRAFT",
            projectRef,
            updatedAt: new Date().toISOString(),
          }
        );
      }

      // 5. Update local submission state with the updated data
      dispatch({
        type: "SET_SUBMISSION",
        payload: {
          id: updatedSubmission.id,
          formId: updatedSubmission.formId,
          projectRef: updatedSubmission.projectRef,
          data: updatedSubmission.formData,
          status: updatedSubmission.status,
          formSchema: state.form,
        },
      });

      // 5. Update UI with success message
      setFormStatus({
        isSubmitted: true,
        isValid: true,
        message: "Progress saved successfully!",
      });

      return true;
    } catch (error: any) {
      // Handle error when saving progress
      console.error("Error saving form progress:", error);
      const errorMessage =
        (error as ErrorResponse).details ?? "Unknown error occurred";

      setFormStatus({
        isSubmitted: true,
        isValid: false,
        message: `Failed to save progress: ${errorMessage}`,
      });

      return false;
    } finally {
      setIsSaving(false);
    }
  };

  /**
   * Submit form
   */
  const submitForm = async (): Promise<boolean> => {
    if (!state.form || !user) {
      console.error("Cannot submit form: No form or user");
      return false;
    }

    // Prevent multiple simultaneous submissions
    if (isSubmitting) {
      return false;
    }

    setIsSubmitting(true);

    try {
      // 1. Commit the current step's data to ensure we have the latest values
      await commitCurrentStepData();

      // 2. Validate all steps
      const validationResult = await validateAllSteps();

      if (!validationResult.isValid) {
        const errorCount = Object.keys(validationResult.errors).length;
        const invalidStepNumbers = validationResult.invalidSteps.map(
          (i) => i + 1
        );

        // Set the validation errors in the form state so they can be displayed
        Object.keys(validationResult.errors).forEach((fieldName) => {
          methods.setError(fieldName, validationResult.errors[fieldName]);
        });
        setFormStatus({
          isSubmitted: true,
          isValid: false,
          message: `Please fix ${errorCount} validation error${
            errorCount > 1 ? "s" : ""
          } before submitting. Check step${
            invalidStepNumbers.length > 1 ? "s" : ""
          }: ${invalidStepNumbers.join(", ")}.`,
        });
        return false;
      }

      // 3. Show submitting status
      setFormStatus({
        isSubmitted: true,
        isValid: true,
        message: "Submitting form...",
      });

      // 4. Get and process all form data
      const allFormData = methods.getValues();

      const processedData = processFormData(
        allFormData,
        state.form?.components || []
      );

      let updatedSubmission: FormApplication;

      // 5. If we don't have a submission yet, create one and submit it
      if (!state.submission?.id) {
        const newSubmission = await SubmissionService.createSubmission({
          formId: state.form.id,
          formSchema: state.form,
          applicantId: user.id,
          applicant: user,
          status: "SUBMITTED", // Create it as submitted directly
          data: processedData,
          projectRef,
          submittedAt: new Date().toISOString(),
        });

        updatedSubmission = newSubmission;
      } else {
        // 6. Otherwise, update the existing submission to submitted status
        updatedSubmission = await SubmissionService.updateSubmission(
          state.form.id,
          state.submission.id,
          {
            status: "SUBMITTED",
            data: processedData,
            projectRef,
            updatedAt: new Date().toISOString(),
            submittedAt: new Date().toISOString(),
          }
        );
      }

      // 7. Update local submission state
      dispatch({
        type: "SET_SUBMISSION",
        payload: {
          id: updatedSubmission.id,
          formId: updatedSubmission.formId,
          projectRef: updatedSubmission.projectRef,
          data: updatedSubmission.formData,
          status: updatedSubmission.status,
          formSchema: state.form,
        },
      });

      // 7. Update UI with success message
      setFormStatus({
        isSubmitted: true,
        isValid: true,
        message: "Form submitted successfully!",
      });

      // 8. Navigate to applications page after a short delay
      setTimeout(() => {
        navigate("/projects");
      }, 2000);

      return true;
    } catch (error) {
      // Handle error when submitting form
      console.error("Error submitting form:", error);
      const errorMessage =
        (error as ErrorResponse).details ?? "Unknown error occurred";

      setFormStatus({
        isSubmitted: true,
        isValid: false,
        message: `Failed to submit form: ${errorMessage}`,
      });

      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    isSaving,
    isSubmitting,
    saveProgress,
    submitForm,
    getProcessData,
  };
}
