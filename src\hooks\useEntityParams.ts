import { useParams, useLocation } from "react-router-dom";
import { useMemo } from "react";
import { ParameterConfig } from "@/lib/types/page-config";

/**
 * Result of parameter extraction and processing
 */
export interface EntityParamsResult {
  /** Dynamic path to append to API endpoints */
  dynamicPath: string;
  /** Extracted and transformed context data */
  contextData: Record<string, any>;
  /** Whether all required parameters are present and valid */
  isValid: boolean;
  /** Raw URL parameters */
  params: Record<string, string | undefined>;
  /** Validation errors if any */
  errors: string[];
}

/**
 * Hook for extracting and processing URL parameters for entity pages
 */
export function useEntityParams(
  paramConfig?: ParameterConfig
): EntityParamsResult {
  const params = useParams();
  const location = useLocation();

  return useMemo(() => {
    const result: EntityParamsResult = {
      dynamicPath: "",
      contextData: {},
      isValid: true,
      params,
      errors: [],
    };

    // If no parameter configuration, return basic result
    if (!paramConfig) {
      console.log("🔍 No paramConfig, returning basic result");
      return result;
    }

    // Validate required parameters
    if (paramConfig.required) {
      for (const requiredParam of paramConfig.required) {
        if (!params[requiredParam]) {
          result.isValid = false;
          result.errors.push(
            `Required parameter '${requiredParam}' is missing`
          );
        }
      }
    }

    // Validate parameter values using custom validation functions
    if (paramConfig.validation) {
      for (const [paramName, validator] of Object.entries(
        paramConfig.validation
      )) {
        const paramValue = params[paramName];
        if (paramValue && !validator(paramValue)) {
          result.isValid = false;
          result.errors.push(
            `Parameter '${paramName}' has invalid value: ${paramValue}`
          );
        }
      }
    }

    // Build dynamic path from parameters
    result.dynamicPath = buildDynamicPath(params, paramConfig);

    // Extract context data (all available parameters)
    result.contextData = extractContextData(params, paramConfig);

    return result;
  }, [params, location.pathname, paramConfig]);
}

/**
 * Build dynamic path for API endpoints based on parameters
 */
function buildDynamicPath(
  params: Record<string, string | undefined>,
  config: ParameterConfig
): string {
  // If useQueryParams is true, don't build a dynamic path
  if (config.useQueryParams) {
    return "";
  }

  const pathParts: string[] = [];

  // Add required parameters to path
  if (config.required) {
    for (const paramName of config.required) {
      const paramValue = params[paramName];
      if (paramValue) {
        pathParts.push(`/${paramName}/${paramValue}`);
      }
    }
  }

  // Add optional parameters to path if they exist
  if (config.optional) {
    for (const paramName of config.optional) {
      const paramValue = params[paramName];
      if (paramValue) {
        pathParts.push(`/${paramName}/${paramValue}`);
      }
    }
  }

  return pathParts.join("");
}

/**
 * Extract context data from URL parameters
 */
function extractContextData(
  params: Record<string, string | undefined>,
  config: ParameterConfig
): Record<string, any> {
  const contextData: Record<string, any> = {};

  // Include all configured parameters
  const allConfiguredParams = [
    ...(config.required || []),
    ...(config.optional || []),
  ];

  for (const paramName of allConfiguredParams) {
    const paramValue = params[paramName];
    if (paramValue !== undefined) {
      contextData[paramName] = paramValue;
    }
  }

  // Include any additional parameters that might be useful
  for (const [key, value] of Object.entries(params)) {
    if (value !== undefined && !contextData[key]) {
      contextData[key] = value;
    }
  }

  return contextData;
}

/**
 * Utility function to validate parameter configuration
 */
export function validateParameterConfig(config: ParameterConfig): string[] {
  const errors: string[] = [];

  // Check for duplicate parameters between required and optional
  if (config.required && config.optional) {
    const duplicates = config.required.filter((param) =>
      config.optional!.includes(param)
    );
    if (duplicates.length > 0) {
      errors.push(
        `Parameters cannot be both required and optional: ${duplicates.join(
          ", "
        )}`
      );
    }
  }

  // Validate path pattern if provided
  if (config.pathPattern) {
    const pathParams = extractPathParams(config.pathPattern);
    const configuredParams = [
      ...(config.required || []),
      ...(config.optional || []),
    ];

    // Check if all path parameters are configured
    for (const pathParam of pathParams) {
      if (!configuredParams.includes(pathParam)) {
        errors.push(
          `Path parameter '${pathParam}' is not configured in required or optional parameters`
        );
      }
    }
  }

  return errors;
}

/**
 * Extract parameter names from a path pattern like "/projects/:projectId/documents/:docId"
 */
function extractPathParams(pathPattern: string): string[] {
  const paramRegex = /:([^/]+)/g;
  const params: string[] = [];
  let match;

  while ((match = paramRegex.exec(pathPattern)) !== null) {
    params.push(match[1]);
  }

  return params;
}

/**
 * Build a complete URL path from a pattern and parameters
 */
export function buildUrlFromPattern(
  pattern: string,
  params: Record<string, string>
): string {
  let url = pattern;

  for (const [key, value] of Object.entries(params)) {
    url = url.replace(`:${key}`, value);
  }

  return url;
}
