import { useState, useEffect } from 'react';

interface OptimizedImageProps {
    imgUrl: string;
    altText: string;
    fallbackImgUrl: string;
    className?: string;
}

const OptimizedImage = ({ imgUrl, altText, fallbackImgUrl, className }: OptimizedImageProps) => {
    const [imgSrc, setImgSrc] = useState(imgUrl && imgUrl !== '' ? imgUrl : fallbackImgUrl);

    useEffect(() => {
        if (imgUrl && imgUrl !== '') {
            setImgSrc(imgUrl);
        } else {
            setImgSrc(fallbackImgUrl);
        }
    }, [imgUrl, fallbackImgUrl]);

    return (
        <img
            src={imgSrc}
            alt={altText}
            loading="lazy"
            onError={() => setImgSrc(fallbackImgUrl)}
            className={className}
        />
    );
};

export default OptimizedImage;