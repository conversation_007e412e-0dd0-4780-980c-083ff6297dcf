import React, { createContext, useContext, useReducer, useEffect, useMemo } from 'react';
import { set } from 'lodash-es';
import { produce } from 'immer';
import { FormSchema } from '@/lib/schemas/form-schemas';
import { FormSubmission } from '@/lib/types/submission';
import { useCalculationEngine } from '@/hooks/useCalculationEngine';

// Define the shape of the form state
export interface FormState {
    form: FormSchema | null;
    submission: FormSubmission | null;
    calculatedValues: Record<string, any>;
}

// Define the actions that can be dispatched
export type FormAction =
    | { type: 'SET_FORM'; payload: FormSchema }
    | { type: 'SET_SUBMISSION'; payload: FormSubmission }
    | { type: 'UPDATE_VALUE'; payload: { name: string; value: any } }
    | { type: 'UPDATE_CALCULATED_VALUES'; payload: Record<string, any> };

// Create the context
const FormStateContext = createContext<{
    state: FormState;
    dispatch: React.Dispatch<FormAction>;
} | null>(null);

// Reducer function to manage state updates
export function formReducer(state: FormState, action: FormAction): FormState {
    switch (action.type) {
        case 'SET_FORM':
            return { ...state, form: action.payload };
        case 'SET_SUBMISSION':
            return { ...state, submission: action.payload };
        case 'UPDATE_CALCULATED_VALUES':
            return produce(state, (draft) => {
                draft.calculatedValues = action.payload;
                if (draft.submission) {
                    for (const key in action.payload) {
                        set(draft.submission.data, key, action.payload[key]);
                    }
                }
            });
        case 'UPDATE_VALUE':
            if (!state.submission) {
                return state;
            }
            // Use Immer for deep immutable updates
            return produce(state, (draft) => {
                set(draft.submission!.data, action.payload.name, action.payload.value);
            });
        default:
            return state;
    }
}

// Context provider component
export const FormStateProvider = ({
    children,
    initialForm,
    initialSubmission,
}: {
    children: React.ReactNode;
    initialForm: FormSchema | null;
    initialSubmission: FormSubmission | null;
}) => {
    const [state, dispatch] = useReducer(formReducer, {
        form: initialForm,
        submission: initialSubmission,
        calculatedValues: {},
    });

    const { calculatedValues, evaluateValidationExpression } = useCalculationEngine(
        state.form,
        state.submission?.data ?? null
    );

    useEffect(() => {
        if (Object.keys(calculatedValues).length > 0) {
            dispatch({ type: 'UPDATE_CALCULATED_VALUES', payload: calculatedValues });
        }
    }, [calculatedValues]);

    const contextValue = useMemo(
        () => ({
            state: {
                ...state,
                calculatedValues,
            },
            dispatch,
            evaluateValidationExpression,
        }),
        [state, calculatedValues, evaluateValidationExpression]
    );

    return (
        <FormStateContext.Provider value={contextValue}>
            {children}
        </FormStateContext.Provider>
    );
};

// Custom hook to use the form state
export const useFormState = () => {
    const context = useContext(FormStateContext);
    if (!context) {
        throw new Error('useFormState must be used within a FormStateProvider');
    }
    return context;
};