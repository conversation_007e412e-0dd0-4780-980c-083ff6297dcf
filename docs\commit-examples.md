# Conventional Commit Examples

This document provides real-world examples of conventional commits for our project.

## Feature Examples

### Adding a New Component

```
feat(ui): add information text component

Add a new text component that displays informative text as a styled card to help users fill in forms.
The component includes a text area in the configuration panel for writing the content.

Closes #123
```

### Adding a New Feature to an Existing Component

```
feat(form-builder): add validation support to data grid

Implement validation rules for data grid cells including required fields, min/max values,
and custom validation messages.
```

## Bug Fix Examples

### Simple Bug Fix

```
fix: resolve issue with form submission in Safari

Fix a bug where form submission would fail in Safari browsers due to a compatibility issue
with FormData handling.
```

### Bug Fix with Scope

```
fix(data-grid): fix column resizing in Firefox

Resolve an issue where data grid columns couldn't be resized properly in Firefox browsers.

Closes #456
```

## Documentation Examples

### Updating Documentation

```
docs: update README with new installation instructions

Update the README.md file with clearer installation instructions and add troubleshooting section.
```

### Adding New Documentation

```
docs(api): add API documentation for form schema endpoints

Create comprehensive documentation for the form schema API endpoints including
request/response examples and error handling.
```

## Refactoring Examples

### Code Refactoring

```
refactor: extract form validation logic to separate hook

Move form validation logic from FormBuilder component to a dedicated useFormValidation hook
to improve code organization and reusability.
```

### Performance Refactoring

```
perf(rendering): optimize component rendering with memoization

Improve rendering performance by implementing React.memo for list items and
useCallback for event handlers in the FormList component.
```

## Breaking Change Examples

### API Change

```
feat(api)!: change authentication endpoint response format

BREAKING CHANGE: The response format of the authentication endpoint has changed.
The token is now returned in the 'accessToken' field instead of 'token'.
```

### Component Interface Change

```
refactor(ui)!: update Button component API

BREAKING CHANGE: The Button component no longer accepts 'color' prop.
Use 'variant' prop instead with values 'primary', 'secondary', or 'danger'.
```

## Other Examples

### Chore

```
chore: update dependencies to latest versions

Update all dependencies to their latest versions and ensure compatibility.
```

### CI

```
ci: add automated accessibility testing to CI pipeline

Configure CI to run accessibility tests on all PRs to ensure components meet WCAG standards.
```

### Style

```
style: format code according to new ESLint rules

Apply code formatting changes to comply with updated ESLint configuration.
No functional changes.
```

### Test

```
test: add unit tests for form validation logic

Add comprehensive unit tests for the form validation utilities to ensure
all validation rules work as expected.
```
