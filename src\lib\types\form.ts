/**
 * This file re-exports types from the Zod schemas in src/lib/schemas/form-schemas.ts
 * for backward compatibility. New code should import directly from form-schemas.ts.
 */

// Import all types from the Zod schemas
import {
  FormStatus,
  FormMeta,
  FormComponentType,
  FormComponent,
  TextComponent,
  NumberComponent,
  DateComponent,
  DateTimeComponent,
  SelectOption,
  SelectComponent,
  CheckboxComponent,
  RadioComponent,
  DataGridCellInputType,
  DataGridCell,
  DataGridColumnConfig,
  DataGridComponent,
  StepComponent,
  SectionComponent,
  InfoTextComponent,
  ValidationRule,
  FormComponentValidation,
  ConditionalRendering,
  GridRowConditionalRendering,
  FormSchema,
} from "@/lib/schemas/form-schemas";

// Re-export all types for backward compatibility
export type {
  FormStatus,
  FormMeta,
  FormComponentType,
  FormComponent,
  TextComponent,
  NumberComponent,
  DateComponent,
  DateTimeComponent,
  SelectOption,
  SelectComponent,
  CheckboxComponent,
  RadioComponent,
  DataGridCellInputType,
  DataGridCell,
  DataGridColumnConfig,
  DataGridComponent,
  StepComponent,
  SectionComponent,
  InfoTextComponent,
  ValidationRule,
  FormComponentValidation,
  ConditionalRendering,
  GridRowConditionalRendering,
  FormSchema,
};

// Re-export all schemas as a namespace for easier migration
import * as FormSchemas from "@/lib/schemas/form-schemas";
export { FormSchemas };
