import {
  FormSubmissionCreate,
  FormSubmissionUpdate,
  FormApplication,
} from "../types/submission";
interface ApplicationFormResponse {
  content: FormApplication[];
  page: PageableObject;
}

// interface ApplicationFormByIdResponse {
//   content: FormApplication;
// }

import { apiClient } from "../api/api-client";
import { PageableObject } from "../types/api";

/**
 * Service for managing form submissions
 */
export const SubmissionService = {
  /**
   * Get all submissions with optional filtering
   */
  getSubmissions: async (): Promise<FormApplication[]> => {
    const response = await apiClient.get<ApplicationFormResponse>(
      "/application-forms"
    );
    return response.data.content;
  },

  /**
   * Get a specific submission by ID
   */
  getSubmissionById: async (
    id: string,
    projectRef?: string
  ): Promise<FormApplication | null> => {
    console.log("🚀 ~ id:", id);
    const response = await apiClient.get<FormApplication>(
      "/application-forms/byProject/" + projectRef
    );
    return response.data;
    // `/application-forms/${id}?status=DRAFT&formId=${id}`;
    // return response.data.content.filter((app) => {
    //   if (!projectRef) {
    //     return app.formId === id;
    //   }
    //   return app.formId === id && app.projectRef === projectRef;
    // })[0];
  },

  /**
   * Create a new submission (start a form)
   */
  createSubmission: async (
    submission: FormSubmissionCreate
  ): Promise<FormApplication> => {
    const newSubmission: Omit<
      FormApplication,
      "id" | "createdAt" | "updatedAt"
    > = {
      status: submission.status,
      formData: submission.data,
      formId: submission.formId,
      projectRef: submission.projectRef,
    };

    const response = await apiClient.post<FormApplication>(
      "/application-forms",
      newSubmission
    );

    return response.data;
  },

  /**
   * Update an existing submission (save progress or submit)
   */
  updateSubmission: async (
    formId: string,
    id: string,
    updates: FormSubmissionUpdate
  ): Promise<FormApplication> => {
    const updatedSubmission = {
      formId: formId,
      status: updates.status,
      formData: updates.data,
      projectRef: updates.projectRef,
    };

    const response = await apiClient.put<FormApplication>(
      `/application-forms/${id}`,
      updatedSubmission
    );
    return response.data;
  },

  /**
   * Delete a submission
   */
  deleteSubmission: async (id: string): Promise<boolean> => {
    console.log("🚀 ~ deleteSubmission: ~ id:", id);
    // await delay(400);

    // const submissionIndex = mockSubmissions.findIndex((s) => s.id === id);

    // if (submissionIndex === -1) {
    //   return false;
    // }

    // // Remove from mock data
    // mockSubmissions.splice(submissionIndex, 1);

    return true;
  },
};
