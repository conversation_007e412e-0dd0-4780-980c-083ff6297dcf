import { memo } from "react";
import { AlertCircle } from "lucide-react";

interface FormStatusMessageProps {
  isSubmitted: boolean;
  isValid: boolean;
  message: string;
}

/**
 * Displays form submission status and validation messages
 */
function FormStatusMessage({ isSubmitted, isValid, message }: FormStatusMessageProps) {
  if (!isSubmitted) {
    return null;
  }

  return (
    <div
      className={`p-3 rounded-md ${
        isValid
          ? "bg-green-50 text-green-700 border border-green-200"
          : "bg-red-50 text-red-700 border border-red-200"
      }`}
    >
      <div className="flex items-center gap-2">
        {isValid ? (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
              clipRule="evenodd"
            />
          </svg>
        ) : (
          <AlertCircle className="h-5 w-5" />
        )}
        <span>{message}</span>
      </div>
    </div>
  );
}

export default memo(FormStatusMessage);
