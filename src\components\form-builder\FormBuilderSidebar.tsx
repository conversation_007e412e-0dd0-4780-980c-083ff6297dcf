import { memo, useCallback, useMemo } from "react";
import { FormComponent } from "@/lib/types/form";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";

// Import components directly to prevent re-initialization
import Component<PERSON>alet<PERSON> from "./ComponentPalette";
import FormComponentEditor from "@/components/form-builder/FormComponentEditor";

interface FormBuilderSidebarProps {
  readonly selectedComponent: FormComponent | undefined;
  readonly selectedComponentId: string | null;
  readonly allComponents: FormComponent[];
  readonly onAddComponent: (type: any) => void;
  readonly onUpdateComponent: (component: FormComponent) => void;
  readonly onDeleteComponent: (id: string) => void;
  readonly activeTab?: "add" | "edit";
  readonly onTabChange?: (tab: "add" | "edit") => void;
}

function FormBuilderSidebar({
  selectedComponent,
  selectedComponentId,
  allComponents,
  onAddComponent,
  onUpdateComponent,
  onDeleteComponent,
  activeTab,
  onTabChange,
}: FormBuilderSidebarProps) {
  // Determine the default tab value
  const defaultTab = selectedComponentId ? "edit" : "add";

  // Memoize the tab change handler to prevent recreating on every render
  const handleTabChange = useCallback(
    (value: string) => {
      onTabChange?.(value as "add" | "edit");
    },
    [onTabChange]
  );

  // Memoize the delete handler to prevent recreating on every render
  const handleDelete = useCallback(() => {
    if (selectedComponent) {
      onDeleteComponent(selectedComponent.id);
    }
  }, [selectedComponent, onDeleteComponent]);

  // Memoize the add component tab content
  const addTabContent = useMemo(
    () => (
      <TabsContent value="add" className="mt-4 flex-1">
        <ComponentPalette onAddComponent={onAddComponent} />
      </TabsContent>
    ),
    [onAddComponent]
  );

  // Memoize the edit component tab content
  const editTabContent = useMemo(
    () => (
      <TabsContent value="edit" className="mt-4 flex-1">
        {selectedComponent && (
          <FormComponentEditor
            component={selectedComponent}
            onChange={onUpdateComponent}
            onDelete={handleDelete}
            allComponents={allComponents}
          />
        )}
      </TabsContent>
    ),
    [selectedComponent, onUpdateComponent, handleDelete, allComponents]
  );

  return (
    <Tabs
      defaultValue={defaultTab}
      value={activeTab}
      onValueChange={handleTabChange}
      className="h-full flex flex-col"
    >
      <TabsList className="w-full">
        <TabsTrigger value="add" className="flex-1">
          Add
        </TabsTrigger>
        <TabsTrigger
          value="edit"
          className="flex-1"
          disabled={!selectedComponentId}
        >
          Edit
        </TabsTrigger>
      </TabsList>
      <div className="flex-1">
        {addTabContent}
        {editTabContent}
      </div>
    </Tabs>
  );
}

// Use memo to prevent unnecessary re-renders of the entire sidebar
export default memo(FormBuilderSidebar);
