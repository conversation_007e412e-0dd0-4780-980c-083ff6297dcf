/**
 * Global dialog manager for handling dialog state across the application
 */

type ProjectData = {
  id: string;
  name: string;
  [key: string]: any;
};

type DialogState = {
  isOpen: boolean;
  projectData: ProjectData | null;
};

type DialogListener = (state: DialogState) => void;

class DialogManager {
  private state: DialogState = {
    isOpen: false,
    projectData: null,
  };

  private readonly listeners: Set<DialogListener> = new Set();

  subscribe(listener: DialogListener) {
    this.listeners.add(listener);
    return () => {
      this.listeners.delete(listener);
    };
  }

  private notify() {
    this.listeners.forEach((listener) => listener(this.state));
  }

  openAttachFundingRoundDialog(projectData: ProjectData) {
    this.state = {
      isOpen: true,
      projectData,
    };
    this.notify();
  }

  closeAttachFundingRoundDialog() {
    this.state = {
      isOpen: false,
      projectData: null,
    };
    this.notify();
  }

  getState() {
    return { ...this.state };
  }
}

export const dialogManager = new DialogManager();
