# Technical Specification: Calculated Cell Feature

This document outlines the technical design for a new "Calculated Cell" feature in the form builder.

## 1. Schema Changes

To support calculated cells, we will introduce a `calculation` object to the `DataGridCellSchema` and a `calculation` property to the `formComponentBaseSchema` for read-only fields.

### 1.1. Modified Schemas in `src/lib/schemas/form-schemas.ts`

We will introduce a new `calculationSchema` and add it to the relevant component schemas.

```typescript
// src/lib/schemas/form-schemas.ts

// ... other schemas

export const calculationSchema = z.object({
  formula: z.string(),
  // We can add more properties here in the future, like error handling display
});
export type Calculation = z.infer<typeof calculationSchema>;

export const dataGridCellSchema = z.object({
  id: z.string(),
  value: z.string(),
  type: dataGridCellTypeSchema,
  inputType: dataGridCellInputTypeSchema.optional(),
  options: z.array(selectOptionSchema).optional(),
  validations: z.array(formComponentValidationSchema).optional(),
  unit: z.string().optional(),
  min: z.number().optional(),
  max: z.number().optional(),
  step: z.number().optional(),
  required: z.boolean().optional(),
  calculation: calculationSchema.optional(), // ADDED
  isReadOnly: z.boolean().optional(), // ADDED
});

export const formComponentBaseSchema = z.object({
  id: z.string(),
  type: formComponentTypeSchema,
  label: z.string(),
  name: z.string(),
  required: z.boolean().optional(),
  placeholder: z.string().optional(),
  defaultValue: z.any().optional(),
  disabled: z.boolean().optional(),
  validations: z.array(formComponentValidationSchema).optional(),
  conditionalRendering: conditionalRenderingSchema.optional(),
  parentId: z.string().optional(),
  calculation: calculationSchema.optional(), // ADDED for read-only fields
  isReadOnly: z.boolean().optional(), // ADDED
});

// Add a new validation rule for expression-based validation
export const validationRuleSchema = z.enum([
  "required",
  "min",
  "max",
  "minLength",
  "maxLength",
  "pattern",
  "email",
  "url",
  "expression", // ADDED
]);

export const formComponentValidationSchema = z.object({
  rule: validationRuleSchema,
  value: z.any().optional(),
  message: z.string().optional(),
  expression: z.string().optional(), // ADDED for expression rule
});
```

### 1.2. Formula Syntax

The `formula` string will use a simple, Excel-like syntax.

- **Functions**: `SUM()`, `PERCENTAGE()`, `SUBTRACT()`
- **Cell References**:
  - Within the same grid: `A2`, `B5`
  - Across different grids: `datagridId.A2`
- **Range References**: `A2:A10` (for `SUM`)
- **Operators**: `+`, `-`, `*`, `/`

**Examples:**

- `SUM(A2:A10)`
- `SUM(otherGrid.B1:B5)`
- `A2 - B2`
- `PERCENTAGE(C1, D1)` (Calculates what percentage C1 is of D1)

## 2. Calculation Engine

We will create a new hook, `useCalculationEngine`, to handle parsing and evaluation of formulas. This hook will be responsible for managing a dependency graph to ensure efficient recalculations.

### 2.1. `useCalculationEngine` Hook Design

```typescript
// src/hooks/useCalculationEngine.ts

import { FormSchema } from "@/lib/schemas/form-schemas";
import { FormSubmission } from "@/lib/types/submission";

interface CalculationEngineResult {
  calculatedValues: Record<string, any>;
  recalculate: () => void;
}

export function useCalculationEngine(
  formSchema: FormSchema,
  formValues: FormSubmission["data"]
): CalculationEngineResult {
  // ... implementation ...
}
```

### 2.2. Dependency Graph

The engine will build a directed acyclic graph (DAG) to represent dependencies between cells.

- **Nodes**: Form fields and datagrid cells.
- **Edges**: Represent a dependency (i.e., a cell's formula references another cell).

When a cell's value changes, only its dependent cells will be recalculated.

```mermaid
graph TD
    A[datagrid1.A1] --> C{datagrid1.C1};
    B[datagrid1.B1] --> C;
    C -- SUM --> D[datagrid1.D1];
    E[datagrid2.A1] --> F[readOnlyField1];
    D --> F;
```

## 3. State Management

The current `useFormState` hook is insufficient. We will introduce a new `FormStateProvider` and `useFormState` context hook to manage the form's state, including input values and calculated values.

### 3.1. `FormStateProvider`

```tsx
// src/contexts/FormStateContext.tsx

import React, { createContext, useContext, useReducer } from "react";

// ... action types and reducer logic ...

const FormStateContext = createContext(null);

export const FormStateProvider = ({
  children,
  initialForm,
  initialSubmission,
}) => {
  // ... useReducer to manage state ...
  // ... useCalculationEngine to get calculated values ...

  return (
    <FormStateContext.Provider
      value={{ formState, dispatch, calculatedValues }}
    >
      {children}
    </FormStateContext.Provider>
  );
};

export const useFormState = () => useContext(FormStateContext);
```

### 3.2. Recalculation Flow

```mermaid
sequenceDiagram
    participant User
    participant InputComponent
    participant FormStateProvider
    participant CalculationEngine

    User->>InputComponent: Changes value of cell A1
    InputComponent->>FormStateProvider: dispatch({ type: 'UPDATE_VALUE', payload: { field: 'datagrid1.A1', value: 10 }})
    FormStateProvider->>CalculationEngine: Recalculates dependencies of A1
    CalculationEngine-->>FormStateProvider: Returns updated calculatedValues
    FormStateProvider->>InputComponent: Re-renders with new calculated values
```

## 4. Configuration UI

### 4.1. `GridConfigurationEditor.tsx`

We will add a new "Calculation" tab to the `GridConfigurationEditor`. When a cell is selected, the user can enter a formula in a text input.

- A new tab "Calculation" will be added to the `Tabs` component.
- This tab will contain a `FormulaEditor` component.
- The `FormulaEditor` will have an input for the formula and could have a helper for inserting functions and cell references.

### 4.2. `FormComponentEditor.tsx`

For standard form components (like `number` or `text` with `isReadOnly: true`), we will add a "Calculation" section in the `BasicTabContent`.

## 5. Validation Integration

### 5.1. `ValidationEditor.tsx`

The `ValidationEditor` will be updated to support the new `expression` rule.

- When "Expression" is selected as the rule type, a text area will be shown for the user to enter the validation expression.
- The expression can reference other cells and calculated values.
- A special keyword `SELF` can be used to refer to the value of the cell being validated.

**Example Expression:**

`SELF > SUM(datagrid1.A2:A10) * 0.2`

### 5.2. Validation Logic

The core validation logic will use the `useCalculationEngine` to evaluate the expression. The engine will need to be extended to support comparison operators (`<`, `>`, `==`).

The result of the expression evaluation (true or false) will determine if the validation passes.

---

# User Guide & Formula Reference

This guide provides a comprehensive overview of how to use formulas for calculated cells and validation expressions.

## Basic Calculations

You can perform standard arithmetic operations directly in the formula input. All formulas must start with an `=` sign, but for simplicity, we will omit it in the examples below.

| Operator | Description    | Example     |
| :------- | :------------- | :---------- |
| `+`      | Addition       | `10 + 5`    |
| `-`      | Subtraction    | `A2 - B2`   |
| `*`      | Multiplication | `C1 * 1.05` |
| `/`      | Division       | `D10 / 2`   |

## Cell Referencing

You can reference other fields in your formulas to create dynamic calculations.

### Referencing DataGrid Cells

To reference a cell within a DataGrid, you need to specify the grid's ID and the cell's coordinates.

- **Syntax**: `gridId.CellCoordinate`
- **Example**: To reference cell `B5` in a grid with the ID `expenses_grid`, you would use `expenses_grid.B5`.

`= expenses_grid.A2 + expenses_grid.A3`

### Referencing Standalone Form Fields

To reference a standalone form field (e.g., an input field outside of a grid), use its `name` attribute.

- **Syntax**: `fieldName`
- **Example**: If you have a number input field with the name `total_cost`, you can reference it like this:

`= total_cost * 0.2`

## Functions

Functions allow you to perform more complex calculations, like aggregating data from a range of cells.

### `SUM()`

Calculates the sum of a range of cells.

- **Syntax**: `SUM(gridId.StartCell:EndCell)`
- **Example**: To sum values from cell `A2` to `A10` in `grid1`:

`= SUM(grid1.A2:A10)`

### `AVG()`

Calculates the average of a range of cells.

- **Syntax**: `AVG(gridId.StartCell:EndCell)`
- **Example**: To find the average of values from `B1` to `B10` in `grid1`:

`= AVG(grid1.B1:B10)`

## Validation Expressions

You can write custom validation rules using formulas to enforce complex conditions. The formula should evaluate to either `true` (passes validation) or `false` (fails validation).

### The `SELF` Keyword

When writing a validation rule for a field, you can use the `SELF` keyword to refer to the value of the field currently being validated.

- **Example**: To ensure a field's value is greater than 100:

`SELF > 100`

### Comparison Operators

Use standard comparison operators to create your validation logic.

| Operator | Description              | Example               |
| :------- | :----------------------- | :-------------------- |
| `>`      | Greater than             | `SELF > 10`           |
| `<`      | Less than                | `SELF < total_cost`   |
| `>=`     | Greater than or equal to | `SELF >= 0`           |
| `<=`     | Less than or equal to    | `SELF <= 100`         |
| `==`     | Equal to                 | `SELF == "Completed"` |
| `!=`     | Not equal to             | `SELF != "Pending"`   |

### Complex Validation Examples

You can combine cell references, functions, and operators to create sophisticated rules.

- **Example 1**: Ensure a discount amount is not more than 20% of the total of an expense column.

`SELF <= SUM(expenses_grid.A1:A10) * 0.20`

- **Example 2**: Check if a project budget is greater than the sum of two other fields.

`SELF > (initial_funding + grant_amount)`

---

## Performance Optimization & Dependency Graph

### 1. The Problem: Inefficient Recalculations

The initial implementation of the calculation engine suffered from two primary performance issues:

- **Unstable Dependencies**: The `useMemo` hook in `useCalculationEngine` depended on an `allComponents` array that was being recreated on every render cycle. This caused the memoized calculation to run far more often than necessary, even when no data had changed.
- **Full Recalculation**: The engine recalculated every single formula in the form whenever any piece of data changed. For forms with many calculations, this approach is inefficient and leads to a sluggish user experience as the form grows in complexity.

### 2. The Solution: Dependency Stabilization and a Dependency Graph

To address these issues, the calculation engine will be re-architected to stabilize its dependencies and perform targeted recalculations using a dependency graph.

#### 2.1. Dependency Stabilization

The first step is to stabilize the `allComponents` array. This will be achieved by wrapping its creation in a `useMemo` hook within `useCalculationEngine`, with `formSchema` as its dependency.

**Before:**

```typescript
// In useCalculationEngine.ts
const allComponents = formSchema?.components || [];
```

**After:**

```typescript
// In useCalculationEngine.ts
const allComponents = useMemo(() => formSchema?.components || [], [formSchema]);
```

This ensures that the `allComponents` array reference remains stable unless the form's structure (`formSchema`) actually changes.

#### 2.2. Dependency Graph Architecture

The core of the optimization is the introduction of a dependency graph. This graph will track the relationships between cells, allowing the engine to intelligently update only what's necessary.

##### Data Structures

Two new data structures will be managed within `useCalculationEngine`:

1.  **`formulaMap`**: A map where keys are the IDs of calculated cells (e.g., `'componentId.cellId'` or `'componentId'`) and values are objects containing the parsed formula (in RPN) and a list of its direct dependencies.

    - **Type**: `Map<string, { rpn: FormulaToken[], dependencies: string[] }>`

2.  **`dependencyGraph`**: An inverted index that maps a dependency cell's ID to a set of calculated cell IDs that rely on it.
    - **Type**: `Map<string, Set<string>>`

##### Visualizing the Graph

Consider a simple grid where `C1 = A1 + B1`. The data structures would look like this:

```mermaid
graph TD
    subgraph Data Structures
        direction LR
        subgraph formulaMap
            C1["'C1' => { rpn: [...], dependencies: ['A1', 'B1'] }"]
        end
        subgraph dependencyGraph
            A1_dep["'A1' => Set('C1')"]
            B1_dep["'B1' => Set('C1')"]
        end
    end

    subgraph Cell Relationships
        A1[Cell A1] --> C1{Calc Cell C1}
        B1[Cell B1] --> C1
    end

    linkStyle 0 stroke-width:0px;
    linkStyle 1 stroke-width:0px;
    linkStyle 2 stroke-width:0px;
```

##### The Process

1.  **Graph Building**:

    - The graph and formula map will be built once, whenever the `formSchema` changes. This process will be memoized.
    - The engine will iterate through all components in the `formSchema`.
    - For each component with a `calculation.formula`, it will parse the formula to identify all `CELL_REF` and `RANGE_REF` tokens. These are the dependencies.
    - It will populate the `formulaMap` for the calculated cell and update the `dependencyGraph` for each of its dependencies.

2.  **Targeted Recalculation**:
    - The engine will track changes between the previous and current `formValues`.
    - When a change is detected in a cell (e.g., the value of `A1` is updated), the engine will:
      a. Create a queue of cells to be recalculated, starting with the direct dependents of the changed cell (retrieved from `dependencyGraph`). For `A1`, the queue would be `['C1']`.
      b. Process the queue iteratively:
      i. Dequeue a cell ID (e.g., `C1`).
      ii. Re-evaluate its formula using the RPN from `formulaMap`.
      iii. Update the value of `C1` in the `calculatedValues` state.
      iv. If the value of `C1` changed as a result of the recalculation, find _its_ dependents from the `dependencyGraph` and add them to the queue. This ensures that changes cascade through the entire dependency chain.
    - This process continues until the queue is empty, ensuring that only the necessary calculations are performed.

---

## Bug Fix: State Synchronization in Form Preview

A bug was identified where the `FormPreview` component's local state, managed by `react-hook-form`, was not synchronized with the global `FormStateContext`. This prevented the calculation engine from running when users updated form fields.

**The Solution: Reactive State Synchronization**

To resolve this, a reactive synchronization mechanism has been implemented within `FormPreview.tsx`. This approach uses the `watch` API from `react-hook-form` to monitor for changes and propagate them to the `FormStateContext`.

The implementation involves a `useEffect` hook that subscribes to form changes:

```typescript
// src/components/form-builder/FormPreview.tsx

// Inside the FormPreview component
const { dispatch } = useFormState();

useEffect(() => {
  const subscription = watch((value, { name, type }) => {
    if (name && type === "change") {
      dispatch({
        type: "UPDATE_VALUE",
        payload: { field: name, value: value[name] },
      });
    }
  });
  return () => subscription.unsubscribe();
}, [watch, dispatch]);
```

This ensures that any change to a form input triggers an `UPDATE_VALUE` action, which in turn causes the `useCalculationEngine` to re-evaluate the formulas, thus fixing the bug in a robust and maintainable way.
