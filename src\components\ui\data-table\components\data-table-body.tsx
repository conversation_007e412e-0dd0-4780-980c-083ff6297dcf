import React, { memo } from "react";
import { flexRender, Table, ColumnDef, Cell, Row } from "@tanstack/react-table";
import { TableBody, TableCell, TableRow } from "@/components/ui/table";
import { cn } from "@/lib/utils";

interface DataTableBodyProps<TData, TValue> {
  table: Table<TData>;
  columns: ColumnDef<TData, TValue>[];
  isLoading: boolean;
  pinnedColumns: Record<string, number>;
  getGeneralColumnStyle: (columnId: string) => React.CSSProperties;
  getColumnStyle: (columnId: string) => React.CSSProperties;
}

// Common props shared across components
interface CommonStyleProps {
  pinnedColumns: Record<string, number>;
  getGeneralColumnStyle: (columnId: string) => React.CSSProperties;
  getColumnStyle: (columnId: string) => React.CSSProperties;
}

interface LoadingStateProps extends CommonStyleProps {
  table: Table<any>;
  columns: ColumnDef<any, any>[];
}

interface EmptyStateProps {
  columns: ColumnDef<any, any>[];
  dataLength: number;
  rowModelLength: number;
}

interface DataRowsProps extends CommonStyleProps {
  table: Table<any>;
}

interface DataRowProps extends CommonStyleProps {
  row: Row<any>;
}

/**
 * Loading state component
 */
const LoadingState = memo(function LoadingState({
  table,
  columns,
  pinnedColumns,
  getGeneralColumnStyle,
  getColumnStyle,
}: Readonly<LoadingStateProps>) {
  return (
    <TableBody>
      {Array.from({ length: table.getState().pagination.pageSize }).map(
        (_, index) => {
          const stableRowId = `loading-row-${index}`;
          return (
            <TableRow key={stableRowId}>
              {columns.map((column: ColumnDef<any, any>, colIndex: number) => {
                // Safely extract column ID
                const columnId =
                  typeof column.id === "string" ? column.id : `col-${colIndex}`;

                return (
                  <TableCell
                    key={`${stableRowId}-cell-${colIndex}`}
                    style={{
                      ...getGeneralColumnStyle(columnId),
                      ...getColumnStyle(columnId),
                    }}
                    className={cn(
                      "h-16 animate-pulse table-cell",
                      pinnedColumns[columnId]
                        ? "bg-background/90 shadow-[4px_0_8px_-2px_rgba(0,0,0,0.1)]"
                        : "bg-muted/30",
                      !pinnedColumns[columnId] ? "p-2" : ""
                    )}
                    data-pinned={pinnedColumns[columnId] ? "true" : "false"}
                  >
                    {Boolean(pinnedColumns[columnId]) && (
                      <div
                        className="p-2 h-full bg-primary/5 rounded-sm pinned-cell-content"
                        data-column-id={columnId}
                      />
                    )}
                  </TableCell>
                );
              })}
            </TableRow>
          );
        }
      )}
    </TableBody>
  );
});

/**
 * Empty state component
 */
const EmptyState = memo(function EmptyState({
  columns,
  dataLength,
  rowModelLength,
}: Readonly<EmptyStateProps>) {
  return (
    <TableBody>
      <TableRow>
        <TableCell colSpan={columns.length} className="h-24 text-center">
          No results found.
          {process.env.NODE_ENV === "development" && (
            <div className="text-xs text-muted-foreground mt-2">
              Data length: {dataLength}, Row model length: {rowModelLength}
            </div>
          )}
        </TableCell>
      </TableRow>
    </TableBody>
  );
});

/**
 * Cell content component
 */
const CellContent = memo(function CellContent({
  cell,
  cellValue,
}: Readonly<{
  cell: Cell<any, any>;
  cellValue: any;
}>) {
  // Pre-compute the cell context
  const cellContext = cell.getContext();

  // Create a value getter function outside the render
  const valueGetter = () => cellValue;

  if (cell.column.columnDef.cell) {
    return flexRender(cell.column.columnDef.cell, {
      ...cellContext,
      getValue: valueGetter,
    });
  }

  const displayValue = String(cellValue ?? "");
  return (
    <div className="truncate" title={displayValue}>
      {displayValue}
    </div>
  );
});

/**
 * Data cell component
 */
const DataCell = memo(function DataCell({
  cell,
  rowData,
  pinnedColumns,
  getGeneralColumnStyle,
  getColumnStyle,
}: Readonly<{
  cell: Cell<any, any>;
  rowData: any;
  pinnedColumns: Record<string, number>;
  getGeneralColumnStyle: (columnId: string) => React.CSSProperties;
  getColumnStyle: (columnId: string) => React.CSSProperties;
}>) {
  // Ensure we have a valid column ID
  const columnId = typeof cell.column.id === "string" ? cell.column.id : "";

  // Get the value directly from the row data if cell.getValue() is undefined
  const cellValue =
    cell.getValue() !== undefined ? cell.getValue() : rowData[columnId];

  const isPinned = Boolean(pinnedColumns[columnId]);

  return (
    <TableCell
      key={cell.id}
      style={{
        ...getGeneralColumnStyle(columnId),
        ...getColumnStyle(columnId),
      }}
      className={cn(
        "table-cell",
        isPinned
          ? "bg-background/95 shadow-[4px_0_8px_-2px_rgba(0,0,0,0.1)]"
          : "",
        !isPinned ? "p-2" : ""
      )}
      data-pinned={isPinned ? "true" : "false"}
    >
      <div
        className={cn(isPinned ? "p-2 font-medium pinned-cell-content" : "")}
        data-column-id={columnId}
      >
        <CellContent cell={cell} cellValue={cellValue} />
      </div>
    </TableCell>
  );
});

/**
 * Data row component
 */
const DataRow = memo(function DataRow({
  row,
  pinnedColumns,
  getGeneralColumnStyle,
  getColumnStyle,
}: Readonly<DataRowProps>) {
  const rowData = row.original;

  return (
    <TableRow
      key={row.id}
      data-state={row.getIsSelected() ? "selected" : undefined}
    >
      {row.getVisibleCells().map((cell: Cell<any, any>) => (
        <DataCell
          key={cell.id}
          cell={cell}
          rowData={rowData}
          pinnedColumns={pinnedColumns}
          getGeneralColumnStyle={getGeneralColumnStyle}
          getColumnStyle={getColumnStyle}
        />
      ))}
    </TableRow>
  );
});

/**
 * Data rows component
 */
const DataRows = memo(function DataRows({
  table,
  pinnedColumns,
  getGeneralColumnStyle,
  getColumnStyle,
}: Readonly<DataRowsProps>) {
  return (
    <TableBody>
      {table.getRowModel().rows.map((row) => (
        <DataRow
          key={row.id}
          row={row}
          pinnedColumns={pinnedColumns}
          getGeneralColumnStyle={getGeneralColumnStyle}
          getColumnStyle={getColumnStyle}
        />
      ))}
    </TableBody>
  );
});

/**
 * Body component for the data table
 */
export const DataTableBody = memo(function DataTableBody<TData, TValue>({
  table,
  columns,
  isLoading,
  pinnedColumns,
  getGeneralColumnStyle,
  getColumnStyle,
}: Readonly<DataTableBodyProps<TData, TValue>>) {
  // Determine which component to render based on state
  if (isLoading) {
    return (
      <LoadingState
        table={table as Table<any>}
        columns={columns as ColumnDef<any, any>[]}
        pinnedColumns={pinnedColumns}
        getGeneralColumnStyle={getGeneralColumnStyle}
        getColumnStyle={getColumnStyle}
      />
    );
  }

  // No results
  if (table.getRowModel().rows.length === 0) {
    // Safely access data property through options
    const dataLength = (table.options as any).data?.length ?? 0;
    const rowModelLength = table.getRowModel().rows.length;

    return (
      <EmptyState
        columns={columns as ColumnDef<any, any>[]}
        dataLength={dataLength}
        rowModelLength={rowModelLength}
      />
    );
  }

  // Data rows
  return (
    <DataRows
      table={table as Table<any>}
      pinnedColumns={pinnedColumns}
      getGeneralColumnStyle={getGeneralColumnStyle}
      getColumnStyle={getColumnStyle}
    />
  );
}) as any;
