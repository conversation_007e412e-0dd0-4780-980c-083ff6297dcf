/**
 * This script generates a changelog from conventional commits.
 * It uses the conventional-changelog-cli package to generate the changelog.
 *
 * Usage:
 * - To update the changelog with new entries: node scripts/generate-conventional-changelog.js
 * - To regenerate the entire changelog: node scripts/generate-conventional-changelog.js --all
 */

import { execSync } from "child_process";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import os from "os";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const changelogPath = path.join(__dirname, "..", "CHANGELOG.md");
const tempChangelogPath = path.join(os.tmpdir(), "temp-changelog.md");

// Check if the --all flag is provided
const regenerateAll = process.argv.includes("--all");

try {
  // Check if CHANGELOG.md exists
  const changelogExists = fs.existsSync(changelogPath);

  // If regenerating all and the file exists, create a backup
  if (regenerateAll && changelogExists) {
    const backupPath = `${changelogPath}.backup`;
    fs.copyFileSync(changelogPath, backupPath);
  }

  if (regenerateAll || !changelogExists) {
    // For full regeneration or new file, use the standard approach
    const command =
      "npx conventional-changelog -p angular -i CHANGELOG.md -s -r 0";
    execSync(command, { cwd: path.join(__dirname, ".."), stdio: "inherit" });

    // Add header to the file
    const content = fs.readFileSync(changelogPath, "utf8");
    const header = `# Changelog

All notable changes to the HNES Form Builder project will be documented in this file.

This changelog is automatically generated using [Conventional Commits](https://www.conventionalcommits.org/).
The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.1.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

`;
    fs.writeFileSync(changelogPath, header + content);
  } else {
    // For incremental updates, we'll generate new entries in a temp file
    // and then append them to the existing changelog

    // Create a temporary file for new entries
    if (fs.existsSync(tempChangelogPath)) {
      fs.unlinkSync(tempChangelogPath);
    }
    fs.writeFileSync(tempChangelogPath, "");

    // Get the latest version tag from git
    let latestTag = "";
    try {
      latestTag = execSync("git describe --tags --abbrev=0", {
        cwd: path.join(__dirname, ".."),
        stdio: "pipe",
      })
        .toString()
        .trim();
    } catch (e) {}

    // Generate new entries in the temp file
    // If we have a tag, use --from-tag to only get commits since that tag
    // If no tag, we'll use a different approach to avoid duplication
    if (latestTag) {
      const tempCommand = `npx conventional-changelog -p angular -i ${tempChangelogPath} -s --from-tag ${latestTag}`;
      execSync(tempCommand, {
        cwd: path.join(__dirname, ".."),
        stdio: "inherit",
      });
    } else {
      // If no tags, we'll get all commits but need to check for duplication
      const tempCommand = `npx conventional-changelog -p angular -i ${tempChangelogPath} -s -r 1`;
      execSync(tempCommand, {
        cwd: path.join(__dirname, ".."),
        stdio: "inherit",
      });
    }

    // Read the existing changelog and the new entries
    let existingChangelog = fs.readFileSync(changelogPath, "utf8");
    const newEntries = fs.readFileSync(tempChangelogPath, "utf8").trim();

    if (newEntries === "") {
      process.exit(0);
    }

    // Check for duplicate entries by looking for commit hashes
    // Extract commit hashes from the new entries
    const commitHashRegex = /\(([a-f0-9]{7,40})\)/g;
    const newCommitHashes = [];
    let match;
    while ((match = commitHashRegex.exec(newEntries)) !== null) {
      newCommitHashes.push(match[1]);
    }

    // Check if any of these hashes already exist in the changelog
    let hasDuplicates = false;
    for (const hash of newCommitHashes) {
      if (existingChangelog.includes(hash)) {
        hasDuplicates = true;
      }
    }

    if (hasDuplicates) {
      process.exit(0);
    }

    // Clean up any duplicate sections that might have been added in previous runs
    // This is a safety measure to ensure we don't have multiple identical sections
    const versionHeaderRegex = /## 0\.0\.0 \(2025-05-13\)/g;
    const matches = [...existingChangelog.matchAll(versionHeaderRegex)];

    if (matches.length > 1) {
      // Keep only the first occurrence and everything before it
      const firstOccurrence = matches[0].index;
      const cleanedChangelog = existingChangelog.substring(
        0,
        firstOccurrence + matches[0][0].length
      );

      // Find the links section
      const linksMarker = "[Unreleased]:";
      const linksPos = existingChangelog.indexOf(linksMarker);

      // Combine the cleaned changelog with the links section
      if (linksPos !== -1) {
        const links = existingChangelog.substring(linksPos);
        fs.writeFileSync(changelogPath, cleanedChangelog + "\n\n" + links);

        // Re-read the cleaned changelog
        existingChangelog = fs.readFileSync(changelogPath, "utf8");

        // Exit early since we've cleaned up the file
        process.exit(0);
      }
    }

    // Also check for duplicate entries at the bottom of the file
    if (
      existingChangelog.indexOf("## 0.0.0 (2025-05-13)") !==
      existingChangelog.lastIndexOf("## 0.0.0 (2025-05-13)")
    ) {
      // Regenerate the entire changelog
      const backupPath = `${changelogPath}.cleanup-backup`;
      fs.copyFileSync(changelogPath, backupPath);

      // Run the full regeneration command
      const command =
        "npx conventional-changelog -p angular -i CHANGELOG.md -s -r 0";
      execSync(command, { cwd: path.join(__dirname, ".."), stdio: "inherit" });

      // Add header to the file
      const content = fs.readFileSync(changelogPath, "utf8");
      const header = `# Changelog

All notable changes to the HNES Form Builder project will be documented in this file.

This changelog is automatically generated using [Conventional Commits](https://www.conventionalcommits.org/).
The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.1.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

`;
      fs.writeFileSync(changelogPath, header + content);

      // Exit early since we've regenerated the file
      process.exit(0);
    }

    // Check for duplicate entries by looking for the same commit hash pattern
    const commitHashPattern = /\(([a-f0-9]{7,40})\)/g;
    const hashMatches = [...existingChangelog.matchAll(commitHashPattern)];
    const commitHashes = hashMatches.map((match) => match[1]);

    // Count occurrences of each hash
    const hashCounts = {};
    for (const hash of commitHashes) {
      hashCounts[hash] = (hashCounts[hash] || 0) + 1;
    }

    // Check if any hash appears more than once
    const duplicateHashes = Object.entries(hashCounts)
      .filter(([_, count]) => count > 1)
      .map(([hash]) => hash);

    if (duplicateHashes.length > 0) {
      // Regenerate the entire changelog
      const backupPath = `${changelogPath}.cleanup-backup`;
      fs.copyFileSync(changelogPath, backupPath);

      // Run the full regeneration command
      const command =
        "npx conventional-changelog -p angular -i CHANGELOG.md -s -r 0";
      execSync(command, { cwd: path.join(__dirname, ".."), stdio: "inherit" });

      // Add header to the file
      const content = fs.readFileSync(changelogPath, "utf8");
      const header = `# Changelog

All notable changes to the HNES Form Builder project will be documented in this file.

This changelog is automatically generated using [Conventional Commits](https://www.conventionalcommits.org/).
The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.1.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

`;
      fs.writeFileSync(changelogPath, header + content);

      // Exit early since we've regenerated the file
      process.exit(0);
    }

    // Find the links section at the bottom of the file
    const linksMarker = "[Unreleased]:";
    const linksPos = existingChangelog.indexOf(linksMarker);

    if (linksPos === -1) {
      // If we can't find the links section, just append to the end
      fs.writeFileSync(changelogPath, existingChangelog + "\n\n" + newEntries);
    } else {
      // Split the existing changelog to insert new entries before the links section
      const beforeLinks = existingChangelog.substring(0, linksPos).trim();
      const linksSection = existingChangelog.substring(linksPos);

      // Combine with new entries before the links section
      const updatedChangelog =
        beforeLinks + "\n\n\n" + newEntries + "\n\n" + linksSection;
      fs.writeFileSync(changelogPath, updatedChangelog);
    }

    // Clean up the temp file
    fs.unlinkSync(tempChangelogPath);
  }
} catch (error) {
  console.error("Error generating changelog:", error.message);
  process.exit(1);
}
