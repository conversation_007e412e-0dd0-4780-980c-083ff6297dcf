import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";
import { ShieldAlert } from "lucide-react";

export default function UnauthorizedPage() {
  return (
    <div className="flex min-h-[80vh] flex-col items-center justify-center text-center">
      <div className="mb-4 rounded-full bg-muted p-6">
        <ShieldAlert className="h-10 w-10 text-muted-foreground" />
      </div>
      <h1 className="mb-2 text-3xl font-bold">Access Denied</h1>
      <p className="mb-6 text-muted-foreground">
        You don't have permission to access this page.
      </p>
      <Button asChild>
        <Link to="/">Go to Home</Link>
      </Button>
    </div>
  );
}
