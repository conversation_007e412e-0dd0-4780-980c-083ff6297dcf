import React from "react";
import { Link } from "react-router-dom";
import { FileText } from "lucide-react";
import { cn } from "@/lib/utils";
import OptimizedImage from "../ui/optimized-image";

interface LogoSectionProps {
  variant?: "mobile" | "desktop" | "sidebar";
  isAuthenticated?: boolean;
  userRole?: string;
  sidebarOpen?: boolean;
}

const schemeName = window?.ENV?.VITE_SCHEME_NAME ?? import.meta.env.VITE_SCHEME_NAME;

/**
 * Component for rendering the logo section with GOV.UK and Gemserv logos
 */
export const LogoSection: React.FC<LogoSectionProps> = ({
  variant = "desktop",
  isAuthenticated = false,
  userRole = "",
  sidebarOpen = true,
}) => {
  // Determine the home link based on user role
  const homeLink = userRole === "admin" ? "/forms" : "/applications";

  // For sidebar variant, render a simplified version
  if (variant === "sidebar") {
    return (
      <div
        className={cn(
          "flex items-center gap-2",
          !sidebarOpen && "justify-center"
        )}
      >
        {sidebarOpen ? (
          <Link
            to={homeLink}
            className="flex items-center gap-2 font-semibold"
          >
            <span className="flex items-center ">
              <span className="inline-block mr-1">{schemeName ?? 'Scheme Manager'}</span>
            </span>
          </Link>
        ) : (
          <Link
            to={homeLink}
            className="flex items-center justify-center"
          ></Link>
        )}
      </div>
    );
  }

  // For mobile variant
  if (variant === "mobile") {
    return (
      <div className="flex items-center gap-3">
        <OptimizedImage
          imgUrl={`${window?.ENV?.VITE_BLACK_LOGO_URL ?? import.meta.env.VITE_BLACK_LOGO_URL}`}
          altText="DESNZ Logo"
          fallbackImgUrl="/DESNZ_black_logo.png"
          className="h-24 w-auto dark:hidden"
        />
        <OptimizedImage
          imgUrl={`${window?.ENV?.VITE_WHITE_LOGO_URL ?? import.meta.env.VITE_WHITE_LOGO_URL}`}
          altText="DESNZ Logo"
          fallbackImgUrl="/DESNZ_white_logo.png"
          className="h-24 w-auto hidden dark:block"
        />

        {isAuthenticated ? (
          <Link
            to={homeLink}
            className="flex items-center gap-2 font-semibold"
          >
            <span className="flex items-center dark:text-white text-desnz-navy">
              <span className="inline-block mr-1">{schemeName ?? 'Scheme Manager'}</span>
            </span>
          </Link>
        ) : (
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            <span className="flex items-center ">
              <span className="inline-block mr-1">{schemeName ?? 'Scheme Manager'}</span>
            </span>
          </div>
        )}
      </div>
    );
  }

  // For desktop variant (default)
  return (
    <div className="flex items-center">
      <div className="flex items-center gap-4 mr-6">
        <OptimizedImage
          imgUrl={`${window?.ENV?.VITE_BLACK_LOGO_URL ?? import.meta.env.VITE_BLACK_LOGO_URL}`}
          altText="DESNZ Logo"
          fallbackImgUrl="/DESNZ_black_logo.png"
          className="h-24 w-auto dark:hidden"
        />
        <OptimizedImage
          imgUrl={`${window?.ENV?.VITE_WHITE_LOGO_URL ?? import.meta.env.VITE_WHITE_LOGO_URL}`}
          altText="DESNZ Logo"
          fallbackImgUrl="/DESNZ_white_logo.png"
          className="h-24 w-auto hidden dark:block"
        />
      </div>
      {!isAuthenticated && (
        <div className="flex items-center gap-2">
          <span className="flex items-center text-white">
            <span className="inline-block mr-1">{schemeName ?? 'Scheme Manager'}</span>

          </span>
        </div>
      )}
    </div>
  );
};
