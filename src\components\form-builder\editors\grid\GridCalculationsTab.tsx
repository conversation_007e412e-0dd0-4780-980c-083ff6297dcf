import { memo } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useGridConfiguration } from "@/hooks/useGridConfiguration";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Code } from "lucide-react";

interface GridCalculationsTabProps {
    gridConfig: ReturnType<typeof useGridConfiguration>;
}

const GridCalculationsTab = memo(function GridCalculationsTab({
    gridConfig,
}: Readonly<GridCalculationsTabProps>) {
    const { selectedCell, getCell, updateCell } = gridConfig;

    const selectedCellData = selectedCell ? getCell(selectedCell) : null;

    const handleFormulaChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (selectedCell && selectedCellData) {
            updateCell(selectedCell, {
                ...selectedCellData,
                calculation: {
                    formula: e.target.value,
                },
            });
        }
    };

    return (
        <div className="space-y-4">
            <h3 className="text-lg font-medium">Cell Calculations</h3>
            {selectedCell ? (
                <div className="space-y-4">
                    <div>
                        <Label htmlFor="formula-input">
                            Formula for cell {selectedCell}
                        </Label>
                        <Input
                            id="formula-input"
                            placeholder="e.g., SUM(A1:A5)"
                            value={selectedCellData?.calculation?.formula || ""}
                            onChange={handleFormulaChange}
                        />
                    </div>
                    <Alert>
                        <Code className="h-4 w-4" />
                        <AlertTitle>How to write calculations</AlertTitle>
                        <AlertDescription>
                            <p>
                                You can use functions like <code>SUM</code>, <code>PERCENTAGE</code>.
                            </p>
                            <ul className="list-disc list-inside space-y-1 mt-2">
                                <li>Sum a range of cells: <code>SUM(A2:A10)</code></li>
                                <li>Calculates what percentage D7 is of B7: <code>PERCENTAGE(D7:B7)</code></li>

                            </ul>
                        </AlertDescription>
                    </Alert>
                </div>
            ) : (
                <p className="text-sm text-muted-foreground">
                    Select a cell in the "Cells" tab to configure its calculation.
                </p>
            )}
        </div>
    );
});

export default GridCalculationsTab;