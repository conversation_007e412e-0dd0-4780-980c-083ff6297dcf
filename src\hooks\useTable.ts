import { useState, useCallback, useRef, useMemo, useEffect } from "react";
import { SortingState, PaginationState } from "@tanstack/react-table";
import { TableFilter, TableData } from "@/lib/services/table-service";

interface UseTableProps<T> {
  fetchData: (
    pagination: PaginationState,
    sorting: SortingState,
    filter: TableFilter
  ) => Promise<TableData<T>>;
  initialPageSize?: number;
  initialPageIndex?: number;
  initialSorting?: SortingState;
}

/**
 * Custom hook for managing table state and data fetching
 * Reimplemented with better memoization to prevent infinite loops
 */
export function useTable<T>({
  fetchData,
  initialPageSize = 10,
  initialPageIndex = 0,
  initialSorting = [],
}: UseTableProps<T>) {
  // Table state
  const [data, setData] = useState<T[]>([]);
  const [pageCount, setPageCount] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // Pagination state
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: initialPageIndex,
    pageSize: initialPageSize,
  });

  // Sorting state
  const [sorting, setSorting] = useState<SortingState>(initialSorting);

  // Filter state
  const [filter, setFilter] = useState<TableFilter>({
    globalFilter: "",
    columnFilters: [],
  });

  // Refs for tracking loading state
  const loadingRef = useRef(false);

  // Fetch data when pagination, sorting, or filters change
  const loadData = useCallback(async () => {
    // Prevent duplicate loading
    if (loadingRef.current) return;

    setIsLoading(true);
    setError(null);
    loadingRef.current = true;

    try {
      const result = await fetchData(pagination, sorting, filter);

      // Check if component is still mounted before updating state
      if (loadingRef.current) {
        setData(result.data);
        setPageCount(result.pageCount);
        setTotalCount(result.totalCount);
      }
    } catch (err) {
      if (loadingRef.current) {
        setError(err instanceof Error ? err : new Error("An error occurred"));
        console.error("Error fetching table data:", err);
      }
    } finally {
      if (loadingRef.current) {
        setIsLoading(false);
      }
      loadingRef.current = false;
    }
  }, [fetchData, pagination, sorting, filter]);

  // Initial data load
  useEffect(() => {
    loadData();
  }, [loadData]);

  // Handle pagination change - memoized
  const handlePaginationChange = useCallback(
    (newPagination: PaginationState) => {
      setPagination(newPagination);
    },
    []
  );

  // Effect to load data when pagination changes
  useEffect(() => {
    if (!loadingRef.current) {
      loadData();
    }
  }, [pagination, loadData]);

  // Handle sorting change - memoized
  const handleSortingChange = useCallback((newSorting: SortingState) => {
    setSorting(newSorting);
    // Reset to first page when sorting changes
    setPagination((prev) => ({ ...prev, pageIndex: 0 }));
  }, []);

  // Effect to load data when sorting changes
  useEffect(() => {
    if (!loadingRef.current) {
      loadData();
    }
  }, [sorting, loadData]);

  // Handle filter change - memoized
  const handleFilterChange = useCallback((newFilter: TableFilter) => {
    setFilter(newFilter);
    // Reset to first page when filters change
    setPagination((prev) => ({ ...prev, pageIndex: 0 }));
  }, []);

  // Effect to load data when filter changes
  useEffect(() => {
    if (!loadingRef.current) {
      loadData();
    }
  }, [filter, loadData]);

  // Refresh data manually - memoized
  const refreshData = useCallback(() => {
    // Only refresh if we're not already loading
    if (!loadingRef.current) {
      loadData();
    }
  }, [loadData]);

  // Memoize the return object to prevent unnecessary re-renders
  return useMemo(
    () => ({
      data,
      pageCount,
      totalCount,
      isLoading,
      error,
      pagination,
      sorting,
      filter,
      handlePaginationChange,
      handleSortingChange,
      handleFilterChange,
      refreshData,
    }),
    [
      data,
      pageCount,
      totalCount,
      isLoading,
      error,
      pagination,
      sorting,
      filter,
      handlePaginationChange,
      handleSortingChange,
      handleFilterChange,
      refreshData,
    ]
  );
}
