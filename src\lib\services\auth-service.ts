import { User, LoginCredentials, UserRole } from "../types/auth";
import { CognitoService } from "./cognito-service";

// Mock users for testing (fallback for development/testing)
const mockUsers: User[] = [
  {
    id: "admin-1",
    email: "<EMAIL>",
    name: "Admin User",
    role: "admin",
  },
  {
    id: "applicant-1",
    email: "<EMAIL>",
    name: "Applicant User",
    role: "applicant",
  },
];

// Mock passwords (in a real app, passwords would be hashed and stored securely)
const mockPasswords: Record<string, string> = {
  "<EMAIL>": "admin123",
  "<EMAIL>": "applicant123",
};

// Simulate API delay
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

/**
 * Authentication service
 * Uses Cognito for authentication with fallback to mock users for development/testing
 */
export const AuthService = {
  /**
   * Get user by ID
   * @param id User ID
   * @returns User object if found, null otherwise
   */
  getUserById: async (id: string): Promise<User | null> => {
    try {
      // First try to get the current Cognito user
      const currentUser = await CognitoService.getCurrentUser();

      // If the current user matches the requested ID, return it
      if (currentUser && currentUser.id === id) {
        return currentUser;
      }

      // For now, fall back to mock users for other IDs
      // In a real implementation, you would query Cognito or your user database
      await delay(300); // Simulate network delay
      const user = mockUsers.find((u) => u.id === id);
      return user || null;
    } catch (error) {
      console.error("Error getting user by ID:", error);

      // Fall back to mock users if Cognito fails
      const user = mockUsers.find((u) => u.id === id);
      return user || null;
    }
  },

  /**
   * Login with credentials
   * @param credentials User credentials
   * @returns User object if login successful, null otherwise
   * @deprecated Use CognitoService.redirectToLogin() instead
   */
  login: async (credentials: LoginCredentials): Promise<User | null> => {
    console.warn(
      "AuthService.login is deprecated. Use CognitoService.redirectToLogin() instead."
    );

    try {
      // Try to redirect to Cognito login
      CognitoService.redirectToLogin();
      return null;
    } catch (error) {
      console.error("Failed to redirect to Cognito login:", error);

      // Fall back to mock login for development/testing
      await delay(800); // Simulate network delay

      const { email, password } = credentials;

      // Check if user exists and password matches
      const user = mockUsers.find((u) => u.email === email);
      if (user && mockPasswords[email] === password) {
        // Store user in localStorage for persistence
        localStorage.setItem("auth_user", JSON.stringify(user));
        return user;
      }

      return null;
    }
  },

  /**
   * Logout current user
   */
  logout: async (): Promise<void> => {
    try {
      // Try to logout from Cognito
      await CognitoService.logout();
    } catch (error) {
      console.error("Failed to logout from Cognito:", error);

      // Fall back to mock logout for development/testing
      await delay(300); // Simulate network delay

      // Remove user from localStorage
      localStorage.removeItem("auth_user");
    }
  },

  /**
   * Get current user from Cognito or localStorage (fallback)
   * @returns User object if found, null otherwise
   */
  getCurrentUser: async (): Promise<User | null> => {
    try {
      // First try to get the user from Cognito
      const cognitoUser = await CognitoService.getCurrentUser();
      if (cognitoUser) {
        return cognitoUser;
      }

      // Fall back to localStorage for backward compatibility
      await delay(300); // Simulate network delay
      const userJson = localStorage.getItem("auth_user");
      if (userJson) {
        try {
          return JSON.parse(userJson) as User;
        } catch (error) {
          console.error("Failed to parse user from localStorage:", error);
        }
      }

      return null;
    } catch (error) {
      console.error("Error getting current user:", error);

      // Fall back to localStorage if Cognito fails
      const userJson = localStorage.getItem("auth_user");
      if (userJson) {
        try {
          return JSON.parse(userJson) as User;
        } catch (error) {
          console.error("Failed to parse user from localStorage:", error);
        }
      }

      return null;
    }
  },

  /**
   * Check if user has required role
   * @param user User to check
   * @param requiredRole Required role
   * @returns True if user has required role, false otherwise
   */
  hasRole: (user: User | null, requiredRole: UserRole): boolean => {
    if (!user) return false;

    // Admin role has access to everything
    if (user.role === "admin") return true;

    // Check if user role matches required role
    return user.role === requiredRole;
  },

  /**
   * Get user by email
   * @param email User email
   * @returns User object if found, null otherwise
   */
  getUserByEmail: async (email: string): Promise<User | null> => {
    try {
      // First try to get the current Cognito user
      const currentUser = await CognitoService.getCurrentUser();

      // If the current user matches the requested email, return it
      if (currentUser && currentUser.email === email) {
        return currentUser;
      }

      // For now, fall back to mock users for other emails
      // In a real implementation, you would query Cognito or your user database
      await delay(300); // Simulate network delay
      const user = mockUsers.find((u) => u.email === email);
      return user || null;
    } catch (error) {
      console.error("Error getting user by email:", error);

      // Fall back to mock users if Cognito fails
      const user = mockUsers.find((u) => u.email === email);
      return user || null;
    }
  },
};
