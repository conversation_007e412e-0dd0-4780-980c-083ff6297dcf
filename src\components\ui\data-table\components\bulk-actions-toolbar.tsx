import { But<PERSON> } from "@/components/ui/button";
import { BulkActionConfig } from "@/lib/types/page-config";
import {
  Download,
  FileText,
  FileUp,
  LucideIcon,
  Send,
  Trash2,
  Upload,
} from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface BulkActionsToolbarProps {
  selectedRowIds: string[];
  selectedRows: any[];
  bulkActions: BulkActionConfig[];
  onClearSelection: () => void;
}

export function BulkActionsToolbar({
  selectedRowIds,
  selectedRows,
  bulkActions,
  onClearSelection,
}: Readonly<BulkActionsToolbarProps>) {
  const iconMap: { [key: string]: LucideIcon } = {
    Download,
    Trash2,
    Upload,
    FileUp,
    FileText,
    Send,
  };

  if (selectedRowIds.length === 0 || !bulkActions || bulkActions.length === 0) {
    return null;
  }

  const handleBulkAction = async (action: BulkActionConfig) => {
    try {
      await action.handler(selectedRowIds, selectedRows);
      // Optionally clear selection after action
      // onClearSelection();
    } catch (error) {
      console.error(`Error executing bulk action ${action.id}:`, error);
    }
  };

  const renderActionButton = (action: BulkActionConfig) => {
    const IconComponent =
      typeof action.icon === "string" ? iconMap[action.icon] : action.icon;

    const buttonElement = (
      <Button
        key={action.id}
        variant={action.variant || "default"}
        size={action.size || "sm"}
        onClick={!action.requireConfirmation ? () => handleBulkAction(action) : undefined}
        className="flex items-center gap-2"
      >
        {IconComponent && <IconComponent className="h-4 w-4" />}
        {action.label}
      </Button>
    );

    if (action.requireConfirmation) {
      return (
        <AlertDialog key={action.id}>
          <AlertDialogTrigger asChild>{buttonElement}</AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Confirm Action</AlertDialogTitle>
              <AlertDialogDescription>
                {action.confirmationMessage ||
                  `Are you sure you want to ${action.label.toLowerCase()} ${selectedRowIds.length
                  } selected item(s)?`}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={() => handleBulkAction(action)}>
                Continue
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      );
    }

    return buttonElement;
  };

  return (
    <div className="flex items-center justify-between p-2 border rounded-lg bg-muted/30">
      <div className="flex items-center gap-4">
        <span className="text-sm font-medium text-muted-foreground pl-2">
          {selectedRowIds.length} item{selectedRowIds.length !== 1 ? "s" : ""} selected
        </span>
        <Button
          variant="link"
          size="sm"
          onClick={onClearSelection}
          className="text-muted-foreground hover:text-foreground h-auto p-1 cursor-pointer"
        >
          Clear selection
        </Button>
      </div>

      <div className="flex items-center gap-2">
        {bulkActions.map(renderActionButton)}
      </div>
    </div>
  );
}
