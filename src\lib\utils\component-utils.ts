import { v4 as uuidv4 } from "uuid";
import { FormComponent, FormComponentType } from "@/lib/schemas/form-schemas";

/**
 * Utility functions for working with form components
 */

/**
 * Creates a default component of the specified type
 *
 * @param type - The type of component to create
 * @param parentId - Optional parent ID for nested components
 * @returns A new component with default values
 */
export function createDefaultComponent(
  type: FormComponentType,
  parentId?: string
): FormComponent {
  const id = uuidv4();

  // Adjust label based on component type
  let label = `New ${type.charAt(0).toUpperCase() + type.slice(1)}`;
  if (type !== "step" && type !== "section") {
    label += " Field";
  }

  const baseComponent = {
    id,
    type,
    label,
    name: `field_${id.slice(0, 8)}`,
    required: false,
    parentId,
  };

  switch (type) {
    case "step":
      return {
        ...baseComponent,
        type: "step",
        description: "A new form step",
        children: [],
      };
    case "section":
      return {
        ...baseComponent,
        type: "section",
        description: "A collapsible section",
        collapsible: true,
        defaultCollapsed: false,
        children: [],
      };
    case "text":
      return {
        ...baseComponent,
        type: "text",
        placeholder: "Enter text",
      };
    case "number":
      return {
        ...baseComponent,
        type: "number",
        placeholder: "Enter number",
        min: 0,
      };
    case "date":
      return {
        ...baseComponent,
        type: "date",
      };
    case "datetime":
      return {
        ...baseComponent,
        type: "datetime",
      };
    case "select":
      return {
        ...baseComponent,
        type: "select",
        options: [
          { label: "Option 1", value: "option1" },
          { label: "Option 2", value: "option2" },
        ],
        multiple: false,
      };
    case "checkbox":
      return {
        ...baseComponent,
        type: "checkbox",
      };
    case "radio":
      return {
        ...baseComponent,
        type: "radio",
        options: [
          { label: "Option 1", value: "option1" },
          { label: "Option 2", value: "option2" },
        ],
      };
    case "datagrid":
      return {
        ...baseComponent,
        type: "datagrid",
        rows: 3,
        columns: 3,
        cells: {
          // Default header cells
          A1: { id: "A1", type: "header", value: "", inputType: "text" },
          B1: {
            id: "B1",
            type: "header",
            value: "Column 1",
            inputType: "text",
          },
          C1: {
            id: "C1",
            type: "header",
            value: "Column 2",
            inputType: "text",
          },
          A2: { id: "A2", type: "header", value: "Row 1", inputType: "text" },
          A3: { id: "A3", type: "header", value: "Row 2", inputType: "text" },
          // Default data cells
          B2: { id: "B2", type: "data", value: "", inputType: "text" },
          C2: { id: "C2", type: "data", value: "", inputType: "text" },
          B3: { id: "B3", type: "data", value: "", inputType: "text" },
          C3: { id: "C3", type: "data", value: "", inputType: "text" },
        },
      };
    case "infoText":
      return {
        ...baseComponent,
        type: "infoText",
        label: "Information Text", // More descriptive label for the component list
        name: `info_${id.slice(0, 8)}`, // Unique name for the component
        infoContent:
          "Enter informative text here to help users complete the form.",
        variant: "default",
      };
    default:
      return baseComponent as FormComponent;
  }
}

/**
 * Get all child component IDs recursively
 *
 * @param parentId - The ID of the parent component
 * @param allComponents - All components in the form
 * @returns Array of child component IDs
 */
export function getAllChildIds(
  parentId: string,
  allComponents: FormComponent[]
): string[] {
  const directChildren = allComponents.filter((c) => c.parentId === parentId);
  const childIds = directChildren.map((c) => c.id);

  // Recursively get children of children
  directChildren.forEach((child) => {
    const nestedChildIds = getAllChildIds(child.id, allComponents);
    childIds.push(...nestedChildIds);
  });

  return childIds;
}

/**
 * Get component type name for display
 *
 * @param type - The component type
 * @returns Human-readable component type name
 */
export function getComponentTypeName(type: FormComponentType): string {
  switch (type) {
    case "step":
      return "Form Step";
    case "section":
      return "Form Section";
    case "text":
      return "Text Field";
    case "number":
      return "Number Field";
    case "date":
      return "Date Field";
    case "datetime":
      return "Date Time Field";
    case "select":
      return "Select Field";
    case "checkbox":
      return "Checkbox Field";
    case "radio":
      return "Radio Field";
    case "datagrid":
      return "Data Grid";
    case "infoText":
      return "Information Text";
    default:
      return type;
  }
}

/**
 * Check if a component is a container (step or section)
 *
 * @param type - The component type
 * @returns True if the component is a container
 */
export function isContainerComponent(type: FormComponentType): boolean {
  return type === "step" || type === "section";
}

/**
 * Get background color class for a component type
 *
 * @param type - The component type
 * @returns Tailwind CSS background color class
 */
export function getComponentBgColorClass(type: FormComponentType): string {
  // Original implementation from form-builder-utils.ts
  if (type === "step") {
    return "bg-primary/5";
  } else if (type === "section") {
    return "bg-secondary/10";
  }

  // Enhanced implementation with more component types
  if (type === "datagrid") {
    return "bg-amber-50 dark:bg-amber-950";
  }

  // Default background color for other component types
  return "bg-gray-50 dark:bg-gray-900";
}

/**
 * Checks if a value is empty (undefined, null, or empty string)
 */
function isEmpty(value: any): boolean {
  return value === undefined || value === null || value === "";
}

/**
 * Evaluates array-based conditions
 */
function evaluateArrayCondition(
  fieldValue: any[],
  operator: string,
  value: any
): boolean {
  switch (operator) {
    case "equals":
    case "contains":
      // For arrays, both equals and contains mean the value is in the array
      return fieldValue.includes(value);
    case "notEquals":
      // For arrays, notEquals means the value is not in the array
      return !fieldValue.includes(value);
    default:
      return true;
  }
}

/**
 * Evaluates scalar value conditions
 */
function evaluateScalarCondition(
  fieldValue: any,
  operator: string,
  value: any
): boolean {
  switch (operator) {
    case "equals":
      return fieldValue === value;
    case "notEquals":
      return fieldValue !== value;
    case "contains":
      return typeof fieldValue === "string" && fieldValue.includes(value);
    case "greaterThan":
      return Number(fieldValue) > Number(value);
    case "lessThan":
      return Number(fieldValue) < Number(value);
    default:
      return true;
  }
}

/**
 * Evaluates if a component should be rendered based on conditional rules
 *
 * @param component - The component to evaluate
 * @param watch - React Hook Form watch function
 * @returns True if the component should be rendered
 */
export function evaluateConditionalRendering(
  component: FormComponent,
  watch: any
): boolean {
  // If no conditional rendering is defined, always render the component
  if (!component.conditionalRendering) {
    return true;
  }

  const { field, operator, value } = component.conditionalRendering;

  // If no field is specified, always render
  if (!field) {
    return true;
  }

  // Get the current value of the field this condition depends on
  const fieldValue = watch(field);

  // Special handling for empty/notEmpty operators
  if (operator === "empty") {
    return isEmpty(fieldValue);
  }

  if (operator === "notEmpty") {
    return !isEmpty(fieldValue);
  }

  // If the field doesn't exist or has no value yet, don't render
  // unless the operator is "notEquals" (which would be true if the field is empty)
  if (fieldValue === undefined && operator !== "notEquals") {
    return false;
  }

  // Handle array values (for checkbox groups) differently
  if (Array.isArray(fieldValue)) {
    return evaluateArrayCondition(fieldValue, operator, value);
  }

  // Handle scalar values
  return evaluateScalarCondition(fieldValue, operator, value);
}
