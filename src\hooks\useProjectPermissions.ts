import { useState, useEffect, useCallback } from "react";
import { ProjectPermissionsService } from "@/lib/services/project-permissions-service";
import {PermissionStatus} from "@/lib/types/api.ts";

interface UseProjectPermissionsResult {
  permissions: PermissionStatus | null;
  isLoading: boolean;
  error: Error | null;
  hasPermission: (permission: string) => boolean;
  canUploadDocuments: boolean;
  refetch: () => void;
}

/**
 * Hook to manage project permissions
 */
export function useProjectPermissions(projectId: string | undefined): UseProjectPermissionsResult {
  const [permissions, setPermissions] = useState<PermissionStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchPermissions = useCallback(async () => {
    if (!projectId) {
      setPermissions(null);
      setError(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const projectPermissions = await ProjectPermissionsService.getProjectPermissions(projectId);
      setPermissions(projectPermissions);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Error loading permissions'));
      setPermissions(null);
    } finally {
      setIsLoading(false);
    }
  }, [projectId]);

  useEffect(() => {
    fetchPermissions();
  }, [fetchPermissions]);

  const hasPermission = useCallback((permission: string): boolean => {
    return permissions?.[permission as keyof PermissionStatus] === "ENABLED" || false;
  }, [permissions]);

  const canUploadDocuments = hasPermission("upload");

  return {
    permissions,
    isLoading,
    error,
    hasPermission,
    canUploadDocuments,
    refetch: fetchPermissions
  };
}
