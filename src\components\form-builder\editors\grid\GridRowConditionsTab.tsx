import { useState, useCallback } from "react";
import { GridRowConditionalRendering, DataGridCell } from "@/lib/types/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Trash2, Plus, AlertCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { excelToIndices } from "@/lib/utils/grid-utils";

interface GridRowConditionsTabProps {
  conditionalRows: GridRowConditionalRendering[] | undefined;
  onChange: (conditionalRows: GridRowConditionalRendering[]) => void;
  rows: number;
  columns: number;
  cells: Record<string, DataGridCell>; // Add cells to get cell configuration
}

export default function GridRowConditionsTab({
  conditionalRows = [],
  onChange,
  rows,
  columns,
  cells,
}: Readonly<GridRowConditionsTabProps>) {
  // State for the new condition being added
  const [newCondition, setNewCondition] = useState<GridRowConditionalRendering>(
    {
      cellId: "",
      operator: "equals",
      value: "",
      targetRowIndex: 1, // Default to row 1 (first data row, using 1-based indexing)
    }
  );

  // State for validation errors
  const [error, setError] = useState<string | null>(null);

  // Generate available cell IDs for the dropdown, excluding cells from the target row
  const availableCellIds = useCallback(
    (targetRowIndex: number) => {
      const cellIds: string[] = [];

      // Skip the header row (row 0) and iterate through all data rows
      for (let rowIndex = 1; rowIndex < rows; rowIndex++) {
        // Skip the target row - we don't want to show cells from the same row
        // that the condition applies to
        if (rowIndex === targetRowIndex) {
          continue;
        }

        // Convert from our 1-based data row index to the actual grid row index
        // In the grid, row 1 is the header, row 2 is data row 1, etc.
        const actualRowIndex = rowIndex + 1;

        // Generate cell IDs for all columns in this row
        for (let colIndex = 0; colIndex < columns; colIndex++) {
          const colLetter = String.fromCharCode(65 + colIndex);
          cellIds.push(`${colLetter}${actualRowIndex}`);
        }
      }

      return cellIds;
    },
    [rows, columns]
  );

  // Generate available row indices for the dropdown (1-based, excluding header)
  const availableRowIndices = useCallback(() => {
    // Start from 1 (first data row) and go to rows-1 (last data row)
    // This is 1-based indexing for the UI, where 1 is the first data row after the header
    return Array.from({ length: rows - 1 }, (_, i) => i + 1);
  }, [rows]);

  // Handle changes to the new condition
  const handleNewConditionChange = useCallback(
    <K extends keyof GridRowConditionalRendering>(
      field: K,
      value: GridRowConditionalRendering[K]
    ) => {
      setNewCondition((prev) => {
        // If changing to empty/notEmpty operator, clear the value field as it's not needed
        if (
          field === "operator" &&
          (value === "empty" || value === "notEmpty")
        ) {
          return {
            ...prev,
            [field]: value,
            value: "",
          };
        }

        return {
          ...prev,
          [field]: value,
        };
      });

      // Clear any previous error
      setError(null);
    },
    []
  );

  // Add a new condition
  const handleAddCondition = useCallback(() => {
    // Validate the new condition
    if (!newCondition.cellId) {
      setError("Please select a cell ID");
      return;
    }

    if (
      newCondition.operator !== "empty" &&
      newCondition.operator !== "notEmpty" &&
      !newCondition.value
    ) {
      setError("Please enter a value");
      return;
    }

    // Add the new condition
    onChange([...conditionalRows, { ...newCondition }]);

    // Reset the new condition
    setNewCondition({
      cellId: "",
      operator: "equals",
      value: "",
      targetRowIndex: 1, // Default to row 1 (first data row, using 1-based indexing)
    });

    // Clear any error
    setError(null);
  }, [newCondition, conditionalRows, onChange]);

  // Remove a condition
  const handleRemoveCondition = useCallback(
    (index: number) => {
      const updatedConditions = [...conditionalRows];
      updatedConditions.splice(index, 1);
      onChange(updatedConditions);
    },
    [conditionalRows, onChange]
  );

  // Get cell description (e.g., "Data Row 1, Column B")
  const getCellDescription = useCallback((cellId: string) => {
    if (!cellId || typeof cellId !== "string" || cellId.length < 2) {
      return cellId || "Unknown";
    }

    try {
      const [rowIndex, colIndex] = excelToIndices(cellId);

      // Convert from grid row index to data row index (1-based)
      // Grid row 1 is header, grid row 2 is data row 1, etc.
      const dataRowIndex = rowIndex; // rowIndex is already 0-based, so data row is rowIndex

      return `Data Row ${dataRowIndex}, Column ${String.fromCharCode(
        65 + colIndex
      )}`;
    } catch {
      // If we can't parse the cell ID, just return it as is
      return cellId;
    }
  }, []);

  // Get the selected cell configuration
  const getSelectedCellConfig = useCallback(
    (cellId: string) => {
      if (!cellId || !cells[cellId]) {
        return null;
      }
      return cells[cellId];
    },
    [cells]
  );

  // Check if the selected cell is a select type
  const isSelectCell = useCallback(
    (cellId: string) => {
      const cellConfig = getSelectedCellConfig(cellId);
      return (
        cellConfig?.inputType === "select" &&
        Array.isArray(cellConfig?.options) &&
        cellConfig.options.length > 0
      );
    },
    [getSelectedCellConfig]
  );

  // Get options for a select cell
  const getSelectOptions = useCallback(
    (cellId: string) => {
      const cellConfig = getSelectedCellConfig(cellId);
      return cellConfig?.options || [];
    },
    [getSelectedCellConfig]
  );

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h3 className="text-lg font-medium">Row Conditional Rendering</h3>
        <p className="text-sm text-muted-foreground">
          Configure conditions to show or hide specific rows based on values in
          cells from other rows. Each condition applies to a specific target row
          and will show that row when the condition is met. For example, you can
          show Row 2 only when a cell in Row 1 has a specific value.
        </p>
      </div>

      {/* List of existing conditions */}
      {conditionalRows.length > 0 ? (
        <div className="space-y-4">
          {conditionalRows.map((condition, index) => (
            <Card
              key={`condition-${condition.cellId}-${condition.targetRowIndex}-${index}`}
              className="relative"
            >
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  Condition {index + 1}
                </CardTitle>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="flex flex-col gap-2 text-sm">
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="font-medium">
                      Applies to Row {condition.targetRowIndex} (Data Row{" "}
                      {condition.targetRowIndex})
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="font-medium">
                      If Cell {condition.cellId} (
                      {getCellDescription(condition.cellId)})
                    </span>
                    <span>{condition.operator}</span>
                    {condition.operator !== "empty" &&
                      condition.operator !== "notEmpty" && (
                        <span>
                          "
                          {isSelectCell(condition.cellId)
                            ? getSelectOptions(condition.cellId).find(
                                (opt) => opt.value === condition.value
                              )?.label ?? condition.value
                            : condition.value}
                          "
                        </span>
                      )}
                  </div>
                </div>
              </CardContent>
              <CardFooter className="pt-0 flex justify-end">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleRemoveCondition(index)}
                >
                  <Trash2 className="h-4 w-4 mr-1" />
                  Remove
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-sm text-muted-foreground bg-muted/30 p-4 rounded-md">
          No conditions defined. All rows will be visible.
        </div>
      )}

      {/* Add new condition */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">
            Add New Condition
          </CardTitle>
          <CardDescription>
            Define when a row should be visible based on a cell value
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-2">
            <Label htmlFor="target-row">Target Row</Label>
            <Select
              value={newCondition.targetRowIndex.toString()}
              onValueChange={(value) =>
                handleNewConditionChange("targetRowIndex", parseInt(value))
              }
            >
              <SelectTrigger id="target-row">
                <SelectValue placeholder="Select a row" />
              </SelectTrigger>
              <SelectContent>
                {availableRowIndices().map((rowIndex) => (
                  <SelectItem
                    key={`row-${rowIndex}`}
                    value={rowIndex.toString()}
                  >
                    Row {rowIndex} (Data Row {rowIndex})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground">
              Select which row this condition will control
            </p>
          </div>

          <div className="grid gap-2">
            <Label htmlFor="cell-id">Cell ID</Label>
            <Select
              value={newCondition.cellId}
              onValueChange={(value) =>
                handleNewConditionChange("cellId", value)
              }
            >
              <SelectTrigger id="cell-id">
                <SelectValue placeholder="Select a cell" />
              </SelectTrigger>
              <SelectContent>
                {availableCellIds(newCondition.targetRowIndex).map((cellId) => (
                  <SelectItem key={cellId} value={cellId}>
                    {cellId} - {getCellDescription(cellId)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground">
              Select a cell from another row to base the condition on
            </p>
          </div>

          <div className="grid gap-2">
            <Label htmlFor="operator">Operator</Label>
            <Select
              value={newCondition.operator}
              onValueChange={(value) =>
                handleNewConditionChange(
                  "operator",
                  value as GridRowConditionalRendering["operator"]
                )
              }
            >
              <SelectTrigger id="operator">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="equals">Equals</SelectItem>
                <SelectItem value="notEquals">Not Equals</SelectItem>
                <SelectItem value="contains">Contains</SelectItem>
                <SelectItem value="greaterThan">Greater Than</SelectItem>
                <SelectItem value="lessThan">Less Than</SelectItem>
                <SelectItem value="empty">Empty</SelectItem>
                <SelectItem value="notEmpty">Not Empty</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {newCondition.operator !== "empty" &&
            newCondition.operator !== "notEmpty" && (
              <div className="grid gap-2">
                <Label htmlFor="value">Value</Label>
                {isSelectCell(newCondition.cellId) ? (
                  // Show dropdown for select cells
                  <Select
                    value={newCondition.value}
                    onValueChange={(value) =>
                      handleNewConditionChange("value", value)
                    }
                  >
                    <SelectTrigger id="value">
                      <SelectValue placeholder="Select a value" />
                    </SelectTrigger>
                    <SelectContent>
                      {getSelectOptions(newCondition.cellId).map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                ) : (
                  // Show text input for other cell types
                  <Input
                    id="value"
                    value={newCondition.value}
                    onChange={(e) =>
                      handleNewConditionChange("value", e.target.value)
                    }
                    placeholder="Enter value"
                  />
                )}
              </div>
            )}

          {error && (
            <div className="text-xs text-destructive flex items-center gap-1">
              <AlertCircle className="h-3 w-3" />
              <span>{error}</span>
            </div>
          )}
        </CardContent>
        <CardFooter>
          <Button onClick={handleAddCondition} className="w-full">
            <Plus className="h-4 w-4 mr-1" />
            Add Condition
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
