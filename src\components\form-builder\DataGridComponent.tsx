import { memo, useCallback } from "react";
import { DataGridComponent as DataGridType } from "@/lib/schemas/form-schemas";
import { useFormState } from "@/contexts/FormStateContext";
import {
  Table,
  TableBody,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import GridHeaders from "./grid/GridHeaders";
import GridRow from "./grid/GridRow";

// Import custom hooks and components
import { useDataGrid } from "@/hooks/useDataGrid";
import { useMemoWithDeps } from "@/hooks/useMemoWithDeps";
import {
  transformToFlat,
  StructuredDataGrid,
} from "@/lib/utils/datagrid-transformer";
import { evaluateRowConditionalRendering } from "@/lib/utils/grid-utils";

interface DataGridComponentProps {
  readonly component: DataGridType;
  readonly value:
  | Record<string, any>
  | { flat: Record<string, any>; structured: any };
  readonly onChange: (value: Record<string, any>) => void;
  readonly mode?: "edit" | "preview" | "submission";
  readonly calculatedValues?: Record<string, any>;
}

/**
 * Normalizes grid value to flat format using custom cell IDs
 * Returns data in format: { "custom-id": "value", ... }
 */
function normalizeGridValue(
  value: DataGridComponentProps["value"],
  component: DataGridType
): Record<string, any> {
  // If value is empty, return empty object
  if (!value) return {};

  // If value is not an object, return empty object
  if (typeof value !== "object") return {};

  // If value is in the new format with flat and structured properties
  if ("flat" in value && "structured" in value) {
    return value.flat; // Already in custom ID format
  }

  // If value is in the structured format (from saved data)
  if ("rows" in value && "metadata" in value) {
    try {
      // transformToFlat now returns data in custom ID format
      return transformToFlat(value as StructuredDataGrid, component);
    } catch (error: unknown) {
      console.error("Error transforming structured data to flat:", error);
      return {};
    }
  }

  // Otherwise, assume it's already in flat custom ID format
  return value;
}

function DataGridComponent({
  component,
  value,
  onChange,
  mode = "edit",
  calculatedValues,
}: DataGridComponentProps) {
  const {
    state: { form, submission },
  } = useFormState();

  const gridCalculatedValues = calculatedValues?.[component.id] || {};

  // Normalize grid value to flat format
  const flatValue = useMemoWithDeps(
    () => normalizeGridValue(value, component),
    [value, component]
  );

  // Use our custom hook for grid data management
  const { getCellValue, getCellError, handleCellChange } = useDataGrid({
    component,
    value: flatValue,
    onChange,
    formSchema: form,
    formValues: submission?.data,
    calculatedValues: gridCalculatedValues,
  });

  // Memoized cell access functions
  const memoizedGetCellValue = useCallback(getCellValue, [getCellValue]);
  const memoizedGetCellError = useCallback(getCellError, [getCellError]);
  const memoizedHandleCellChange = useCallback(handleCellChange, [
    handleCellChange,
  ]);

  const dataRows = Array.from({ length: component.rows - 1 }, (_, i) => i + 1);

  return (
    <div className="space-y-2 overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <GridHeaders
              columns={component.columns}
              cells={component.cells}
            />
          </TableRow>
        </TableHeader>
        <TableBody>
          {dataRows.map((rowIndex) => {
            const shouldRenderRow =
              mode === "edit" ||
              evaluateRowConditionalRendering(
                rowIndex,
                component.conditionalRows,
                memoizedGetCellValue
              );

            if (!shouldRenderRow) {
              return null;
            }

            return (
              <GridRow
                key={rowIndex}
                rowIndex={rowIndex}
                columns={component.columns}
                cells={component.cells}
                getCellValue={memoizedGetCellValue}
                getCellError={memoizedGetCellError}
                onCellChange={memoizedHandleCellChange}
                calculatedValues={gridCalculatedValues}
              />
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}

export default memo(DataGridComponent);
