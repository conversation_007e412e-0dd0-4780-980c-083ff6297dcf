# API Integration with TanStack Query

This directory contains the API integration layer for the application, built with TanStack Query (React Query).

## Overview

The API integration is structured to provide:

1. **Consistent Data Fetching**: Standardized approach to fetching and caching data
2. **Type Safety**: Full TypeScript support for API requests and responses
3. **Error Handling**: Centralized error handling with toast notifications
4. **Mock API Support**: Ability to switch between mock and real API for development
5. **Optimistic Updates**: Support for optimistic UI updates

## Key Components

### QueryProvider

The `QueryProvider` component in `src/lib/providers/query-provider.tsx` sets up the TanStack Query client with sensible defaults:

- **Stale Time**: 5 minutes for most queries
- **Cache Time**: 10 minutes
- **Retry Logic**: 3 retries with exponential backoff
- **Focus Refetching**: Disabled in development for easier debugging

### API Client

The `apiClient` in `src/lib/api/api-client.ts` provides a standardized interface for making HTTP requests:

- **Authentication**: Automatically adds authentication tokens
- **Error Handling**: Standardized error handling
- **Response Formatting**: Consistent response structure

### Mock API

The `mockApi` in `src/lib/api/mock-api.ts` provides a mock implementation of the API for development:

- **Simulated Latency**: Realistic network delays
- **In-Memory Storage**: Simulates a backend database
- **API Parity**: Matches the real API interface

### API Switch

The `useApiSwitch` hook in `src/hooks/useApiSwitch.ts` allows toggling between mock and real API:

- **Persistent Setting**: Saves preference in localStorage
- **Runtime Switching**: Can switch modes without reloading
- **UI Component**: Includes a UI component for toggling

## Usage Examples

### Basic Query

```tsx
import { useForms } from "@/hooks/useFormsWithMock";

function FormsList() {
  const { data: forms, isLoading, error } = useForms();
  
  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  
  return (
    <ul>
      {forms?.map(form => (
        <li key={form.id}>{form.title}</li>
      ))}
    </ul>
  );
}
```

### Mutation

```tsx
import { useCreateForm } from "@/hooks/useFormsWithMock";

function CreateFormButton() {
  const createForm = useCreateForm();
  
  const handleClick = () => {
    createForm.mutate({
      title: "New Form",
      description: "Form description",
      components: []
    });
  };
  
  return (
    <button 
      onClick={handleClick}
      disabled={createForm.isPending}
    >
      {createForm.isPending ? "Creating..." : "Create Form"}
    </button>
  );
}
```

## Query Keys

Query keys are defined in `src/lib/types/api.ts` and follow a consistent pattern:

- **Base Key**: `['api']`
- **Entity Type**: e.g., `['api', 'forms']`
- **Entity ID**: e.g., `['api', 'forms', '123']`
- **Nested Resources**: e.g., `['api', 'forms', '123', 'submissions']`
- **Filters**: e.g., `['api', 'forms', { status: 'active' }]`

## Best Practices

1. **Use Entity-Specific Hooks**: Prefer using the entity-specific hooks like `useForms` instead of generic `useApiQuery`
2. **Invalidate Queries**: After mutations, invalidate related queries to keep data fresh
3. **Error Handling**: Let the hooks handle errors by default, but override when needed
4. **Optimistic Updates**: Use optimistic updates for better UX when appropriate
5. **Prefetching**: Prefetch data when you know it will be needed soon

## Extending

To add support for a new entity type:

1. Add query keys in `src/lib/types/api.ts`
2. Add mock API implementation in `src/lib/api/mock-api.ts`
3. Create entity-specific hooks in a new file like `src/hooks/useEntityWithMock.ts`
4. Use the hooks in your components
