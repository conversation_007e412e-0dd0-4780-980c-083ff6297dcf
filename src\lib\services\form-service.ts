import { apiClient } from "../api/api-client";
import { FormSchema, FormStatus } from "../schemas/form-schemas";

export const FormService = {
  // Get all forms with optional filtering
  getForms: async (status?: FormStatus): Promise<FormSchema[]> => {
    const response = await apiClient.get<FormSchema[]>("/forms", {
      params: { status },
    });
    return response.data;
  },

  // Get a specific form by ID
  getFormById: async (id: string): Promise<FormSchema | null> => {
    const response = await apiClient.get<FormSchema>(`/forms/${id}`);
    return response.data;
  },

  // Create a new form
  createForm: async (
    form: Omit<FormSchema, "id" | "createdAt" | "updatedAt">
  ): Promise<FormSchema> => {
    const response = await apiClient.post<FormSchema>("/forms", form);

    return response.data;
  },

  // Update an existing form
  updateForm: async (
    id: string,
    form: Partial<FormSchema>
  ): Promise<FormSchema> => {
    const response = await apiClient.put<FormSchema>(`/forms/${id}`, form);
    return response.data;
  },

  // Delete a form
  deleteForm: async (id: string): Promise<boolean> => {
    await apiClient.delete(`/forms/${id}`);
    return true;
  },
  activateForm: async (id: string): Promise<FormSchema> => {
    const response = await apiClient.patch<FormSchema>(`/forms/activate/${id}`);

    return response.data;
  },
};
