import { memo } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { useDroppable } from "@dnd-kit/core";



function EmptyFormArea() {
  const { setNodeRef } = useDroppable({
    id: "form-drop-area",
  });

  return (
    <Card
      ref={setNodeRef}
      className="border-2 border-dashed border-muted-foreground/25 hover:border-primary/50 transition-colors"
    >
      <CardContent className="flex flex-col items-center justify-center p-6">
        <p className="mb-4 text-center text-muted-foreground">
          Drag components here or use the panel on the right to add
          components.
        </p>

      </CardContent>
    </Card>
  );
}

export default memo(EmptyFormArea);
