import { useState, useCallback } from "react";

interface UseSidebarReturn {
  sidebarOpen: boolean;
  mobileMenuOpen: boolean;
  toggleSidebar: () => void;
  toggleMobileMenu: () => void;
  setMobileMenuOpen: (open: boolean) => void;
}

/**
 * Hook to manage sidebar and mobile menu state
 */
export function useSidebar(): UseSidebarReturn {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const toggleSidebar = useCallback(() => {
    setSidebarOpen((prev) => !prev);
  }, []);

  const toggleMobileMenu = useCallback(() => {
    setMobileMenuOpen((prev) => !prev);
  }, []);

  return {
    sidebarOpen,
    mobileMenuOpen,
    toggleSidebar,
    toggleMobileMenu,
    setMobileMenuOpen,
  };
}
