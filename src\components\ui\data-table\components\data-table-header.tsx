import { flexRender, Table } from "@tanstack/react-table";
import { TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Search, SortAsc, SortDesc, ArrowUpDown, Pin } from "lucide-react";
import { cn } from "@/lib/utils";

interface DataTableHeaderProps<TData> {
  table: Table<TData>;
  enableColumnFilters: boolean;
  showPinningControls: boolean;
  pinnedColumns: Record<string, number>;
  canColumnBePinned: (columnId: string) => boolean;
  handlePinColumn: (columnId: string) => void;
  getGeneralColumnStyle: (columnId: string) => React.CSSProperties;
  getColumnStyle: (columnId: string) => React.CSSProperties;
}

/**
 * Header component for the data table
 */
export function DataTableHeader<TData>({
  table,
  enableColumnFilters,
  showPinningControls,
  pinnedColumns,
  canColumnBePinned,
  handlePinColumn,
  getGeneralColumnStyle,
  getColumnStyle,
}: Readonly<DataTableHeaderProps<TData>>) {
  return (
    <TableHeader>
      {table.getHeaderGroups().map((headerGroup) => (
        <TableRow key={headerGroup.id}>
          {headerGroup.headers.map((header) => (
            <TableHead
              key={header.id}
              style={{
                ...getGeneralColumnStyle(header.id),
                ...getColumnStyle(header.id),
              }}
              className={cn(
                "transition-colors hover:bg-muted table-cell",
                pinnedColumns[header.id] ? "bg-primary/5" : "bg-muted/50",
                !pinnedColumns[header.id] ? "p-2" : ""
              )}
              data-pinned={pinnedColumns[header.id] ? "true" : "false"}
            >
              <div
                className={cn(
                  "flex items-center justify-between p-2 font-medium",
                  pinnedColumns[header.id]
                    ? "text-primary/80 pinned-cell-content"
                    : ""
                )}
                data-column-id={header.id}
              >
                {header.isPlaceholder ? null : (
                  <div className="flex items-center gap-2 font-semibold">
                    <span>
                      {flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                    </span>
                    {header.column.getCanSort() && (
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6 p-0"
                        onClick={header.column.getToggleSortingHandler()}
                        aria-label={(() => {
                          const sortState = header.column.getIsSorted();
                          if (sortState === "asc") return "Sort descending";
                          if (sortState === "desc") return "Clear sort";
                          return "Sort ascending";
                        })()}
                      >
                        {(() => {
                          const sortState = header.column.getIsSorted();
                          if (sortState === "asc")
                            return <SortAsc className="h-4 w-4" />;
                          if (sortState === "desc")
                            return <SortDesc className="h-4 w-4" />;
                          return <ArrowUpDown className="h-4 w-4 opacity-50" />;
                        })()}
                      </Button>
                    )}
                  </div>
                )}
                {/* Show pin button only if pinning is enabled globally,
                    for this column, and if pin controls should be shown */}
                {showPinningControls && canColumnBePinned(header.id) && (
                  <Button
                    variant={pinnedColumns[header.id] ? "secondary" : "ghost"}
                    size="icon"
                    className={cn(
                      "h-6 w-6 ml-1 transition-all rounded-sm",
                      pinnedColumns[header.id]
                        ? "bg-primary/20 hover:bg-primary/30 border border-primary/30 shadow-sm"
                        : "hover:bg-muted hover:text-primary"
                    )}
                    onClick={() => handlePinColumn(header.id)}
                    aria-label={
                      pinnedColumns[header.id] ? "Unpin column" : "Pin column"
                    }
                    title={
                      pinnedColumns[header.id]
                        ? `Unpin column (Order: ${pinnedColumns[header.id]})`
                        : "Pin column"
                    }
                  >
                    <Pin
                      className={cn(
                        "h-3.5 w-3.5 transition-all",
                        pinnedColumns[header.id]
                          ? "fill-primary text-primary"
                          : "text-muted-foreground"
                      )}
                    />
                    {Boolean(pinnedColumns[header.id]) && (
                      <span className="sr-only">
                        {pinnedColumns[header.id]}
                      </span>
                    )}
                  </Button>
                )}
              </div>

              {/* Column Filters */}
              {enableColumnFilters && header.column.getCanFilter() && (
                <div
                  className={cn(
                    "mt-2 px-2 pb-2 relative filter-input-container",
                    pinnedColumns[header.id] ? "bg-primary/5" : ""
                  )}
                >
                  <div className="relative group">
                    <Label
                      htmlFor={`filter-${header.id}`}
                      className="sr-only"
                    >
                      Filter {header.column.columnDef.header as string}
                    </Label>
                    <Search className="search-icon absolute left-2 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-muted-foreground/70 group-hover:text-primary/70 transition-colors" />
                    <Input
                      id={`filter-${header.id}`}
                      placeholder={`Filter ${header.column.columnDef.header as string
                        }`}
                      value={(header.column.getFilterValue() as string) ?? ""}
                      onChange={(e) =>
                        header.column.setFilterValue(e.target.value)
                      }
                      className={cn(
                        "h-7 w-full pl-8 text-sm transition-all",
                        "border-muted-foreground/20 focus-visible:border-primary/30 focus-visible:ring-primary/20",
                        pinnedColumns[header.id]
                          ? "bg-background/80 hover:bg-background focus:bg-background"
                          : "bg-background/50 hover:bg-background/80"
                      )}
                    />
                    {(header.column.getFilterValue() as string) && (
                      <button
                        onClick={() => header.column.setFilterValue("")}
                        className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 rounded-full bg-muted-foreground/10 hover:bg-muted-foreground/20 flex items-center justify-center transition-colors"
                        aria-label="Clear filter"
                      >
                        <span className="text-muted-foreground text-xs font-medium">
                          ×
                        </span>
                      </button>
                    )}
                  </div>
                </div>
              )}
            </TableHead>
          ))}
        </TableRow>
      ))}
    </TableHeader>
  );
}
