import { PermissionStatus } from "./api";

/**
 * Navigation item structure for frontend components
 * Extends the backend NavigationItem with computed properties
 */
export interface NavItem {
  id: string;
  name: string;
  href: string;
  icon: React.ReactNode; // Rendered icon component
  active: boolean; // Computed based on current route
  order: number;
  parentId?: string;
  hasPermission: boolean; // Computed from permissions
}

/**
 * Navigation state and actions
 */
export interface NavigationState {
  items: NavItem[];
  isLoading: boolean;
  error: string | null;
  lastFetched: Date | null;
}

/**
 * Global permission actions for pages
 */
export type GlobalPageAction = "view" | "create" | "getAll";

/**
 * Page permission structure
 */
export interface PagePermissions {
  view: PermissionStatus;
  create: PermissionStatus;
  getAll: PermissionStatus;
}

/**
 * Global permissions state
 */
export interface GlobalPermissionsState {
  pages: Record<string, PagePermissions>;
  navigation: Record<string, PermissionStatus>;
  isLoading: boolean;
  error: string | null;
  lastFetched: Date | null;
}

/**
 * Icon mapping for navigation items
 * Maps backend icon identifiers to React components
 */
export interface IconMapping {
  [iconId: string]: React.ReactNode;
}

/**
 * Navigation configuration options
 */
export interface NavigationConfig {
  enableCaching: boolean;
  cacheTimeout: number; // in milliseconds
  fallbackToClientSide: boolean;
  refreshOnUserChange: boolean;
}
