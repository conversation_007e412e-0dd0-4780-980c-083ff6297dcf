import { useCallback, useEffect, useRef } from "react";
import { UseFormReturn } from "react-hook-form";
import { FieldConfig } from "@/lib/types/page-config";
import {
  createFieldContext,
  resolveDynamicOptions,
  getFieldDependencies,
  clearOptionsCache,
} from "@/lib/utils/field-resolver";

/**
 * Hook to manage field dependencies and trigger updates when dependent fields change
 */
export function useFieldDependencies(
  fields: FieldConfig[],
  methods: UseFormReturn<any>,
  initialData?: Record<string, any>
) {
  const { watch, setValue } = methods;
  const dependencyMapRef = useRef<Map<string, string[]>>(new Map());
  const isUpdatingRef = useRef<Set<string>>(new Set());

  // Build dependency map on mount or when fields change
  useEffect(() => {
    const dependencyMap = new Map<string, string[]>();

    fields.forEach((field) => {
      const dependencies = getFieldDependencies(field);
      if (dependencies.length > 0) {
        dependencyMap.set(field.id, dependencies);
      }
    });

    dependencyMapRef.current = dependencyMap;
  }, [fields]);

  // Function to update dependent fields
  const updateDependentFields = useCallback(
    async (changedFieldName: string, formValues: Record<string, any>) => {
      const dependencyMap = dependencyMapRef.current;

      // Find fields that depend on the changed field
      const dependentFields = fields.filter((field) => {
        const dependencies = dependencyMap.get(field.id);
        return dependencies?.includes(changedFieldName);
      });

      if (dependentFields.length === 0) return;

      // Create context for resolvers
      const context = createFieldContext(formValues, initialData, fields);

      // Update each dependent field
      for (const field of dependentFields) {
        // Skip if we're already updating this field to prevent infinite loops
        if (isUpdatingRef.current.has(field.id)) continue;

        try {
          isUpdatingRef.current.add(field.id);

          // Clear cache for this field
          clearOptionsCache(field.id);

          // Resolve new options if field has dynamic options
          if (field.dynamicOptionsConfig || field.dynamicOptions) {
            const newOptions = await resolveDynamicOptions(field, context);

            // Update the field's options in the form component
            // This is handled by re-rendering the component with new options
            // The actual options update happens in the form rendering logic

            // Clear the current value if it's no longer valid
            const currentValue = formValues[field.name];
            if (currentValue && newOptions.length > 0) {
              const isValidValue = newOptions.some(
                (option) => option.value === currentValue
              );
              if (!isValidValue) {
                setValue(field.name, "", { shouldValidate: false });
              }
            } else if (
              field.dynamicOptionsConfig?.clearOnEmptyDependency &&
              newOptions.length === 0
            ) {
              setValue(field.name, "", { shouldValidate: false });
            }
          }
        } catch (error) {
          console.error(`Error updating dependent field ${field.id}:`, error);
        } finally {
          isUpdatingRef.current.delete(field.id);
        }
      }
    },
    [fields, setValue, initialData]
  );

  // Watch for changes in fields that have dependents
  const watchedFields = Array.from(
    new Set(Array.from(dependencyMapRef.current.values()).flat())
  );

  useEffect(() => {
    if (watchedFields.length === 0) return;

    const subscription = watch((formValues, { name }) => {
      if (name && watchedFields.includes(name)) {
        // Debounce the update to avoid excessive API calls
        const timeoutId = setTimeout(() => {
          updateDependentFields(name, formValues);
        }, 300);

        return () => clearTimeout(timeoutId);
      }
    });

    return subscription.unsubscribe;
  }, [watch, watchedFields, updateDependentFields]);

  // Function to manually trigger dependency updates
  const triggerDependencyUpdate = useCallback(
    (fieldName: string) => {
      const formValues = methods.getValues();
      updateDependentFields(fieldName, formValues);
    },
    [methods, updateDependentFields]
  );

  // Function to get fields that depend on a specific field
  const getDependentFields = useCallback(
    (fieldName: string): FieldConfig[] => {
      return fields.filter((field) => {
        const dependencies = dependencyMapRef.current.get(field.id);
        return dependencies?.includes(fieldName);
      });
    },
    [fields]
  );

  // Function to check if a field has dependencies
  const fieldHasDependencies = useCallback((fieldName: string): boolean => {
    return dependencyMapRef.current.has(fieldName);
  }, []);

  return {
    triggerDependencyUpdate,
    getDependentFields,
    fieldHasDependencies,
    dependencyMap: dependencyMapRef.current,
  };
}
