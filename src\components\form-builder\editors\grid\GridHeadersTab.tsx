import { memo } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";

interface GridHeadersTabProps {
  columnHeaders: string[];
  rowNumbers: number[];
  gridConfig: any; // Using any for brevity, but should be properly typed
}

/**
 * Tab for configuring grid headers
 */
const GridHeadersTab = memo(function GridHeadersTab({
  columnHeaders,
  rowNumbers,
  gridConfig,
}: GridHeadersTabProps) {
  return (
    <div className="space-y-4">
      <div>
        <h4 className="text-sm font-medium mb-2">Column Headers</h4>
        <div className="grid grid-cols-2 gap-2">
          {columnHeaders.map((header, colIndex) => (
            <div
              key={`col-header-${header}-${colIndex}`}
              className="flex items-center gap-2"
            >
              <span className="w-8 text-center font-medium">
                {header}
              </span>
              <Label
                htmlFor={`col-header-input-${header}-${colIndex}`}
                className="sr-only"
              >
                Column {header} Header
              </Label>
              <Input
                id={`col-header-input-${header}-${colIndex}`}
                value={gridConfig.getCellValue(0, colIndex)}
                onChange={(e) =>
                  gridConfig.handleCellValueChange(0, colIndex, e.target.value)
                }
                placeholder={`Column ${header}`}
                className="flex-1"
              />
              <Switch
                id={`col-header-switch-${header}-${colIndex}`}
                checked={gridConfig.isCellHeader(0, colIndex)}
                onCheckedChange={(checked) =>
                  gridConfig.handleCellTypeChange(
                    0,
                    colIndex,
                    checked ? "header" : "data"
                  )
                }
                aria-label={`Toggle header for column ${header}`}
              />
            </div>
          ))}
        </div>
      </div>

      <div>
        <h4 className="text-sm font-medium mb-2">Row Headers</h4>
        <div className="grid grid-cols-2 gap-2">
          {rowNumbers.map((rowNum, rowIndex) => (
            <div
              key={`row-header-${rowNum}-${rowIndex}`}
              className="flex items-center gap-2"
            >
              <span className="w-8 text-center font-medium">
                {rowNum}
              </span>
              <Label
                htmlFor={`row-header-input-${rowNum}-${rowIndex}`}
                className="sr-only"
              >
                Row {rowNum} Header
              </Label>
              <Input
                id={`row-header-input-${rowNum}-${rowIndex}`}
                value={gridConfig.getCellValue(rowIndex, 0)}
                onChange={(e) =>
                  gridConfig.handleCellValueChange(rowIndex, 0, e.target.value)
                }
                placeholder={`Row ${rowNum}`}
                className="flex-1"
              />
              <Switch
                id={`row-header-switch-${rowNum}-${rowIndex}`}
                checked={gridConfig.isCellHeader(rowIndex, 0)}
                onCheckedChange={(checked) =>
                  gridConfig.handleCellTypeChange(
                    rowIndex,
                    0,
                    checked ? "header" : "data"
                  )
                }
                aria-label={`Toggle header for row ${rowNum}`}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
});

export default GridHeadersTab;
