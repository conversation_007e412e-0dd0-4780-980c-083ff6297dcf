import { useState, useEffect } from "react";
import { ColumnFiltersState } from "@tanstack/react-table";
import { TableFilter, ColumnFilter } from "@/lib/services/table-service";

interface UseTableFilterProps {
  onFilterChange: (filter: TableFilter) => void;
}

interface UseTableFilterResult {
  columnFilters: ColumnFiltersState;
  setColumnFilters: React.Dispatch<React.SetStateAction<ColumnFiltersState>>;
  globalFilter: string;
  setGlobalFilter: React.Dispatch<React.SetStateAction<string>>;
}

/**
 * Custom hook to manage table filtering
 */
export function useTableFilter({
  onFilterChange,
}: UseTableFilterProps): UseTableFilterResult {
  // State for column filters
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);

  // State for global filter
  const [globalFilter, setGlobalFilter] = useState("");

  // Update filters when they change
  useEffect(() => {
    // Use setTimeout to debounce and prevent rapid re-renders
    const timeoutId = setTimeout(() => {
      const columnFiltersArray: ColumnFilter[] = columnFilters.map(
        (filter) => ({
          id: filter.id,
          value: filter.value as string,
        })
      );

      onFilterChange({
        globalFilter,
        columnFilters: columnFiltersArray,
      });
    }, 100);

    return () => clearTimeout(timeoutId);
  }, [columnFilters, globalFilter, onFilterChange]);

  return {
    columnFilters,
    setColumnFilters,
    globalFilter,
    setGlobalFilter,
  };
}
