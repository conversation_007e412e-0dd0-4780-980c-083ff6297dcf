import { useState, useEffect, useCallback, useMemo, useRef } from "react";
import { DataGridComponent as DataGridType } from "@/lib/schemas/form-schemas";
import { indicesToExcel } from "@/lib/utils/grid-utils";
import { transformToStructured } from "@/lib/utils/datagrid-transformer";
import { useDataGridValidation } from "./useDataGridValidation";
import { useFormState } from "@/contexts/FormStateContext";

interface UseDataGridProps {
  component: DataGridType;
  value: Record<string, any>;
  onChange: (value: Record<string, any>) => void;
  formSchema: any;
  formValues: any;
  calculatedValues?: Record<string, any>;
}

/**
 * Custom hook for managing data grid state and operations
 */
export function useDataGrid({
  component,
  value,
  onChange,
  formSchema,
  formValues,
  calculatedValues,
}: UseDataGridProps) {
  const { dispatch } = useFormState();
  // Use the validation hook
  const { validateCellValue, validateAllCells } = useDataGridValidation(
    component,
    formSchema,
    formValues
  );

  // Convert custom ID data to Excel coordinate format for internal use
  const convertToExcelFormat = useCallback(
    (customIdData: Record<string, any>): Record<string, any> => {
      const excelData: Record<string, any> = {};

      // First, initialize all cells with empty values
      for (let row = 0; row < component.rows; row++) {
        for (let col = 0; col < component.columns; col++) {
          const cellId = indicesToExcel(row, col);
          excelData[cellId] = "";
        }
      }

      // Then map custom ID data to Excel coordinates
      for (const [customId, value] of Object.entries(customIdData)) {
        // Find the Excel coordinate for this custom ID
        let excelCoord = customId; // Default to assuming it's already an Excel coordinate

        // Search through component cells to find matching custom ID
        for (const [coord, cell] of Object.entries(component.cells)) {
          if (cell.id === customId) {
            excelCoord = coord;
            break;
          }
        }

        excelData[excelCoord] = value;
      }

      return excelData;
    },
    [component.rows, component.columns, component.cells]
  );

  // Initialize grid data with proper format conversion
  const initializeGridData = useCallback(() => {
    if (!value || Object.keys(value).length === 0) {
      // Initialize empty grid with Excel coordinates
      const initialData: Record<string, any> = {};
      for (let row = 0; row < component.rows; row++) {
        for (let col = 0; col < component.columns; col++) {
          const cellId = indicesToExcel(row, col);
          initialData[cellId] = "";
        }
      }
      return initialData;
    }

    // Convert incoming data (which might be in custom ID format) to Excel format
    return convertToExcelFormat(value);
  }, [value, component.rows, component.columns, convertToExcelFormat]);

  // State for grid data and errors (using Excel coordinates internally)
  const [gridData, setGridData] =
    useState<Record<string, any>>(initializeGridData);
  const errors = useRef<Record<string, string>>({});

  // Convert Excel coordinate data to custom ID format
  const convertToCustomIdFormat = useCallback(
    (excelData: Record<string, any>): Record<string, any> => {
      const customIdData: Record<string, any> = {};

      for (const [excelCoord, value] of Object.entries(excelData)) {
        // Get custom ID for this cell, fallback to Excel coordinate
        const customId = component.cells[excelCoord]?.id || excelCoord;
        customIdData[customId] = value;
      }

      return customIdData;
    },
    [component.cells]
  );

  // Transform and notify changes
  const notifyChanges = useCallback(
    (newGridData: Record<string, any>) => {
      // Convert internal Excel format to custom ID format
      const customIdData = convertToCustomIdFormat(newGridData);

      // Transform flat data to structured format (using custom IDs)
      const structuredData = transformToStructured(customIdData, component);

      // Pass both formats to the onChange handler (using custom IDs)
      onChange({
        flat: customIdData,
        structured: structuredData,
      });
    },
    [component, onChange, convertToCustomIdFormat]
  );

  // Handle cell change with memoization
  const handleCellChange = useCallback(
    (rowIndex: number, colIndex: number, value: string) => {
      const cellId = indicesToExcel(rowIndex, colIndex);
      const newGridData = { ...gridData, [cellId]: value };

      // Validate the cell value
      const errorMessage = validateCellValue(rowIndex, colIndex, value);

      // Update errors state - use custom cell ID for error storage to match validation
      const customCellId = component.cells[cellId]?.id || cellId;
      const newErrors = { ...errors.current };

      if (errorMessage) {
        newErrors[customCellId] = errorMessage;
      } else {
        delete newErrors[customCellId];
      }
      errors.current = newErrors;

      // Update grid data
      setGridData(newGridData);

      // Notify changes
      notifyChanges(newGridData);
    },
    [
      gridData,
      validateCellValue,
      notifyChanges,
      component.name,
      dispatch,
      component.cells,
    ]
  );

  // Get cell value with memoization
  const getCellValue = useCallback(
    (rowIndex: number, colIndex: number): string => {
      const cellId = indicesToExcel(rowIndex, colIndex);
      return gridData[cellId] ?? "";
    },
    [gridData]
  );

  // Get cell error with memoization
  const getCellError = useCallback(
    (rowIndex: number, colIndex: number): string | undefined => {
      const cellId = indicesToExcel(rowIndex, colIndex);
      const customCellId = component.cells[cellId]?.id || cellId;
      return errors.current[customCellId];
    },
    [errors.current, component.cells]
  );

  // Run validation on all cells
  const runValidation = useCallback(() => {
    // Convert grid data to custom ID format for validation
    const customIdData = convertToCustomIdFormat(gridData);
    const newErrors = validateAllCells(customIdData);
    errors.current = newErrors;
    return Object.keys(newErrors).length === 0;
  }, [gridData, validateAllCells, convertToCustomIdFormat]);

  // Initialize or update grid data when value changes
  useEffect(() => {
    const newGridData = initializeGridData();

    // Only update if the data has actually changed
    setGridData((prevData) => {
      if (JSON.stringify(prevData) === JSON.stringify(newGridData)) {
        return prevData;
      }
      return newGridData;
    });
  }, [value, initializeGridData]);

  // Validate all cells when the component is mounted or when the component prop changes
  useEffect(() => {
    runValidation();
  }, [component, runValidation]);

  // Effect to synchronize calculated values with the form state
  useEffect(() => {
    if (!calculatedValues || Object.keys(calculatedValues).length === 0) {
      return;
    }

    // Check if any calculated value has actually changed
    const hasChanged = Object.entries(calculatedValues).some(
      ([cellId, calculatedValue]) => {
        const currentValue = gridData[cellId];
        // Using != to handle type differences (e.g., "5" vs 5) which is acceptable here
        return currentValue != calculatedValue;
      }
    );

    if (hasChanged) {
      // Create a new grid data object with the updated calculated values
      const newGridData = {
        ...gridData,
        ...calculatedValues,
      };
      // Update the internal state
      setGridData(newGridData);
      // Notify react-hook-form of the change
      notifyChanges(newGridData);
    }
  }, [calculatedValues, gridData, notifyChanges]);

  // Memoize the return value to prevent unnecessary re-renders
  const returnValue = useMemo(
    () => ({
      gridData,
      errors,
      getCellValue,
      getCellError,
      handleCellChange,
      validateAllCells: runValidation,
    }),
    [
      gridData,
      errors,
      getCellValue,
      getCellError,
      handleCellChange,
      runValidation,
    ]
  );

  return returnValue;
}
