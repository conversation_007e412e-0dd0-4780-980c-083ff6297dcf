import { FormComponent } from "@/lib/schemas/form-schemas";
import { SubmissionSection } from "./SubmissionSection";
import { SubmissionField } from "./SubmissionField";
import { formatFieldName } from "@/lib/utils/submission-data-utils";
import { SubmissionDataGrid } from "./SubmissionDataGrid";
import { transformToStructured } from "@/lib/utils/datagrid-transformer";

interface SubmissionStepProps {
    title: string;
    description?: string;
    components: FormComponent[];
    allComponents: FormComponent[];
    data: Record<string, any>;
}

/**
 * Check if data is in structured format (old format)
 */
export const isStructuredDataGrid = (value: any): boolean => {
    return value && typeof value === "object" && "rows" in value && "metadata" in value;
};

/**
 * Check if data is in flat custom ID format (new format)
 */
export const isCustomIdDataGrid = (value: any, component: FormComponent): boolean => {
    if (!value || typeof value !== "object" || component.type !== "datagrid") {
        return false;
    }

    // If it's already structured, it's not flat custom ID format
    if (isStructuredDataGrid(value)) {
        return false;
    }

    // Check if it looks like flat data (has string keys and values)
    const keys = Object.keys(value);
    if (keys.length === 0) return false;

    // For custom ID format, we expect simple key-value pairs
    return keys.every(key => typeof key === "string" && value[key] !== undefined);
};

/**
 * Convert custom ID format to structured format for display
 */
export const convertCustomIdToStructured = (customIdData: any, component: FormComponent): any => {
    try {
        // Type guard to ensure component is a DataGrid component
        if (component.type !== "datagrid") {
            return null;
        }
        return transformToStructured(customIdData, component as any);
    } catch (error) {
        console.error("Error converting custom ID data to structured format:", error);
        return null;
    }
};

export function SubmissionStep({
    title,
    description,
    components,
    allComponents,
    data,
}: Readonly<SubmissionStepProps>) {
    return (
        <div className="bg-white shadow-sm border rounded-md p-4 mb-6">
            <h2 className="text-xl font-semibold text-primary mb-2">{title}</h2>
            {description && (
                <p className="text-sm text-muted-foreground mb-4">{description}</p>
            )}
            <div className="space-y-6">
                {components.map((component) => {
                    if (component.type === "section") {
                        const sectionChildren = allComponents.filter(
                            (c) => c.parentId === component.id
                        );
                        const sectionFields = Object.keys(data)
                            .filter((key) =>
                                sectionChildren.some((c) => c.name === key)
                            )
                            .map((key) => ({
                                key,
                                value: data[key],
                                component: sectionChildren.find((c) => c.name === key),
                            }));

                        return (
                            <SubmissionSection
                                key={component.id}
                                title={component.label}
                                description={(component as any).description}
                                fields={sectionFields}
                                components={allComponents}
                            />
                        );
                    }

                    const value = data[component.name];
                    if (value === undefined) {
                        return null;
                    }

                    // Handle DataGrid components (both old structured format and new custom ID format)
                    if ((component as any).type === "datagrid") {
                        let dataGridData = null;

                        // Check if it's already in structured format (backward compatibility)
                        if (isStructuredDataGrid(value)) {
                            dataGridData = value;
                        }
                        // Check if it's in custom ID format (new format)
                        else if (isCustomIdDataGrid(value, component)) {
                            dataGridData = convertCustomIdToStructured(value, component);
                        }
                        else {
                            console.log(`DataGrid ${component.name}: Unknown format`, value);
                        }

                        // If we have valid data grid data, render it
                        if (dataGridData) {
                            return (
                                <div
                                    key={component.id}
                                    className="md:col-span-2 bg-muted/20 p-4 rounded-md"
                                >
                                    <SubmissionDataGrid
                                        label={component.label || formatFieldName(component.name)}
                                        data={dataGridData}
                                    />
                                </div>
                            );
                        }

                        // If we can't process the data grid data, show a fallback
                        return (
                            <div
                                key={component.id}
                                className="md:col-span-2 bg-muted/20 p-4 rounded-md"
                            >
                                <div className="text-sm font-medium text-muted-foreground mb-2">
                                    {component.label || formatFieldName(component.name)}
                                </div>
                                <div className="text-sm italic text-muted-foreground">
                                    Unable to display data grid data
                                </div>
                                <details className="mt-2">
                                    <summary className="text-xs text-muted-foreground cursor-pointer">
                                        View raw data
                                    </summary>
                                    <pre className="text-xs text-muted-foreground overflow-auto max-h-40 p-2 bg-muted/20 rounded mt-1">
                                        {JSON.stringify(value, null, 2)}
                                    </pre>
                                </details>
                            </div>
                        );
                    }

                    return (
                        <div
                            key={component.id}
                            className="p-3 rounded-md border border-muted/50 hover:border-primary/30 transition-colors"
                        >
                            <SubmissionField
                                label={component.label ?? component.name}
                                value={value}
                                type={
                                    component.type === "step" ||
                                        (component as any).type === "datagrid"
                                        ? undefined
                                        : (component.type as any)
                                }
                            />
                        </div>
                    );
                })}
            </div>
        </div>
    );
}