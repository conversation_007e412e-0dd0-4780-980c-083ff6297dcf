import { useState, useCallback, useEffect } from "react";
import { FormComponent, FormComponentType } from "@/lib/types/form";
import { DragEndEvent, DragStartEvent } from "@dnd-kit/core";
import {
  createDefaultComponent,
  getAllChildIds,
} from "@/lib/utils/component-utils";

interface UseFormBuilderProps {
  components: FormComponent[];
  onChange: (components: FormComponent[]) => void;
}

export function useFormBuilder({ components, onChange }: UseFormBuilderProps) {
  const [selectedComponentId, setSelectedComponentId] = useState<string | null>(
    null
  );
  const [activeId, setActiveId] = useState<string | null>(null);
  // Initialize expandedContainers with all containers expanded by default
  const [expandedContainers, setExpandedContainers] = useState<
    Record<string, boolean>
  >(() => {
    // Create an object with all container components (step, section) set to expanded
    const initialState: Record<string, boolean> = {};
    components.forEach((component) => {
      if (component.type === "step" || component.type === "section") {
        initialState[component.id] = true;
      }
    });
    return initialState;
  });
  const [sidebarTab, setSidebarTab] = useState<"add" | "edit">("add");

  // Update expandedContainers when components change
  useEffect(() => {
    // Check for any new container components that aren't in expandedContainers
    const newContainers: Record<string, boolean> = {};
    let hasNewContainers = false;

    components.forEach((component) => {
      if (
        (component.type === "step" || component.type === "section") &&
        expandedContainers[component.id] === undefined
      ) {
        newContainers[component.id] = true;
        hasNewContainers = true;
      }
    });

    // Only update state if there are new containers
    if (hasNewContainers) {
      setExpandedContainers((prev) => ({
        ...prev,
        ...newContainers,
      }));
    }
  }, [components, expandedContainers]);

  // Toggle expand/collapse for container components
  const handleToggleExpand = useCallback((id: string) => {
    // Use a functional update that doesn't recreate the entire object
    // This is more efficient for large state objects
    setExpandedContainers((prev) => {
      // Create a new object only with the changed property
      // This is more efficient than spreading the entire previous state
      const newValue = !prev[id];
      return { ...prev, [id]: newValue };
    });
  }, []);

  // Add a new component
  const handleAddComponent = useCallback(
    (type: FormComponentType) => {
      const newComponent: FormComponent = createDefaultComponent(type);
      onChange([...components, newComponent]);
      setSelectedComponentId(newComponent.id);
    },
    [components, onChange]
  );

  // Select a component
  const handleSelectComponent = useCallback(
    (id: string, activateEditTab: boolean = false) => {
      setSelectedComponentId(id);
      if (activateEditTab) {
        setSidebarTab("edit");

        // Ensure the sidebar is visible by scrolling to it
        setTimeout(() => {
          const sidebarElement = document.querySelector(
            ".form-builder-sidebar"
          );
          if (sidebarElement) {
            sidebarElement.scrollIntoView({
              behavior: "smooth",
              block: "start",
            });
          }
        }, 150); // Slightly longer timeout to ensure component scrolling happens first
      }
    },
    []
  );

  // Update a component
  const handleUpdateComponent = useCallback(
    (updatedComponent: FormComponent) => {
      onChange(
        components.map((component) =>
          component.id === updatedComponent.id ? updatedComponent : component
        )
      );
    },
    [components, onChange]
  );

  // Delete a component
  const handleDeleteComponent = useCallback(
    (id: string) => {
      // First, find all child components that need to be deleted
      const childIds = getAllChildIds(id, components);

      // Delete the component and all its children
      onChange(
        components.filter(
          (component) => component.id !== id && !childIds.includes(component.id)
        )
      );

      if (
        selectedComponentId === id ||
        (selectedComponentId && childIds.includes(selectedComponentId))
      ) {
        setSelectedComponentId(null);
      }
    },
    [components, onChange, selectedComponentId]
  );

  // Move a component up or down, handling nested structures
  const handleMoveComponent = useCallback(
    (id: string, direction: "up" | "down") => {
      const componentToMove = components.find((c) => c.id === id);
      if (!componentToMove) {
        return;
      }

      // Find siblings to determine move possibility
      const siblings = components.filter(
        (c) => c.parentId === componentToMove.parentId
      );
      const currentSiblingIndex = siblings.findIndex((c) => c.id === id);

      if (
        (direction === "up" && currentSiblingIndex === 0) ||
        (direction === "down" && currentSiblingIndex === siblings.length - 1)
      ) {
        return; // Cannot move further
      }

      const targetSibling =
        direction === "up"
          ? siblings[currentSiblingIndex - 1]
          : siblings[currentSiblingIndex + 1];

      // If moving a section, move the entire block (section + children)
      if (componentToMove.type === "section") {
        const idsToMove = [id, ...getAllChildIds(id, components)];
        const targetIds = [
          targetSibling.id,
          ...getAllChildIds(targetSibling.id, components),
        ];

        const newComponents = [...components];
        const blockToMove = newComponents.filter((c) =>
          idsToMove.includes(c.id)
        );
        const remainingComponents = newComponents.filter(
          (c) => !idsToMove.includes(c.id)
        );

        if (direction === "up") {
          const targetIndex = remainingComponents.findIndex(
            (c) => c.id === targetSibling.id
          );
          if (targetIndex !== -1) {
            remainingComponents.splice(targetIndex, 0, ...blockToMove);
            onChange(remainingComponents);
          }
        } else {
          // "down"
          const lastIdOfTargetBlock = targetIds[targetIds.length - 1];
          const targetIndex = remainingComponents.findIndex(
            (c) => c.id === lastIdOfTargetBlock
          );
          if (targetIndex !== -1) {
            remainingComponents.splice(targetIndex + 1, 0, ...blockToMove);
            onChange(remainingComponents);
          }
        }
      } else {
        // For steps and normal fields, perform a simple swap of just the component itself.
        // This leaves child components in their original array positions.
        const componentToMoveIndex = components.findIndex((c) => c.id === id);
        const targetSiblingIndex = components.findIndex(
          (c) => c.id === targetSibling.id
        );

        if (componentToMoveIndex !== -1 && targetSiblingIndex !== -1) {
          const newComponents = [...components];
          // Swap the positions of the component to move and its target sibling
          const temp = newComponents[componentToMoveIndex];
          newComponents[componentToMoveIndex] =
            newComponents[targetSiblingIndex];
          newComponents[targetSiblingIndex] = temp;
          onChange(newComponents);
        }
      }
    },
    [components, onChange]
  );

  // Handle drag start
  const handleDragStart = useCallback((event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  }, []);

  // Handle drag end
  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over } = event;

      // Handle sorting existing components
      if (over && active.id !== over.id) {
        // Check if this is a palette item being dropped
        if (typeof active.id === "string" && active.id.startsWith("palette-")) {
          // Extract the component type from the ID
          const type = active.id.replace("palette-", "") as FormComponentType;

          // If dropping onto an existing component
          if (
            over.id &&
            typeof over.id === "string" &&
            !over.id.startsWith("palette-")
          ) {
            const overComponent = components.find(
              (item) => item.id === over.id
            );

            if (overComponent) {
              // If dropping onto a container (step or section), add as a child
              if (
                overComponent.type === "step" ||
                overComponent.type === "section"
              ) {
                // Create a new component of this type with the container as parent
                const newComponent = createDefaultComponent(
                  type,
                  overComponent.id
                );
                onChange([...components, newComponent]);
                setSelectedComponentId(newComponent.id);

                // Ensure the container is expanded
                setExpandedContainers((prev) => ({
                  ...prev,
                  [overComponent.id]: true,
                }));
              } else {
                // If dropping onto a regular component, insert at that position
                const overIndex = components.findIndex(
                  (item) => item.id === over.id
                );
                if (overIndex !== -1) {
                  // Create a new component with the same parent as the target component
                  const newComponent = createDefaultComponent(
                    type,
                    overComponent.parentId
                  );
                  const newComponents = [...components];
                  newComponents.splice(overIndex, 0, newComponent);
                  onChange(newComponents);
                  setSelectedComponentId(newComponent.id);
                }
              }
            }
          } else {
            // Otherwise add to the end (top level)
            const newComponent = createDefaultComponent(type);
            onChange([...components, newComponent]);
            setSelectedComponentId(newComponent.id);
          }
        } else {
          // Regular reordering of existing components
          const activeComponent = components.find(
            (item) => item.id === active.id
          );
          const overComponent = components.find((item) => item.id === over.id);

          if (activeComponent && overComponent) {
            // If dropping onto a container, make it a child of that container
            if (
              overComponent.type === "step" ||
              overComponent.type === "section"
            ) {
              // Update the component's parentId
              const updatedComponent = {
                ...activeComponent,
                parentId: overComponent.id,
              };

              // Remove the original and add the updated one
              const newComponents = components.filter(
                (item) => item.id !== active.id
              );
              newComponents.push(updatedComponent);
              onChange(newComponents);

              // Ensure the container is expanded
              setExpandedContainers((prev) => ({
                ...prev,
                [overComponent.id]: true,
              }));
            } else {
              // Regular reordering
              const oldIndex = components.findIndex(
                (item) => item.id === active.id
              );
              const newIndex = components.findIndex(
                (item) => item.id === over.id
              );

              if (oldIndex !== -1 && newIndex !== -1) {
                // Make sure the component keeps its parent or gets the same parent as the target
                const updatedComponent = {
                  ...activeComponent,
                  parentId: overComponent.parentId,
                };

                // Create a new array without the active component
                const newComponents = components.filter(
                  (item) => item.id !== active.id
                );

                // Insert the updated component at the new position
                newComponents.splice(newIndex, 0, updatedComponent);
                onChange(newComponents);
              }
            }
          }
        }
      } else if (
        over &&
        over.id === "form-drop-area" &&
        typeof active.id === "string" &&
        active.id.startsWith("palette-")
      ) {
        // Dropping onto the empty form area
        const type = active.id.replace("palette-", "") as FormComponentType;
        const newComponent = createDefaultComponent(type);
        onChange([...components, newComponent]);
        setSelectedComponentId(newComponent.id);
      }

      setActiveId(null);
    },
    [components, onChange]
  );

  // Get the selected component
  const selectedComponent = components.find(
    (component) => component.id === selectedComponentId
  );

  return {
    selectedComponentId,
    selectedComponent,
    activeId,
    expandedContainers,
    sidebarTab,
    handleToggleExpand,
    handleAddComponent,
    handleSelectComponent,
    handleUpdateComponent,
    handleDeleteComponent,
    handleMoveComponent,
    handleDragStart,
    handleDragEnd,
    setSelectedComponentId,
    setSidebarTab,
  };
}
