# Conventional Commits Guide

This document provides guidelines for using Conventional Commits in our project.

## What are Conventional Commits?

[Conventional Commits](https://www.conventionalcommits.org/) is a specification for adding human and machine-readable meaning to commit messages. It provides a set of rules for creating an explicit commit history, which makes it easier to write automated tools on top of it, such as automatic changelog generation.

## Commit Message Format

Each commit message consists of a **header**, a **body**, and a **footer**. The header has a special format that includes a **type**, an optional **scope**, and a **subject**:

```
<type>(<scope>): <subject>
<BLANK LINE>
<body>
<BLANK LINE>
<footer>
```

### Header

The header is mandatory and must conform to the following format:

- **type**: This represents the kind of change you're making. Must be one of the following:
  - `feat`: A new feature
  - `fix`: A bug fix
  - `docs`: Documentation only changes
  - `style`: Changes that do not affect the meaning of the code (white-space, formatting, etc.)
  - `refactor`: A code change that neither fixes a bug nor adds a feature
  - `perf`: A code change that improves performance
  - `test`: Adding missing tests or correcting existing tests
  - `build`: Changes that affect the build system or external dependencies
  - `ci`: Changes to our CI configuration files and scripts
  - `chore`: Other changes that don't modify src or test files
  - `revert`: Reverts a previous commit

- **scope** (optional): A term describing the section of the codebase affected (e.g., `auth`, `form-builder`, `data-grid`).

- **subject**: A short description of the change:
  - Use the imperative, present tense: "change" not "changed" nor "changes"
  - Don't capitalize the first letter
  - No period (.) at the end

### Body

The body is optional and should include the motivation for the change and contrast this with previous behavior.

### Footer

The footer is optional and should contain any information about **Breaking Changes** and is also the place to reference GitHub issues that this commit **Closes**.

Breaking changes should start with the word `BREAKING CHANGE:` with a space or two newlines. The rest of the commit message is then used for this.

## Examples

### Simple Feature

```
feat: add email validation to login form
```

### Feature with Scope

```
feat(auth): implement JWT authentication
```

### Bug Fix with Issue Reference

```
fix(data-grid): resolve issue with column resizing

Closes #123
```

### Documentation Change

```
docs: update README with new installation instructions
```

### Breaking Change

```
feat(api): change authentication endpoint response format

BREAKING CHANGE: The response format of the authentication endpoint has changed.
The token is now returned in the 'accessToken' field instead of 'token'.
```

### Multiple Types of Changes

If your commit includes multiple types of changes, prioritize them in this order:
1. Breaking changes
2. Features
3. Bug fixes
4. Everything else

## Automatic Changelog Generation

By following the Conventional Commits specification, we can automatically generate changelogs that categorize changes based on their type:

- `feat` commits are listed under "Features"
- `fix` commits are listed under "Bug Fixes"
- `perf` commits are listed under "Performance Improvements"
- Commits with `BREAKING CHANGE` in the footer are listed under "Breaking Changes"

## Tools

We use the following tools to help with Conventional Commits:

- **commitlint**: Validates commit messages against the Conventional Commits specification
- **husky**: Sets up Git hooks to validate commit messages before they're committed
- **conventional-changelog-cli**: Generates changelogs from Conventional Commits

## Best Practices

1. **Keep commits focused**: Each commit should represent a single logical change.
2. **Be descriptive**: The subject line should give a clear idea of what the commit does.
3. **Use the body for context**: Explain why the change was made, not how (the code shows that).
4. **Reference issues**: If the commit addresses an issue, reference it in the footer.
5. **Breaking changes**: Always mark breaking changes clearly in the footer.
6. **Consider future readers**: Write commit messages for people who will read them in the future, possibly without the context you have now.

## Common Mistakes to Avoid

1. **Vague messages**: "Fix bug" or "Update code" don't provide useful information.
2. **Mixing unrelated changes**: Don't include multiple unrelated changes in a single commit.
3. **Forgetting breaking changes**: Always mark breaking changes explicitly.
4. **Inconsistent formatting**: Follow the format consistently to ensure tools can parse your commits.

## Further Reading

- [Conventional Commits Specification](https://www.conventionalcommits.org/)
- [Angular Commit Message Guidelines](https://github.com/angular/angular/blob/master/CONTRIBUTING.md#commit)
- [Semantic Versioning](https://semver.org/)
