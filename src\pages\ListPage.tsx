import { useMemo } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { DataTable } from "@/components/ui/data-table/data-table";
import { useTable } from "@/hooks/useTable";
import { Person, TableService } from "@/lib/services/table-service";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Eye, Edit, Trash2 } from "lucide-react";

// StatusBadge component
interface StatusBadgeProps {
  status: string;
}

const StatusBadge = ({ status }: StatusBadgeProps) => {
  // Extract nested ternary into an independent statement
  const getBadgeVariant = (status: string) => {
    if (status === "active") return "default";
    if (status === "inactive") return "destructive";
    return "outline";
  };

  return <Badge variant={getBadgeVariant(status)}>{status}</Badge>;
};

// ProgressBar component
interface ProgressBarProps {
  progress: number;
}

const ProgressBar = ({ progress }: ProgressBarProps) => {
  return (
    <div className="w-full bg-muted rounded-full h-2.5">
      <div
        className="bg-primary h-2.5 rounded-full"
        style={{ width: `${progress}%` }}
      ></div>
    </div>
  );
};

// DateFormatter component
interface DateFormatterProps {
  dateString: string;
}

const DateFormatter = ({ dateString }: DateFormatterProps) => {
  const date = new Date(dateString);
  return <span>{date.toLocaleDateString()}</span>;
};

// ActionButtons component
const ActionButtons = () => {
  return (
    <div className="flex items-center gap-2">
      <Button variant="ghost" size="icon" title="View">
        <Eye className="h-4 w-4" />
      </Button>
      <Button variant="ghost" size="icon" title="Edit">
        <Edit className="h-4 w-4" />
      </Button>
      <Button variant="ghost" size="icon" title="Delete">
        <Trash2 className="h-4 w-4" />
      </Button>
    </div>
  );
};

export default function ListPage() {
  // Define columns
  const columns = useMemo<ColumnDef<Person, any>[]>(
    () => [
      {
        accessorKey: "id",
        header: "ID",
        enableSorting: true,
        enableColumnFilter: true,
        meta: {
          enablePinning: true,
        },
      },
      {
        accessorKey: "firstName",
        header: "First Name",
        enableSorting: true,
        enableColumnFilter: true,
        meta: {
          enablePinning: false,
        },
      },
      {
        accessorKey: "lastName",
        header: "Last Name",
        enableSorting: true,
        enableColumnFilter: true,
        meta: {
          enablePinning: false,
        },
      },
      {
        accessorKey: "email",
        header: "Email",
        enableSorting: true,
        enableColumnFilter: true,
        meta: {
          enablePinning: false,
        },
      },
      {
        accessorKey: "age",
        header: "Age",
        enableSorting: true,
        enableColumnFilter: true,
        meta: {
          enablePinning: false,
        },
      },
      {
        accessorKey: "status",
        header: "Status",
        enableSorting: true,
        enableColumnFilter: true,
        cell: ({ row }) => {
          // The type assertion is needed because getValue returns unknown
          const status = String(row.getValue("status"));
          return <StatusBadge status={status} />;
        },
        meta: {
          enablePinning: false,
        },
      },
      {
        accessorKey: "role",
        header: "Role",
        enableSorting: true,
        enableColumnFilter: true,
        meta: {
          enablePinning: false,
        },
      },
      {
        accessorKey: "visits",
        header: "Visits",
        enableSorting: true,
        enableColumnFilter: true,
        meta: {
          enablePinning: false,
        },
      },
      {
        accessorKey: "progress",
        header: "Progress",
        enableSorting: true,
        enableColumnFilter: true,
        cell: ({ row }) => {
          // Convert to number to ensure proper type
          const progress = Number(row.getValue("progress"));
          return <ProgressBar progress={progress} />;
        },
        meta: {
          enablePinning: false,
        },
      },
      {
        accessorKey: "createdAt",
        header: "Created At",
        enableSorting: true,
        enableColumnFilter: true,
        cell: ({ row }) => {
          const dateString = String(row.getValue("createdAt"));
          return <DateFormatter dateString={dateString} />;
        },
        meta: {
          enablePinning: false,
        },
      },
      {
        id: "actions",
        header: "Actions",
        enableSorting: false,
        enableColumnFilter: false,
        cell: () => <ActionButtons />,
      },
    ],
    []
  );

  // Use the table hook
  const {
    data,
    pageCount,
    totalCount,
    isLoading,
    pagination,
    sorting,
    handlePaginationChange,
    handleSortingChange,
    handleFilterChange,
  } = useTable<Person>({
    fetchData: TableService.getPersons,
    initialPageSize: 10,
  });

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-3xl font-bold tracking-tight">People List</h1>
        <Button>Add Person</Button>
      </div>

      <DataTable
        columns={columns}
        data={data}
        pageCount={pageCount}
        totalCount={totalCount}
        pagination={pagination}
        sorting={sorting}
        onPaginationChange={handlePaginationChange}
        onSortingChange={handleSortingChange}
        onFilterChange={handleFilterChange}
        isLoading={isLoading}
        enableColumnFilters={true}
        enableGlobalFilter={true}
        enablePinning={true}
        showPinningControls={false}
        defaultPinnedColumns={{
          id: 1,
        }}
      />
    </div>
  );
}
