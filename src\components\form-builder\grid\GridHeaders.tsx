import { memo } from "react";
import { TableHead } from "@/components/ui/table";
import {
  indicesToExcel,
  getCellDisplayValue,
  isCellHeader
} from "@/lib/utils/grid-utils";

interface GridHeadersProps {
  columns: number;
  cells: Record<string, any>;
}

function GridHeaders({ columns, cells }: GridHeadersProps) {
  return (
    <>
      <TableHead className="w-12"></TableHead>
      {renderColumnHeaders(columns, cells)}
    </>
  );
}

// Generate column headers
const renderColumnHeaders = (columns: number, cells: Record<string, any>) => {
  return Array.from({ length: columns }, (_, colIndex) => {
    // Skip the first column (it's for row headers)
    if (colIndex === 0) return null;

    const headerText =
      getCellDisplayValue(cells, 0, colIndex) || String.fromCharCode(65 + colIndex);
    const isHeader = isCellHeader(cells, 0, colIndex);
    const cellCoord = indicesToExcel(0, colIndex);

    return (
      <TableHead
        key={`col-${cellCoord}`}
        // className={isHeader ? "font-bold bg-muted/50" : ""}
        className={isHeader ? `font-bold bg-muted/50 px-2 text-left align-middle h-10 text-foreground whitespace-normal break-words w-[200px] max-w-[200px]` : ''}
        title={`Column ${cellCoord.charAt(0)}`}
      >
        {headerText}
      </TableHead>
    );
  }).filter(Boolean);
};

export default memo(GridHeaders);
