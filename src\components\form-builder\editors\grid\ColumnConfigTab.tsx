import { memo, useState } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ValidationEditor } from "../ValidationEditor";
import { OptionsEditor } from "../OptionsEditor";
import { DataGridCellInputType } from "@/lib/types/form";
import { Wand2 } from "lucide-react";

interface ColumnConfigTabProps {
  columnHeaders: string[];
  gridConfig: any; // Using any for brevity, but should be properly typed
  component: any; // Using any for brevity, but should be properly typed
}

/**
 * Tab for configuring column-level settings
 */
const ColumnConfigTab = memo(function ColumnConfigTab({
  columnHeaders,
  gridConfig,
  component,
}: ColumnConfigTabProps) {
  const [selectedColumn, setSelectedColumn] = useState<number | null>(null);

  // Get column letter from index
  const getColumnLetter = (colIndex: number): string => {
    return String.fromCharCode(65 + colIndex);
  };

  // Get column header text
  const getColumnHeaderText = (colIndex: number): string => {
    const headerCellId = `${getColumnLetter(colIndex)}1`;
    const headerCell = component.cells[headerCellId];
    return headerCell?.value ?? getColumnLetter(colIndex);
  };

  // Get button variant based on selection and config state
  const getButtonVariant = (
    isSelected: boolean,
    hasConfig: boolean
  ): "default" | "outline" | "ghost" => {
    if (isSelected) return "default";
    if (hasConfig) return "outline";
    return "ghost";
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 gap-4">
        <div className="border rounded-md p-4">
          <h4 className="text-sm font-medium mb-4">Column Configurations</h4>
          <p className="text-sm text-muted-foreground mb-4">
            Configure default settings for all cells in a column. These settings
            will be applied to all data cells in the column unless overridden.
          </p>

          <div className="grid grid-cols-5 gap-2 mb-4">
            {columnHeaders.map((_, colIndex) => {
              // Skip the first column (row headers)
              if (colIndex === 0) return null;

              const isSelected = selectedColumn === colIndex;
              const hasConfig = !!gridConfig.getColumnConfig(colIndex);

              return (
                <Button
                  key={`col-config-${getColumnLetter(colIndex)}`}
                  variant={getButtonVariant(isSelected, hasConfig)}
                  className={`h-10 ${hasConfig ? "border-primary/50" : ""}`}
                  onClick={() => setSelectedColumn(colIndex)}
                >
                  {getColumnHeaderText(colIndex)}
                  {hasConfig && <span className="ml-1 text-xs">✓</span>}
                </Button>
              );
            })}
          </div>

          {selectedColumn !== null && (
            <div className="space-y-4 border-t pt-4">
              <h4 className="text-sm font-medium">
                Column {getColumnLetter(selectedColumn)} Configuration
              </h4>

              <div className="flex items-center justify-between">
                <p className="text-sm text-muted-foreground">
                  {gridConfig.getColumnConfig(selectedColumn)
                    ? "This column has a configuration that applies to all cells."
                    : "No configuration set for this column yet."}
                </p>

                {!gridConfig.getColumnConfig(selectedColumn) && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      // Create a column config based on the first data cell in the column
                      gridConfig.createColumnConfig(1, selectedColumn);
                    }}
                  >
                    <Wand2 className="mr-2 h-4 w-4" />
                    Create Column Config
                  </Button>
                )}
              </div>

              {gridConfig.getColumnConfig(selectedColumn) && (
                <div className="space-y-4">
                  <div className="grid gap-2">
                    <Label htmlFor="columnInputType">Input Type</Label>
                    <Select
                      value={
                        gridConfig.getColumnConfig(selectedColumn)?.inputType ??
                        "text"
                      }
                      onValueChange={(value: DataGridCellInputType) => {
                        gridConfig.updateColumnConfig(selectedColumn, {
                          inputType: value,
                        });
                      }}
                    >
                      <SelectTrigger id="columnInputType">
                        <SelectValue placeholder="Select input type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="text">Text</SelectItem>
                        <SelectItem value="number">Number</SelectItem>
                        <SelectItem value="select">Select</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {gridConfig.getColumnConfig(selectedColumn)?.inputType ===
                    "number" && (
                    <div className="grid grid-cols-3 gap-4">
                      <div className="grid gap-2">
                        <Label htmlFor="columnMin">Min Value</Label>
                        <Input
                          id="columnMin"
                          type="number"
                          value={
                            gridConfig.getColumnConfig(selectedColumn)?.min ??
                            ""
                          }
                          onChange={(e) => {
                            const value =
                              e.target.value === ""
                                ? undefined
                                : Number(e.target.value);
                            gridConfig.updateColumnConfig(selectedColumn, {
                              min: value,
                            });
                          }}
                        />
                      </div>
                      <div className="grid gap-2">
                        <Label htmlFor="columnMax">Max Value</Label>
                        <Input
                          id="columnMax"
                          type="number"
                          value={
                            gridConfig.getColumnConfig(selectedColumn)?.max ??
                            ""
                          }
                          onChange={(e) => {
                            const value =
                              e.target.value === ""
                                ? undefined
                                : Number(e.target.value);
                            gridConfig.updateColumnConfig(selectedColumn, {
                              max: value,
                            });
                          }}
                        />
                      </div>
                      <div className="grid gap-2">
                        <Label htmlFor="columnStep">Step</Label>
                        <Input
                          id="columnStep"
                          type="number"
                          value={
                            gridConfig.getColumnConfig(selectedColumn)?.step ??
                            ""
                          }
                          onChange={(e) => {
                            const value =
                              e.target.value === ""
                                ? undefined
                                : Number(e.target.value);
                            gridConfig.updateColumnConfig(selectedColumn, {
                              step: value,
                            });
                          }}
                        />
                      </div>
                    </div>
                  )}

                  {gridConfig.getColumnConfig(selectedColumn)?.inputType ===
                    "select" && (
                    <div className="space-y-4">
                      <Label>Options</Label>
                      <OptionsEditor
                        options={
                          gridConfig.getColumnConfig(selectedColumn)?.options ??
                          []
                        }
                        onChange={(options) => {
                          gridConfig.updateColumnConfig(selectedColumn, {
                            options,
                          });
                        }}
                      />
                    </div>
                  )}

                  <div className="grid gap-2">
                    <Label htmlFor="columnUnit">Unit</Label>
                    <Input
                      id="columnUnit"
                      value={
                        gridConfig.getColumnConfig(selectedColumn)?.unit ?? ""
                      }
                      onChange={(e) => {
                        gridConfig.updateColumnConfig(selectedColumn, {
                          unit: e.target.value,
                        });
                      }}
                      placeholder="e.g., kg, m, °C"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Validations</Label>
                    <ValidationEditor
                      component={{
                        ...component,
                        type: "text",
                        validations:
                          gridConfig.getColumnConfig(selectedColumn)
                            ?.validations ?? [],
                      }}
                      onChange={(validations) => {
                        gridConfig.updateColumnConfig(selectedColumn, {
                          validations,
                        });
                      }}
                    />
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
});

export default ColumnConfigTab;
