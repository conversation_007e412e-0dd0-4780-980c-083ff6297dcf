import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { FormPageConfig } from "@/lib/types/page-config";
import { EntityService } from "@/lib/services/entity-service";
import DynamicPageHeader from "./DynamicPageHeader";
import { Loading } from "@/components/ui/loading";
import { ArrowLeft, Edit } from "lucide-react";

interface DynamicDetailsPageProps {
  config: FormPageConfig;
  entityId: string;
  onEdit?: (id: string) => void;
  backButtonUrl?: string;
}

/**
 * Component for rendering a dynamic details page in read-only mode
 */
export function DynamicDetailsPage({
  config,
  entityId,
  onEdit,
  backButtonUrl,
}: Readonly<DynamicDetailsPageProps>) {
  const navigate = useNavigate();
  const [entityData, setEntityData] = useState<Record<string, any> | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(true);

  // Load entity data
  useEffect(() => {
    const loadEntityData = async () => {
      if (entityId) {
        try {
          setIsLoading(true);
          const data = await EntityService.getEntityById(
            config.entityName,
            config.endpoints.get,
            entityId
          );
          if (data) {
            setEntityData(data);
          } else {
            // Handle case where entity is not found
            console.error(`Entity ${entityId} not found`);
          }
        } catch (error) {
          console.error("Error loading entity data:", error);
        } finally {
          setIsLoading(false);
        }
      }
    };

    loadEntityData();
  }, [config.entityName, entityId]);

  // Handle edit button click
  const handleEditClick = () => {
    if (onEdit) {
      onEdit(entityId);
    } else {
      // Navigate to edit page if no onEdit handler is provided
      navigate(`/${config.id}/edit/${entityId}`);
    }
  };

  if (isLoading) {
    return <Loading />;
  }

  if (!entityData) {
    return (
      <div className="flex flex-col items-center justify-center p-8">
        <h2 className="text-2xl font-bold mb-4">Entity Not Found</h2>
        <p className="text-muted-foreground mb-6">
          The requested {config.entityName} could not be found.
        </p>
        <Button
          onClick={() =>
            backButtonUrl ? navigate(backButtonUrl) : navigate(-1)
          }
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Go Back
        </Button>
      </div>
    );
  }
  return (
    <div className="space-y-6">
      <DynamicPageHeader
        config={config}
        showBackButton={true}
        backButtonUrl={backButtonUrl}
        backButtonText="Back"
        actionButtons={entityData.permissions.edit === "ENABLED" && (
          <Button onClick={handleEditClick}>
            <Edit className="h-4 w-4 mr-2" />
            Edit {config.entityName}
          </Button>
        )}
      />

      <Card>
        <CardContent className="pt-6">
          <div className="space-y-6">
            {/* Group fields by category for better organization */}
            <div className="grid gap-6 md:grid-cols-2">
              {/* Render entity data in a structured format */}
              {Object.entries(entityData).map(([key, value]) => {
                // Skip internal fields like id, createdAt, updatedAt
                if (["id", "createdAt", "updatedAt", "permissions"].includes(key)) {
                  return null;
                }

                // Special handling for data grid structures or complex objects
                if (
                  value &&
                  typeof value === "object" &&
                  !Array.isArray(value) &&
                  Object.keys(value).length > 3
                ) {
                  return (
                    <div
                      key={key}
                      className="md:col-span-2 bg-muted/20 p-4 rounded-md border"
                    >
                      <h3 className="font-medium text-primary mb-2">
                        {formatFieldName(key)}
                      </h3>
                      <div className="text-sm">
                        <pre className="bg-muted p-2 rounded text-xs overflow-x-auto">
                          {JSON.stringify(value, null, 2)}
                        </pre>
                      </div>
                    </div>
                  );
                }

                // Regular fields
                return (
                  <div
                    key={key}
                    className="p-3 rounded-md border border-muted/50 hover:border-primary/30 transition-colors"
                  >
                    <div className="font-medium text-sm text-muted-foreground mb-1">
                      {formatFieldName(key)}
                    </div>
                    <div className="text-base">{renderFieldValue(value)}</div>
                  </div>
                );
              })}
            </div>
          </div>
        </CardContent>

        <CardFooter className="border-t px-6 py-4 bg-muted/50 flex justify-between">
          <p className="text-sm text-muted-foreground">
            {config.entityName} ID: {entityId}
          </p>

        </CardFooter>
      </Card>
    </div>
  );
}

/**
 * Format a field name for display
 */
function formatFieldName(key: string): string {
  // Convert camelCase to Title Case with spaces
  return key
    .replace(/([A-Z])/g, " $1")
    .replace(/^./, (str) => str.toUpperCase());
}

/**
 * Render a field value based on its type
 */
function renderFieldValue(value: any): React.ReactNode {
  if (value === null || value === undefined) {
    return <span className="text-muted-foreground italic">Not provided</span>;
  }

  if (typeof value === "boolean") {
    return value ? "Yes" : "No";
  }

  if (typeof value === "object") {
    if (Array.isArray(value)) {
      if (value.length === 0) {
        return <span className="text-muted-foreground italic">No items</span>;
      }

      return (
        <ul className="list-disc pl-5">
          {value.map((item, index) => (
            <li key={index} className="mb-1">
              {renderFieldValue(item)}
            </li>
          ))}
        </ul>
      );
    }

    // Check if it's a date
    if (value instanceof Date) {
      return value.toLocaleDateString();
    }

    // Check if it's a date string (ISO format)
    if (
      typeof value === "string" &&
      /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(value)
    ) {
      try {
        const date = new Date(value);
        return date.toLocaleString();
      } catch (e) {
        // If parsing fails, fall back to the original string
        return value;
      }
    }

    // For small objects (3 or fewer properties), display inline
    if (Object.keys(value).length <= 3) {
      return (
        <div className="text-sm">
          {Object.entries(value).map(([k, v]) => (
            <div key={k} className="mb-1">
              <span className="font-medium">{formatFieldName(k)}:</span>{" "}
              <span>
                {typeof v === "object" ? JSON.stringify(v) : String(v)}
              </span>
            </div>
          ))}
        </div>
      );
    }

    // For other objects, render as JSON
    return (
      <pre className="bg-muted p-2 rounded text-sm overflow-x-auto">
        {JSON.stringify(value, null, 2)}
      </pre>
    );
  }

  // For numbers, format with commas for thousands
  if (typeof value === "number") {
    return value.toLocaleString();
  }

  // For simple values
  return String(value);
}

export default DynamicDetailsPage;
