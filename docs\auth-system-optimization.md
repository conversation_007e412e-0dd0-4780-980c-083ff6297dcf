# Auth System Optimization Guide

This document describes the optimizations made to the authentication system, including React performance optimizations, session expiry due to inactivity, and improved token refresh behavior.

## Overview of Changes

### 1. React Performance Optimizations

#### AuthContext Optimizations

- **Memoized callbacks**: All auth functions (`login`, `logout`, `hasPermission`, `handleCallback`) are wrapped with `useCallback` to prevent unnecessary re-renders
- **Memoized context value**: The context value is memoized with `useMemo` to prevent child components from re-rendering when the context provider re-renders
- **Optimized event listeners**: Activity event listeners use passive event listeners for better performance

#### Component Optimizations

- **ProtectedRoute**: Wrapped with `React.memo` to prevent unnecessary re-renders when props haven't changed
- **RoleBasedRedirect**: Wrapped with `React.memo` and uses `useCallback` for navigation logic

### 2. Session Expiry Due to Inactivity

#### Features

- **Configurable timeout**: Session timeout is configurable via environment variables
- **Activity tracking**: Tracks user activity (mouse movements, clicks, keyboard input, scrolling, touch events)
- **Persistent tracking**: Continues tracking inactivity even when the browser is closed or tab is inactive
- **Cross-session validation**: Checks session expiry when the user returns to the application
- **Warning dialog**: Shows a warning dialog before the session expires, allowing users to extend their session
- **Automatic logout**: Automatically logs out users when the inactivity timeout is reached

#### Configuration

Set these environment variables to configure session timeouts:

```bash
# Session will expire after 30 minutes of inactivity (default)
VITE_SESSION_INACTIVITY_TIMEOUT_MINUTES=30

# Warning dialog appears 5 minutes before expiry (default)
VITE_SESSION_WARNING_TIME_MINUTES=5
```

#### How It Works

1. When a user logs in, an inactivity timer is started and session start time is stored
2. User activity resets the timer and updates the last activity timestamp in localStorage
3. When the warning time is reached, a dialog appears asking if the user wants to extend the session
4. If no action is taken, the user is automatically logged out
5. The timer is cleared when the user logs out or the session expires
6. **Persistent tracking**: When the user returns to the application (browser restart, tab focus, etc.), the system checks if the session expired while away
7. **Cross-session validation**: If the session expired while the browser was closed, the user is automatically logged out

#### Implementation Details

- Uses `useRef` to track the last activity timestamp in memory for active sessions
- Stores last activity timestamp in `localStorage` for persistence across browser sessions
- Sets up event listeners for various user activities (mouse, keyboard, scroll, touch)
- Implements two timers: warning timer and logout timer
- Clears timers when user is active or logs out
- Uses passive event listeners for better performance
- Monitors window focus and visibility change events to detect when user returns
- Validates session expiry on application startup and during tab/window focus
- Automatically cleans up persistent data on logout or session expiry

### 3. Improved Token Refresh Behavior

#### Features

- **Proactive refresh**: Tokens are refreshed proactively when they're about to expire (within 10 minutes)
- **Retry logic**: Failed token refresh attempts are retried up to 3 times with exponential backoff
- **Better error handling**: Distinguishes between different types of errors (invalid tokens vs network errors)
- **Concurrent refresh protection**: Prevents multiple simultaneous refresh attempts
- **Token validation**: Validates token responses before storing them

#### Improvements

- **Automatic refresh interval**: Checks token expiry every 5 minutes and refreshes if needed
- **Buffer time**: Tokens are considered expired 30 seconds before their actual expiry time
- **Graceful degradation**: If refresh fails due to invalid tokens, the user is logged out gracefully

## Environment Variables

### Required Variables (existing)

```bash
VITE_COGNITO_HOSTED_UI_URL=https://your-cognito-domain.auth.region.amazoncognito.com
VITE_COGNITO_CLIENT_ID=your-client-id
VITE_COGNITO_USER_POOL_ID=your-user-pool-id
VITE_COGNITO_REGION=your-aws-region
VITE_COGNITO_REDIRECT_URI=http://localhost:3000/callback
```

### New Optional Variables

```bash
# Session inactivity timeout in minutes (default: 30)
VITE_SESSION_INACTIVITY_TIMEOUT_MINUTES=30

# Warning time before logout in minutes (default: 5)
VITE_SESSION_WARNING_TIME_MINUTES=5
```

## Usage Examples

### Basic Usage

The optimized auth system works the same way as before:

```tsx
import { useAuth } from "@/contexts/AuthContext";

function MyComponent() {
  const { user, isAuthenticated, login, logout } = useAuth();

  if (!isAuthenticated) {
    return <button onClick={login}>Login</button>;
  }

  return (
    <div>
      <p>Welcome, {user?.name}!</p>
      <button onClick={logout}>Logout</button>
    </div>
  );
}
```

### Checking Permissions

```tsx
import { useAuth } from "@/contexts/AuthContext";

function AdminPanel() {
  const { hasPermission } = useAuth();

  if (!hasPermission("admin")) {
    return <div>Access denied</div>;
  }

  return <div>Admin content</div>;
}
```

## Docker Configuration

The new environment variables are automatically included in all Docker configurations:

### Development

```bash
docker-compose up --build app
```

### Production

```bash
docker-compose --profile production up --build app-prod
```

## Security Considerations

1. **Token Storage**: Tokens are still stored in localStorage. Consider using httpOnly cookies for enhanced security in production
2. **Activity Tracking**: Activity is tracked on the client side. Consider server-side session validation for critical applications
3. **Inactivity Timeout**: The default 30-minute timeout balances security and user experience. Adjust based on your security requirements
4. **Token Refresh**: Failed refresh attempts are logged. Monitor these logs for potential security issues

## Performance Impact

The optimizations provide several performance benefits:

1. **Reduced Re-renders**: Memoized callbacks and context values prevent unnecessary component re-renders
2. **Efficient Event Handling**: Passive event listeners improve scroll and touch performance
3. **Smart Token Refresh**: Proactive token refresh prevents authentication interruptions
4. **Optimized Components**: Memoized auth components reduce rendering overhead

## Troubleshooting

### Common Issues

1. **Session expires too quickly**: Check the `VITE_SESSION_INACTIVITY_TIMEOUT_MINUTES` environment variable
2. **Warning dialog doesn't appear**: Ensure `VITE_SESSION_WARNING_TIME_MINUTES` is less than the inactivity timeout
3. **Token refresh fails**: Check network connectivity and Cognito configuration
4. **Activity not detected**: Ensure the user is interacting with the page (mouse, keyboard, touch events)

### Debug Logging

The auth system includes console logging for debugging:

- Token refresh attempts and results
- Session expiry warnings
- Activity timer resets
- Authentication state changes

Enable browser developer tools to see these logs during development.
