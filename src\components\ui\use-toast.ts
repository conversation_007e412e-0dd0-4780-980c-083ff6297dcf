// Adapted from shadcn/ui (https://ui.shadcn.com/docs/components/toast)
import { useContext } from "react";

import type { ToastActionElement, ToastProps } from "@/components/ui/toast";

import { ToastContext, type ToasterToast } from "@/components/ui/toaster";

const TOAST_LIMIT = 5;
// Unused but kept for reference
// const TOAST_REMOVE_DELAY = 1000000

type ToasterToastOptions = Omit<
  ToasterToast,
  "id" | "title" | "description" | "action" | "open"
>;

let count = 0;

function genId() {
  count = (count + 1) % Number.MAX_SAFE_INTEGER;
  return count.toString();
}

type Toast = Omit<ToasterToast, "id">;

function useToast() {
  const { toasts, setToasts } = useContext(ToastContext);

  function dismiss(toastId?: string) {
    setToasts((toasts) =>
      toasts.map((toast) =>
        toast.id === toastId || toastId === undefined
          ? {
              ...toast,
              open: false,
            }
          : toast
      )
    );
  }

  function toast({
    ...props
  }: ToastProps & {
    title?: React.ReactNode;
    description?: React.ReactNode;
    action?: ToastActionElement;
  }) {
    const id = genId();

    const update = (props: ToasterToastOptions) =>
      setToasts((toasts) =>
        toasts.map((t) => (t.id === id ? { ...t, ...props } : t))
      );
    const dismiss = () =>
      setToasts((toasts) =>
        toasts.map((t) => (t.id === id ? { ...t, open: false } : t))
      );

    setToasts(
      (toasts) =>
        [
          {
            id,
            open: true,
            ...props,
            onOpenChange: (open) => {
              if (!open) dismiss();
            },
            variant: props.variant as "default" | "destructive" | undefined,
          },
          ...toasts.slice(0, TOAST_LIMIT - 1),
        ] as ToasterToast[]
    );

    return {
      id,
      dismiss,
      update,
    };
  }

  return {
    toast,
    dismiss,
    toasts,
  };
}

export { useToast, type Toast };
