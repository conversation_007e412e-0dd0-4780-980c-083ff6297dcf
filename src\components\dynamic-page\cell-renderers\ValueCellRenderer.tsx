import React from "react";
import { Row } from "@tanstack/react-table";
import { ValueBadge } from "@/components/ui/formatters/value-badge";
import { ColumnConfig } from "@/lib/types/page-config";

interface ValueCellRendererProps {
  row: Row<unknown>;
  columnId: string;
  configColumn: ColumnConfig;
  type: "number" | "currency" | "percent";
  showTrend?: boolean;
}

export const ValueCellRenderer: React.FC<ValueCellRendererProps> = ({
  row,
  columnId,
  configColumn,
  type,
  showTrend = false,
}) => {
  const value = row.getValue(columnId);
  return value !== undefined ? (
    <ValueBadge
      value={Number(value)}
      type={type}
      showIcon={configColumn.formatOptions?.showIcon ?? (type !== "number")}
      showTrend={showTrend}
      size={configColumn.formatOptions?.size ?? "md"}
    />
  ) : null;
};
