import React from "react";
import {Row} from "@tanstack/react-table";
import {StatusBadge} from "@/components/ui/formatters/status-badge";
import {ColumnConfig} from "@/lib/types/page-config";
import {formatEnumLabel} from "@/lib/utils/format-enum-label.ts";

interface StatusCellRendererProps {
  row: Row<unknown>;
  columnId: string;
  configColumn: ColumnConfig;
}

export const StatusCellRenderer: React.FC<StatusCellRendererProps> = ({
  row,
  columnId,
  configColumn,
}) => {
  const value = row.getValue(columnId);
  return value ? (
    <StatusBadge
      status={formatEnumLabel(String(value))}
      showIcon={configColumn.formatOptions?.showIcon ?? true}
      size={configColumn.formatOptions?.size ?? "md"}
    />
  ) : null;
};
