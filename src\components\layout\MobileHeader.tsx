import React from "react";
import { Menu } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { ThemeToggle } from "@/components/theme-toggle";
import { UserProfileDropdown } from "./UserProfileDropdown";
import { LogoSection } from "./LogoSection";
import { User } from "@/lib/types/auth";

interface MobileHeaderProps {
  isAuthenticated: boolean;
  user: User | null;
  toggleMobileMenu: () => void;
  logout: () => void;
}

/**
 * Component for rendering the mobile header
 */
export const MobileHeader: React.FC<MobileHeaderProps> = ({
  isAuthenticated,
  user,
  toggleMobileMenu,
  logout,
}) => {
  return (
    <header className="sticky top-0 z-40 border-b border-desnz-navy bg-desnz-blue lg:hidden bg-white dark:bg-black">
      <div className="flex h-20 items-center justify-between px-2 ">
        {isAuthenticated ? (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              className="lg:hidden"
              onClick={toggleMobileMenu}
            >
              <Menu className="h-5 w-5" />
              <span className="sr-only">Toggle menu</span>
            </Button>
            <LogoSection
              variant="mobile"
              isAuthenticated={isAuthenticated}
              userRole={user?.role}
            />
          </div>
        ) : (
          <LogoSection variant="mobile" isAuthenticated={false} />
        )}

        <div className="flex items-center space-x-2 ml-auto">
          {isAuthenticated && user && (
            <UserProfileDropdown user={user} logout={logout} />
          )}
          <ThemeToggle />
        </div>
      </div>
    </header>
  );
};
