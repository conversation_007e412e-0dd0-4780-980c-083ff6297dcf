import { memo } from "react";
import { StepComponent } from "@/lib/types/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Layers } from "lucide-react";

interface StepConfigTabContentProps {
  component: StepComponent;
  onChange: <K extends keyof StepComponent>(field: K, value: StepComponent[K]) => void;
}

/**
 * Configuration tab content for Step components
 */
export const StepConfigTabContent = memo(function StepConfigTabContent({
  component,
  onChange,
}: StepConfigTabContentProps) {
  return (
    <div className="space-y-4 pt-4">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h4 className="text-sm font-medium">Step Configuration</h4>
          <div className="flex items-center space-x-2">
            <Layers className="h-5 w-5 text-muted-foreground" />
          </div>
        </div>

        <div className="rounded-md border p-4 space-y-4">
          <p className="text-sm text-muted-foreground">
            Steps provide a sequential navigation structure for your form.
            Users will navigate through steps one at a time.
          </p>

          <div className="grid gap-2">
            <Label htmlFor="icon">Icon (optional)</Label>
            <Input
              id="icon"
              value={component.icon ?? ""}
              onChange={(e) =>
                onChange("icon", e.target.value)
              }
              placeholder="Icon name (e.g., 'user', 'settings')"
            />
            <p className="text-xs text-muted-foreground">
              Enter a Lucide icon name to display with this step
            </p>
          </div>
        </div>
      </div>
    </div>
  );
});

export default StepConfigTabContent;
