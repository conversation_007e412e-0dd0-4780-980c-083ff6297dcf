import { ReactNode } from "react";

interface SubmissionFieldProps {
  label: string;
  value: any;
  type?:
    | "text"
    | "number"
    | "date"
    | "datetime"
    | "select"
    | "checkbox"
    | "checkbox-group"
    | "radio"
    | "infoText";
}

/**
 * Renders a single field in the submission data view
 */
export function SubmissionField({
  label,
  value,
  type = "text",
}: Readonly<SubmissionFieldProps>) {
  // Format the value based on its type
  const formattedValue = formatValue(value, type);

  return (
    <div className="flex flex-col space-y-1 py-2">
      <div className="text-sm font-medium text-muted-foreground">{label}</div>
      <div className="text-base font-medium">{formattedValue}</div>
    </div>
  );
}

/**
 * Format date values
 */
function formatDateValue(value: any, includeTime: boolean): string {
  try {
    const date = new Date(value);
    // Check if date is valid
    if (isNaN(date.getTime())) {
      return String(value);
    }
    return includeTime ? date.toLocaleString() : date.toLocaleDateString();
  } catch {
    // If date parsing fails, return the original value
    return String(value);
  }
}

/**
 * Format number values
 */
function formatNumberValue(value: any): string {
  if (typeof value === "number") {
    return value.toString();
  }
  if (typeof value === "string" && !isNaN(Number(value))) {
    return value;
  }
  return String(value);
}

/**
 * Format object values
 */
function formatObjectValue(value: any): string {
  try {
    return JSON.stringify(value);
  } catch {
    return String(value);
  }
}

/**
 * Format value based on its type
 */
function formatValue(value: any, type: string): ReactNode {
  // Handle undefined or null values
  if (value === undefined || value === null || value === "") {
    return <span className="text-muted-foreground italic">Not provided</span>;
  }

  // Format based on type
  switch (type) {
    case "date":
      return formatDateValue(value, false);

    case "datetime":
      return formatDateValue(value, true);

    case "checkbox":
      return value === true || value === "true" ? "Yes" : "No";

    case "checkbox-group":
      // For checkbox groups (arrays of selected values)
      if (Array.isArray(value)) {
        return (
          <div className="flex flex-wrap gap-1">
            {value.map((item, index) => (
              <span
                key={`${item}-${index}`}
                className="px-2 py-1 bg-primary/10 text-primary rounded-md text-sm"
              >
                {item}
              </span>
            ))}
          </div>
        );
      }
      return value;

    case "select":
    case "radio":
      // For select/radio, we just display the selected value
      return value;

    case "number":
      return formatNumberValue(value);

    case "infoText":
      // For info text, just display the content directly
      return value;

    default:
      // For text and other types
      if (Array.isArray(value)) {
        // Handle any other arrays
        return (
          <div className="flex flex-wrap gap-1">
            {value.map((item, index) => (
              <span
                key={`item-${index}-${
                  typeof item === "object" ? JSON.stringify(item) : item
                }`}
                className="px-2 py-1 bg-muted rounded-md text-sm"
              >
                {typeof item === "object" ? JSON.stringify(item) : String(item)}
              </span>
            ))}
          </div>
        );
      } else if (typeof value === "object") {
        return formatObjectValue(value);
      }
      return value;
  }
}
