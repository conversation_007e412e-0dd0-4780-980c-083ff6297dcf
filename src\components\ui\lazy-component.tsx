import { lazy, Suspense, ComponentType } from "react";
import { Loading } from "@/components/ui/loading";

interface LazyComponentProps {
  fallback?: React.ReactNode;
  children?: React.ReactNode;
}

/**
 * Creates a lazy-loaded component with a loading fallback
 * 
 * @param importFn - Function that imports the component
 * @param fallback - Optional custom fallback component
 * @returns Lazy-loaded component
 */
export function createLazyComponent<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  fallback?: React.ReactNode
): React.ComponentType<React.ComponentProps<T>> {
  const LazyComponent = lazy(importFn);
  
  const WrappedComponent = (props: React.ComponentProps<T>) => (
    <Suspense fallback={fallback || <Loading message="Loading component..." />}>
      <LazyComponent {...props} />
    </Suspense>
  );
  
  // Set display name for debugging
  const componentName = importFn.toString().match(/\/([^/]+)\.tsx/)?.[1] || "LazyComponent";
  WrappedComponent.displayName = `Lazy(${componentName})`;
  
  return WrappedComponent;
}

/**
 * Lazy component wrapper for code splitting
 */
export function LazyComponent({ fallback, children }: LazyComponentProps) {
  return (
    <Suspense fallback={fallback || <Loading message="Loading component..." />}>
      {children}
    </Suspense>
  );
}
