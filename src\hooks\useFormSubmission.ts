import { useForm, UseFormReturn } from "react-hook-form";
import { useAuth } from "@/contexts/AuthContext";
import { useStepNavigation } from "./useStepNavigation";
import { useFormDataLoader } from "./useFormDataLoader";
import { useFormSubmissionOperations } from "./useFormSubmissionOperations";
import { useFormState } from "./useFormState";
import { FormStep } from "@/lib/utils/form-structure-utils";
import { FormAction, FormState } from "@/contexts/FormStateContext";

interface UseFormSubmissionProps {
  formId: string;
  dispatch: React.Dispatch<FormAction>;
  state: FormState;
  submissionId?: string; // Optional - if provided, will load existing submission
  projectRef?: string;
}

interface UseFormSubmissionReturn {
  methods: UseFormReturn;
  isLoading: boolean;
  isSaving: boolean;
  isSubmitting: boolean;
  formStatus: {
    isSubmitted: boolean;
    isValid: boolean;
    message: string;
  };
  setFormStatus: (status: {
    isSubmitted: boolean;
    isValid: boolean;
    message: string;
  }) => void;
  steps: FormStep[];
  isMultiStep: boolean;
  currentStep: number;
  currentStepData: FormStep;
  nextStep: () => Promise<void>;
  prevStep: () => void;
  saveProgress: () => Promise<boolean>;
  submitForm: () => Promise<boolean>;
  getProcessData: () => Promise<Record<string, any>>;
}

/**
 * Custom hook for form submission functionality
 * Coordinates form loading, state management, step navigation, and form operations
 */
export function useFormSubmission({
  formId,
  dispatch,
  state,
  submissionId,
  projectRef,
}: UseFormSubmissionProps): UseFormSubmissionReturn {
  const { user } = useAuth();

  // Create a form instance with react-hook-form
  const methods = useForm({
    mode: "onSubmit",
    criteriaMode: "all", // Show all validation errors
  });

  // Initialize form state
  const { formStatus, setFormStatus: updateFormStatus } = useFormState();

  // Create a React.Dispatch compatible setFormStatus function
  const setFormStatus: React.Dispatch<
    React.SetStateAction<{
      isSubmitted: boolean;
      isValid: boolean;
      message: string;
    }>
  > = (value) => {
    if (typeof value === "function") {
      const updater = value;
      updateFormStatus(updater(formStatus));
    } else {
      updateFormStatus(value);
    }
  };

  // Load form and submission data
  const { isLoading } = useFormDataLoader({
    formId,
    projectRef,
    submissionId,
    methods,
    user,
    setFormStatus,
    dispatch,
  });

  // Use step navigation
  const {
    currentStep,
    currentStepData,
    nextStep,
    prevStep,
    commitCurrentStepData,
    steps,
    isMultiStep,
  } = useStepNavigation({
    methods,
    state,
    setFormStatus,
  });

  // Form operations (save and submit)
  const { isSaving, isSubmitting, saveProgress, submitForm, getProcessData } =
    useFormSubmissionOperations({
      state,
      dispatch,
      methods,
      user,
      setFormStatus,
      commitCurrentStepData,
      projectRef,
    });

  return {
    methods,
    isLoading,
    isSaving,
    isSubmitting,
    formStatus,
    setFormStatus,
    steps,
    isMultiStep,
    currentStep,
    currentStepData,
    nextStep,
    prevStep,
    saveProgress,
    submitForm,
    getProcessData,
  };
}
