import React, { useEffect } from "react";
import { Outlet, useLocation } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { Loading } from "@/components/ui/loading";
import { CognitoService } from "@/lib/services/cognito-service";

/**
 * Root layout component that handles initial authentication check
 * Shows a loading screen while checking authentication
 * Redirects to login if not authenticated
 */
const RootLayout: React.FC = () => {
  const { isAuthenticated, isLoading } = useAuth();
  const location = useLocation();

  // Handle redirection to Cognito login if not authenticated
  useEffect(() => {
    // Clear any stuck redirect flags when the component mounts
    if (sessionStorage.getItem("cognito_redirecting")) {
      sessionStorage.removeItem("cognito_redirecting");
    }

    if (!isLoading && !isAuthenticated && location.pathname !== "/callback") {
      // Force redirect to ensure it happens
      CognitoService.redirectToLogin(true);
    }

    // Cleanup function to clear the flag when the component unmounts
    return () => {
      if (sessionStorage.getItem("cognito_redirecting")) {
        sessionStorage.removeItem("cognito_redirecting");
      }
    };
  }, [isLoading, isAuthenticated, location.pathname]);

  // If we're on the callback page, render the outlet directly
  if (location.pathname === "/callback") {
    return <Outlet />;
  }

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center bg-background">
        <Loading
          className="py-8"
          iconClassName="h-12 w-12"
          message="Checking authentication..."
        />
      </div>
    );
  }

  // If not authenticated, show loading while redirecting to Cognito
  if (!isAuthenticated) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center bg-background">
        <Loading
          className="py-8"
          iconClassName="h-12 w-12"
          message="Redirecting to login..."
        />
      </div>
    );
  }

  // User is authenticated, render the outlet
  return <Outlet />;
};

export default RootLayout;
