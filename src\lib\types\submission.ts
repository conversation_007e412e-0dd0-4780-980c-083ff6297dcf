import { FormSchema } from "../schemas/form-schemas";
import { User } from "./auth";

/**
 * Submission status types
 */
export type SubmissionStatus =
  | "draft" // Partially completed, saved for later
  | "submitted" // Completed and submitted
  | "approved" // Reviewed and approved by admin
  | "rejected" // Reviewed and rejected by admin
  | "returned"; // Returned to applicant for corrections

/**
 * Form submission metadata
 */
export interface SubmissionMeta {
  id: string;
  formId: string;
  formName: string;
  applicantId?: string;
  applicantName?: string;
  status: SubmissionStatus | ApplicationStatus;
  createdAt?: string;
  updatedAt?: string;
  submittedAt?: string;
  reviewedAt?: string;
  reviewedBy?: string;
}

/**
 * Complete form submission data
 */
export interface FormSubmission {
  id: string;
  formId: string;
  projectRef?: string;
  version?: string;
  formSchema: FormSchema;
  applicantId?: string;
  applicant?: User;
  status: ApplicationStatus;
  data: Record<string, any>;
  createdAt?: string;
  updatedAt?: string;
  submittedAt?: string;
  reviewedAt?: string;
  reviewedBy?: string;
  reviewNotes?: string;
  submittedBy?: string;
}

export type ApplicationStatus = "DRAFT" | "SUBMITTED";

export interface FormApplication {
  id: string;
  status: ApplicationStatus;
  formData: Record<string, any>;
  formId: string;
  projectRef?: string;
  createdAt?: string;
  updatedAt?: string;
  submittedAt?: string;
  submittedBy?: string;
}

/**
 * Form submission creation data
 */
export type FormSubmissionCreate = Omit<
  FormSubmission,
  "id" | "createdAt" | "updatedAt" | "reviewedAt" | "reviewedBy" | "reviewNotes"
>;

/**
 * Form submission update data
 */
export type FormSubmissionUpdate = Partial<
  Omit<FormSubmission, "formSchema" | "applicantId" | "applicant" | "createdAt">
>;
