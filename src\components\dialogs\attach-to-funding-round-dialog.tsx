
import { useForm } from "react-hook-form";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { SearchableSelect, SearchableSelectOption } from "@/components/ui/searchable-select";

import { useFundingRounds, useAttachProjectToFundingRound } from "@/hooks/use-funding-rounds";
import { useToast } from "@/components/ui/use-toast";
import { Loading } from "@/components/ui/loading";
import { useAttachFundingRoundDialog } from "@/hooks/use-attach-funding-round-dialog";

type FormData = {
  fundingRoundId: string;
};

export function AttachToFundingRoundDialog() {
  const { toast } = useToast();
  const { isOpen, projectData, closeDialog } = useAttachFundingRoundDialog();

  // Fetch funding rounds data
  const {
    data: fundingRounds,
    isLoading: isFundingRoundsLoading,
    error: fundingRoundsError,
  } = useFundingRounds();

  // Attach project to funding round mutation
  const attachProjectMutation = useAttachProjectToFundingRound();

  // Form setup
  const {
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<FormData>({
    defaultValues: {
      fundingRoundId: "",
    },
  });

  const selectedFundingRoundId = watch("fundingRoundId");

  // Convert funding rounds to searchable select options
  const fundingRoundOptions: SearchableSelectOption[] =
    fundingRounds?.content?.map((round) => ({
      value: (round as any).id,
      label: `Round ${(round as any).roundNumber} (${new Date((round as any).openDate).toLocaleDateString()} - ${new Date((round as any).closeDate).toLocaleDateString()})`,
    })) ?? [];

  const onSubmit = async (data: FormData) => {
    if (!data.fundingRoundId) {
      toast({
        title: "Error",
        description: "Please select a funding round.",
        variant: "destructive",
      });
      return;
    }

    if (!projectData?.id) {
      toast({
        title: "Error",
        description: "Project data is missing.",
        variant: "destructive",
      });
      return;
    }

    // Call the API to attach the project to the funding round
    attachProjectMutation.mutate(
      {
        fundingRoundId: data.fundingRoundId,
        projectId: projectData.id,
      },
      {
        onSuccess: () => {
          closeDialog();
        },
      }
    );
  };

  const handleCancel = () => {
    closeDialog();
  };

  return (
    <Dialog open={isOpen} onOpenChange={closeDialog}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Attach to Funding Round</DialogTitle>
          <DialogDescription>
            Select a funding round to attach the project "{projectData?.name}" to.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="fundingRound">Funding Round</Label>
            {isFundingRoundsLoading ? (
              <Loading message="Loading funding rounds..." />
            ) : fundingRoundsError ? (
              <div className="text-sm text-destructive">
                Error loading funding rounds. Please try again.
              </div>
            ) : (
              <SearchableSelect
                options={fundingRoundOptions}
                value={selectedFundingRoundId}
                onValueChange={(value) => setValue("fundingRoundId", value)}
                placeholder="Select a funding round..."
                searchPlaceholder="Search funding rounds..."
                emptyMessage="No funding rounds found."
                disabled={attachProjectMutation.isPending}
              />
            )}
            {errors.fundingRoundId && (
              <div className="text-sm text-destructive">
                {errors.fundingRoundId.message}
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={attachProjectMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={attachProjectMutation.isPending || !selectedFundingRoundId}
            >
              {attachProjectMutation.isPending ? "Attaching..." : "Attach"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
