import {
  DataGridComponent,
  GridRowConditionalRendering,
} from "@/lib/schemas/form-schemas";

/**
 * Interface for structured data grid row
 */
export interface StructuredDataGridRow {
  rowHeader: string;
  cells: Record<
    string,
    {
      value: string;
      columnHeader: string;
      inputType?: string;
      unit?: string;
    }
  >;
}

/**
 * Interface for structured data grid
 */
export interface StructuredDataGrid {
  rows: StructuredDataGridRow[];
  metadata: {
    rowHeaders: string[];
    columnHeaders: string[];
    component: {
      id: string;
      name: string;
      label: string;
      conditionalRows?: GridRowConditionalRendering[];
    };
  };
}

/**
 * Transform flat data grid format to structured format
 *
 * @param flatData - Flat data grid format (custom IDs or Excel coordinates as keys)
 * @param component - Data grid component definition
 * @returns Structured data grid format
 */
export function transformToStructured(
  flatData: Record<string, any>,
  component: DataGridComponent
): StructuredDataGrid {
  // Helper function to get value by custom ID or Excel coordinate
  const getValueByCustomId = (rowIndex: number, colIndex: number): string => {
    const excelCoord = `${String.fromCharCode(65 + colIndex)}${rowIndex + 1}`;
    const cellConfig = component.cells[excelCoord];
    const customId = cellConfig?.id || excelCoord;

    // Try custom ID first, then fall back to Excel coordinate
    return flatData[customId] ?? flatData[excelCoord] ?? "";
  };

  // Extract row and column headers
  const rowHeaders: string[] = [];
  const columnHeaders: string[] = [];

  // Get row headers (A2, A3, A4, etc.)
  for (let rowIndex = 1; rowIndex < component.rows; rowIndex++) {
    const excelCoord = `A${rowIndex + 1}`;
    const headerCell = component.cells[excelCoord];
    const headerValue = getValueByCustomId(rowIndex, 0);
    rowHeaders.push(headerValue || headerCell?.value || `Row ${rowIndex}`);
  }

  // Get column headers (B1, C1, D1, etc.)
  for (let colIndex = 1; colIndex < component.columns; colIndex++) {
    const excelCoord = `${String.fromCharCode(65 + colIndex)}1`;
    const headerCell = component.cells[excelCoord];
    const headerValue = getValueByCustomId(0, colIndex);
    columnHeaders.push(
      headerValue || headerCell?.value || `Column ${colIndex}`
    );
  }

  // Create structured rows
  const rows: StructuredDataGridRow[] = [];

  for (let rowIndex = 1; rowIndex < component.rows; rowIndex++) {
    const rowHeader = rowHeaders[rowIndex - 1];
    const rowCells: Record<string, any> = {};

    for (let colIndex = 1; colIndex < component.columns; colIndex++) {
      const excelCoord = `${String.fromCharCode(65 + colIndex)}${rowIndex + 1}`;
      const columnHeader = columnHeaders[colIndex - 1];
      const cellConfig = component.cells[excelCoord];
      const cellValue = getValueByCustomId(rowIndex, colIndex);

      rowCells[columnHeader] = {
        value: cellValue,
        columnHeader,
        inputType: cellConfig?.inputType,
        unit: cellConfig?.unit,
      };
    }

    rows.push({
      rowHeader,
      cells: rowCells,
    });
  }

  // Create the structured data
  const structuredData = {
    rows,
    metadata: {
      rowHeaders,
      columnHeaders,
      component: {
        id: component.id,
        name: component.name,
        label: component.label,
        conditionalRows: component.conditionalRows,
      },
    },
  };

  return structuredData;
}

/**
 * Transform structured data grid format back to flat format
 *
 * @param structuredData - Structured data grid format
 * @param component - Data grid component definition
 * @returns Flat data grid format (custom IDs as keys)
 */
export function transformToFlat(
  structuredData: StructuredDataGrid,
  component: DataGridComponent
): Record<string, any> {
  const flatData: Record<string, any> = {};

  // First, initialize all cells with empty values using custom IDs
  for (let rowIndex = 0; rowIndex < component.rows; rowIndex++) {
    for (let colIndex = 0; colIndex < component.columns; colIndex++) {
      const excelCoord = `${String.fromCharCode(65 + colIndex)}${rowIndex + 1}`;
      const cellConfig = component.cells[excelCoord];
      const customId = cellConfig?.id || excelCoord;
      flatData[customId] = "";
    }
  }

  // Then, preserve header cells from the component definition
  for (let rowIndex = 0; rowIndex < component.rows; rowIndex++) {
    for (let colIndex = 0; colIndex < component.columns; colIndex++) {
      const excelCoord = `${String.fromCharCode(65 + colIndex)}${rowIndex + 1}`;
      const cell = component.cells[excelCoord];
      const customId = cell?.id || excelCoord;

      if (cell?.type === "header") {
        flatData[customId] = cell.value || "";
      }
    }
  }

  // Map structured data back to flat format using custom IDs
  if (structuredData?.rows && Array.isArray(structuredData.rows)) {
    structuredData.rows.forEach((row, rowIndex) => {
      const actualRowIndex = rowIndex + 1; // Skip header row

      if (row?.cells) {
        Object.entries(row.cells).forEach(([columnHeader, cellData]) => {
          // Find the column index for this header
          const colIndex = structuredData.metadata?.columnHeaders?.findIndex(
            (header) => header === columnHeader
          );

          if (colIndex !== -1) {
            const actualColIndex = colIndex + 1; // Skip row header column
            const excelCoord = `${String.fromCharCode(65 + actualColIndex)}${
              actualRowIndex + 1
            }`;

            // Get the custom ID for this cell
            const cellConfig = component.cells[excelCoord];
            const customId = cellConfig?.id || excelCoord;

            // Handle both string values and object values with a value property
            let cellValue;
            if (typeof cellData === "object" && cellData !== null) {
              if ("value" in cellData) {
                cellValue = cellData.value;
              } else {
                // If it's an object without a value property, stringify it
                cellValue = JSON.stringify(cellData);
              }
            } else {
              cellValue = cellData;
            }

            flatData[customId] = cellValue;
          }
        });
      }
    });
  }

  return flatData;
}
