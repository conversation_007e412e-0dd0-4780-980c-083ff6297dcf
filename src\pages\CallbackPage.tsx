import { useEffect, useState, useRef } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { Loading } from "@/components/ui/loading";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";

/**
 * Callback page for handling Cognito authentication redirect
 * This page is responsible for:
 * 1. Extracting the authorization code from the URL
 * 2. Exchanging the code for tokens
 * 3. Redirecting to the appropriate page based on user role
 */
export default function CallbackPage() {
  const { handleCallback, isLoading, user, isAuthenticated } = useAuth();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [error, setError] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);

  // Use a ref to track if we've already processed the callback
  const hasProcessedCallback = useRef<boolean>(false);

  // Add a timeout to automatically redirect after 5 seconds
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (!error) {
        navigate("/projects", { replace: true });
      }
    }, 5000);

    return () => clearTimeout(timeoutId);
  }, [navigate, error]);

  // Add debug logging for auth state and handle redirection when user state changes
  useEffect(() => {
    // If authentication is complete and user is available, redirect based on role
    if (!isLoading && isAuthenticated && user && !isProcessing) {
      if (user.role === "admin") {
        navigate("/forms", { replace: true });
      } else {
        navigate("/projects", { replace: true });
      }
    }
  }, [isLoading, isAuthenticated, user, navigate, isProcessing]);

  useEffect(() => {
    // Clear the redirect flag when the callback page is loaded
    sessionStorage.removeItem("cognito_redirecting");

    const processCallback = async () => {
      // Prevent multiple processing attempts using the ref
      if (hasProcessedCallback.current) {
        return;
      }

      // Prevent multiple processing attempts using state
      if (isProcessing) {
        return;
      }

      // Mark as processing
      setIsProcessing(true);
      hasProcessedCallback.current = true;

      try {
        // Get authorization code from URL
        const code = searchParams.get("code");

        if (!code) {
          setError("No authorization code found in URL");
          setIsProcessing(false);
          return;
        }

        // Handle the callback in the auth context
        const success = await handleCallback(code);

        if (!success) {
          setError("Failed to authenticate with Cognito");
          setIsProcessing(false);
          return;
        }

        // Add a small delay to ensure the user state has been updated
        await new Promise((resolve) => setTimeout(resolve, 500));

        // We'll let the other useEffect handle the redirection based on user state
        setIsProcessing(false);
      } catch (err) {
        console.error("Error processing callback:", err);
        setError("An error occurred during authentication");
        setIsProcessing(false);
      }
    };

    // Only process the callback once when the component mounts
    // Don't depend on isLoading which can change multiple times
    processCallback();

    // Cleanup function to reset processing flag
    return () => {
      setIsProcessing(false);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center">
        <Alert variant="destructive" className="max-w-md">
          <AlertCircle className="h-5 w-5" />
          <AlertDescription className="text-base">{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center gap-4">
      <Loading
        className="py-8"
        iconClassName="h-12 w-12"
        message="Authenticating..."
        showMessage={false}
      />
      <div className="text-center">
        <h2 className="text-xl font-semibold mb-2">Authenticating</h2>
        <p className="text-muted-foreground">
          Please wait while we complete your authentication...
        </p>
      </div>
    </div>
  );
}
