import {
  FormSchema,
  FormComponent,
  DataGridComponent,
} from "@/lib/schemas/form-schemas";
import { FormSubmission } from "@/lib/types/submission";
import { useMemo, useRef, useEffect } from "react";

// Types
type TokenType =
  | "FUNCTION"
  | "CELL_REF"
  | "RANGE_REF"
  | "OPERATOR"
  | "NUMBER"
  | "LPAREN"
  | "RPAREN"
  | "COMMA"
  | "SELF";

interface FormulaToken {
  type: TokenType;
  value: string;
}

interface CalculationEngineResult {
  calculatedValues: Record<string, any>;
  evaluateValidationExpression: (
    expression: string,
    componentId: string,
    submissionData: FormSubmission["data"]
  ) => boolean;
}

// --- 1. Formula Parser ---
const tokenRegex =
  /[a-zA-Z0-9_-]+\.[A-Z]+\d+:[A-Z]+\d+|[a-zA-Z0-9_-]+\.[A-Z]+\d+|[a-zA-Z_][a-zA-Z0-9_]*|==|!=|<=|>=|<|>|[+\-*/]|\d+(?:\.\d+)?|\(|\)|,/g;

const operatorPrecedence: { [key: string]: number } = {
  "+": 2,
  "-": 2,
  "*": 3,
  "/": 3,
  "==": 1,
  "!=": 1,
  "<": 1,
  ">": 1,
  "<=": 1,
  ">=": 1,
};

export function parseFormula(formula: string): FormulaToken[] {
  const outputQueue: FormulaToken[] = [];
  const operatorStack: FormulaToken[] = [];

  const tokens = formula.match(tokenRegex) || [];

  tokens.forEach((tokenValue) => {
    if (/^\d+(\.\d+)?$/.test(tokenValue)) {
      outputQueue.push({ type: "NUMBER", value: tokenValue });
    } else if (/^(SUM|AVG|PERCENTAGE)$/.test(tokenValue)) {
      operatorStack.push({ type: "FUNCTION", value: tokenValue });
    } else if (tokenValue === "SELF") {
      outputQueue.push({ type: "SELF", value: tokenValue });
    } else if (/^[a-zA-Z0-9_-]+\.[A-Z]+\d+:[A-Z]+\d+$/.test(tokenValue)) {
      outputQueue.push({ type: "RANGE_REF", value: tokenValue });
    } else if (/^[a-zA-Z0-9_-]+\.[A-Z]+\d+$/.test(tokenValue)) {
      outputQueue.push({ type: "CELL_REF", value: tokenValue });
    } else if (/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(tokenValue)) {
      outputQueue.push({ type: "CELL_REF", value: tokenValue });
    } else if (/^(==|!=|<=|>=|<|>|[+\-*/])$/.test(tokenValue)) {
      while (
        operatorStack.length > 0 &&
        operatorStack[operatorStack.length - 1].type === "OPERATOR" &&
        operatorPrecedence[operatorStack[operatorStack.length - 1].value] >=
          operatorPrecedence[tokenValue]
      ) {
        outputQueue.push(operatorStack.pop()!);
      }
      operatorStack.push({ type: "OPERATOR", value: tokenValue });
    } else if (tokenValue === "(") {
      operatorStack.push({ type: "LPAREN", value: tokenValue });
    } else if (tokenValue === ")") {
      while (
        operatorStack.length > 0 &&
        operatorStack[operatorStack.length - 1].type !== "LPAREN"
      ) {
        outputQueue.push(operatorStack.pop()!);
      }
      operatorStack.pop(); // Pop LPAREN
      if (
        operatorStack.length > 0 &&
        operatorStack[operatorStack.length - 1].type === "FUNCTION"
      ) {
        outputQueue.push(operatorStack.pop()!);
      }
    }
  });

  while (operatorStack.length > 0) {
    outputQueue.push(operatorStack.pop()!);
  }

  return outputQueue;
}

// --- 2. Evaluation Logic ---
export function evaluate(
  rpn: FormulaToken[],
  flattenedData: Record<string, any>
): number | string | boolean {
  const stack: (number | string | any[] | boolean)[] = [];

  const getCellValue = (ref: string): number => {
    const value = flattenedData[ref];
    return parseFloat(value) || 0;
  };

  const getRangeValues = (ref: string): number[] => {
    const lastDotIndex = ref.lastIndexOf(".");
    if (lastDotIndex === -1) {
      console.error("Invalid range reference, missing gridId or range", ref);
      return [];
    }

    const gridId = ref.substring(0, lastDotIndex);
    const rangeStr = ref.substring(lastDotIndex + 1);

    const [startCell, endCell] = rangeStr.split(":");
    if (!endCell) {
      const value = flattenedData[ref];
      return [parseFloat(value) || 0];
    }

    const parseCellId = (cellId: string) => {
      const colStr = cellId.match(/[A-Z]+/)?.[0];
      const rowStr = cellId.match(/\d+/)?.[0];
      if (!colStr || !rowStr) return null;

      const row = parseInt(rowStr, 10);
      const col =
        colStr.split("").reduce((acc, char) => {
          return acc * 26 + char.charCodeAt(0) - "A".charCodeAt(0) + 1;
        }, 0) - 1;

      return { col, row };
    };

    const start = parseCellId(startCell);
    const end = parseCellId(endCell);

    if (!start || !end) {
      console.error("Invalid range format:", ref);
      return [];
    }

    const startCol = Math.min(start.col, end.col);
    const endCol = Math.max(start.col, end.col);
    const startRow = Math.min(start.row, end.row);
    const endRow = Math.max(start.row, end.row);

    const values: number[] = [];
    for (let r = startRow; r <= endRow; r++) {
      for (let c = startCol; c <= endCol; c++) {
        let colStr = "";
        let temp = c + 1;
        while (temp > 0) {
          const remainder = (temp - 1) % 26;
          colStr = String.fromCharCode("A".charCodeAt(0) + remainder) + colStr;
          temp = Math.floor((temp - 1) / 26);
        }
        const cellId = `${colStr}${r}`;
        const value = flattenedData[`${gridId}.${cellId}`];
        values.push(parseFloat(value) || 0);
      }
    }
    return values;
  };

  for (const token of rpn) {
    switch (token.type) {
      case "NUMBER":
        stack.push(parseFloat(token.value));
        break;
      case "SELF":
        stack.push(parseFloat(flattenedData["SELF"]) || 0);
        break;
      case "CELL_REF":
        stack.push(getCellValue(token.value));
        break;
      case "RANGE_REF":
        stack.push(getRangeValues(token.value));
        break;
      case "OPERATOR":
        const b = stack.pop();
        const a = stack.pop();
        switch (token.value) {
          case "+":
            stack.push((a as number) + (b as number));
            break;
          case "-":
            stack.push((a as number) - (b as number));
            break;
          case "*":
            stack.push((a as number) * (b as number));
            break;
          case "/":
            if (b === 0) return "Error: Division by zero";
            stack.push((a as number) / (b as number));
            break;
          case "==":
            stack.push(a === b);
            break;
          case "!=":
            stack.push(a !== b);
            break;
          case ">":
            stack.push((a as number) > (b as number));
            break;
          case "<":
            stack.push((a as number) < (b as number));
            break;
          case ">=":
            stack.push((a as number) >= (b as number));
            break;
          case "<=":
            stack.push((a as number) <= (b as number));
            break;
        }
        break;
      case "FUNCTION":
        const arg = stack.pop();
        if (token.value === "SUM") {
          if (Array.isArray(arg)) {
            stack.push(arg.reduce((acc, val) => acc + val, 0));
          }
        } else if (token.value === "AVG") {
          if (Array.isArray(arg) && arg.length > 0) {
            stack.push(arg.reduce((acc, val) => acc + val, 0) / arg.length);
          } else {
            stack.push(0);
          }
        } else if (token.value === "PERCENTAGE") {
          if (Array.isArray(arg) && arg.length >= 2) {
            if (arg[0] === 0) {
              stack.push(0);
            } else {
              const part = arg[arg.length - 1];
              const total = arg[0];
              const percentage = parseFloat(((part / total) * 100).toFixed(2));
              stack.push(percentage);
            }
          }
        }
        break;
    }
  }

  return stack[0] as number | string | boolean;
}

const transformKeysWithId = (
  input: Record<string, number>,
  id: string
): Record<string, string> => {
  const result: Record<string, string> = {};

  for (const [key, value] of Object.entries(input)) {
    result[`${id}.${key}`] = value === 0 ? "" : value.toString();
  }

  return result;
};

const mergeWithUpdates = (
  source: Record<string, string>,
  base: Record<string, string>
): Record<string, string> => {
  const result: Record<string, string> = {};

  for (const key in base) {
    result[key] = source.hasOwnProperty(key) ? source[key] : base[key];
  }

  return result;
};

// --- 3. Hook Implementation ---
const flattenSubmissionData = (
  submissionData: FormSubmission["data"] | null,
  allComponents: FormComponent[]
): Record<string, any> => {
  if (!submissionData) return {};

  const flattenedData: Record<string, any> = {};
  let updatedData: Record<string, any> = {};
  const componentNameMap = new Map(allComponents.map((c) => [c.name, c]));

  for (const componentName in submissionData) {
    if (!Object.prototype.hasOwnProperty.call(submissionData, componentName)) {
      continue;
    }

    const component = componentNameMap.get(componentName);
    if (!component) {
      continue;
    }

    const value = submissionData[componentName];

    if (component.type === "datagrid") {
      const gridComponent = component as DataGridComponent;

      if (component.id && submissionData[component.id]) {
        updatedData = transformKeysWithId(
          submissionData[component.id],
          component.id
        );
      }

      // Handle both structured and flat data formats
      if (value && typeof value === "object") {
        // Check if it's structured format (has rows and metadata)
        if ("rows" in value && "metadata" in value) {
          const gridData = value as {
            rows?: Array<{
              rowHeader: string;
              cells: Record<string, { value: any }>;
            }>;
          };

          if (!gridData || !gridData.rows) {
            continue;
          }

          // Process structured data (existing logic)
          const rowHeaderToRowIndex = new Map<string, string>();
          const colHeaderToColLetter = new Map<string, string>();

          const gridCells = gridComponent.cells;
          for (const cellId in gridCells) {
            const cell = gridCells[cellId];
            if (cell.type === "header" && cell.value) {
              const match = cellId.match(/^([A-Z]+)(\d+)$/);
              if (match) {
                const colLetter = match[1];
                const rowNumber = match[2];
                if (colLetter === "A" && parseInt(rowNumber, 10) > 1) {
                  rowHeaderToRowIndex.set(cell.value, rowNumber);
                } else if (parseInt(rowNumber, 10) === 1 && colLetter !== "A") {
                  colHeaderToColLetter.set(cell.value, colLetter);
                }
              }
            }
          }

          for (const row of gridData.rows) {
            const rowIndex = rowHeaderToRowIndex.get(row.rowHeader);
            if (rowIndex) {
              for (const cellHeader in row.cells) {
                const colLetter = colHeaderToColLetter.get(cellHeader);
                if (colLetter) {
                  const excelCellId = `${colLetter}${rowIndex}`;

                  // Get the custom cell ID from the grid component configuration
                  const cellConfig = gridComponent.cells[excelCellId];
                  const customCellId = cellConfig?.id || excelCellId;

                  // Create flattened keys for both Excel coordinate and custom ID
                  const excelFlattenedKey = `${gridComponent.id}.${excelCellId}`;
                  const customFlattenedKey = `${gridComponent.id}.${customCellId}`;

                  const cellValue = row.cells[cellHeader].value;

                  // Store under both keys for backward compatibility
                  flattenedData[excelFlattenedKey] = cellValue;
                  if (customCellId !== excelCellId) {
                    flattenedData[customFlattenedKey] = cellValue;
                  }

                  // Also store the custom cell ID directly for validation expressions
                  flattenedData[customCellId] = cellValue;
                }
              }
            }
          }
        } else {
          // Handle flat data format (custom ID format)
          for (const [customCellId, cellValue] of Object.entries(value)) {
            // Store the custom cell ID directly for validation expressions
            flattenedData[customCellId] = cellValue;

            // Also create a grid-prefixed key for consistency
            flattenedData[`${gridComponent.id}.${customCellId}`] = cellValue;
          }
        }
      }

      continue; // Skip the default handling below
    }

    // Default handling for non-datagrid components
    flattenedData[component.name] = value;
  }

  // return flattenedData;
  return mergeWithUpdates(updatedData, flattenedData);
};

// Helper to expand a range reference into individual cell references
const expandRange = (ref: string): string[] => {
  const lastDotIndex = ref.lastIndexOf(".");
  if (lastDotIndex === -1) {
    console.error("Invalid range reference, missing gridId or range", ref);
    return [];
  }

  const gridId = ref.substring(0, lastDotIndex);
  const rangeStr = ref.substring(lastDotIndex + 1);

  const [startCell, endCell] = rangeStr.split(":");
  if (!endCell) {
    return [ref]; // It's a single cell reference, not a range
  }

  const parseCellId = (cellId: string) => {
    const colStr = cellId.match(/[A-Z]+/)?.[0];
    const rowStr = cellId.match(/\d+/)?.[0];
    if (!colStr || !rowStr) return null;
    const row = parseInt(rowStr, 10);
    const col =
      colStr
        .split("")
        .reduce(
          (acc, char) => acc * 26 + char.charCodeAt(0) - "A".charCodeAt(0) + 1,
          0
        ) - 1;
    return { col, row };
  };

  const start = parseCellId(startCell);
  const end = parseCellId(endCell);

  if (!start || !end) {
    console.error("Invalid range format:", ref);
    return [];
  }

  const startCol = Math.min(start.col, end.col);
  const endCol = Math.max(start.col, end.col);
  const startRow = Math.min(start.row, end.row);
  const endRow = Math.max(start.row, end.row);

  const cells: string[] = [];
  for (let r = startRow; r <= endRow; r++) {
    for (let c = startCol; c <= endCol; c++) {
      let colStr = "";
      let temp = c + 1;
      while (temp > 0) {
        const remainder = (temp - 1) % 26;
        colStr = String.fromCharCode("A".charCodeAt(0) + remainder) + colStr;
        temp = Math.floor((temp - 1) / 26);
      }
      const cellId = `${colStr}${r}`;
      cells.push(`${gridId}.${cellId}`);
    }
  }
  return cells;
};
export function useCalculationEngine(
  formSchema: FormSchema | null,
  formValues: FormSubmission["data"] | null
): CalculationEngineResult {
  // 1. Stabilize Dependencies: Memoize allComponents to ensure its reference is stable.
  const allComponents = useMemo(
    () => formSchema?.components || [],
    [formSchema]
  );

  const formulaMapRef = useRef(
    new Map<string, { rpn: FormulaToken[]; dependencies: string[] }>()
  );
  const dependencyGraphRef = useRef(new Map<string, Set<string>>());

  // 2. Build Dependency Graph: Parse formulas and build the graph when the schema changes.
  // Stored in a ref to prevent re-renders.
  useEffect(() => {
    const newFormulaMap = new Map<
      string,
      { rpn: FormulaToken[]; dependencies: string[] }
    >();
    const newDependencyGraph = new Map<string, Set<string>>();

    if (!formSchema) {
      formulaMapRef.current = newFormulaMap;
      dependencyGraphRef.current = newDependencyGraph;
      return;
    }

    const componentsWithCalculations: {
      id: string;
      formula: string;
      gridId?: string;
    }[] = [];
    allComponents.forEach((component) => {
      if (component.calculation?.formula) {
        componentsWithCalculations.push({
          id: component.id,
          formula: component.calculation.formula,
        });
      }
      if (component.type === "datagrid") {
        Object.values((component as DataGridComponent).cells).forEach(
          (cell) => {
            if (cell.calculation?.formula) {
              componentsWithCalculations.push({
                id: `${component.id}.${cell.id}`,
                formula: cell.calculation.formula,
                gridId: component.id,
              });
            }
          }
        );
      }
    });

    componentsWithCalculations.forEach(({ id, formula, gridId }) => {
      let processedFormula = formula;
      if (gridId) {
        // Prepend gridId to unqualified cell/range references (e.g., A1, B2:C3).
        // Uses negative lookbehind to avoid matching already qualified references
        // like `some_other_grid.A1`. It ensures the reference is not preceded
        // by a character that could be part of an ID.
        const cellRefRegex = /(?<![a-zA-Z0-9_.-])([A-Z]+\d+(?::[A-Z]+\d+)?)\b/g;
        processedFormula = formula.replace(cellRefRegex, `${gridId}.$1`);
      }

      const rpn = parseFormula(processedFormula);
      const dependencies: string[] = [];
      rpn.forEach((token) => {
        if (token.type === "CELL_REF") {
          dependencies.push(token.value);
        } else if (token.type === "RANGE_REF") {
          dependencies.push(...expandRange(token.value));
        }
      });

      newFormulaMap.set(id, { rpn, dependencies });

      dependencies.forEach((dep) => {
        if (!newDependencyGraph.has(dep)) {
          newDependencyGraph.set(dep, new Set());
        }
        newDependencyGraph.get(dep)!.add(id);
      });
    });

    formulaMapRef.current = newFormulaMap;
    dependencyGraphRef.current = newDependencyGraph;
  }, [allComponents, formSchema]);

  const prevFlattenedValuesRef = useRef<Record<string, any> | null>(null);
  const calculatedValuesRef = useRef<Record<string, any>>({});

  // 3. Implement Targeted Recalculations
  const calculatedValues = useMemo(() => {
    if (!formSchema || !formValues) {
      return {};
    }
    // if (!formValues.flat) {
    //   return {};
    // }
    const flattenedValues = flattenSubmissionData(formValues, allComponents);
    const prevFlattenedValues = prevFlattenedValuesRef.current;

    const formulaMap = formulaMapRef.current;

    const dependencyGraph = dependencyGraphRef.current;

    const changedDependencies = new Set<string>();

    if (prevFlattenedValues) {
      const allKeys = new Set([
        ...Object.keys(flattenedValues),
        ...Object.keys(prevFlattenedValues),
      ]);
      for (const key of allKeys) {
        if (flattenedValues[key] !== prevFlattenedValues[key]) {
          changedDependencies.add(key);
        }
      }
    } else {
      // First run, consider all fields with values as "changed"
      Object.keys(flattenedValues).forEach((key) =>
        changedDependencies.add(key)
      );
    }

    const evaluationQueue = new Set<string>();
    // On the first run, calculate everything.

    if (!prevFlattenedValues) {
      formulaMap.forEach((_, cellId) => evaluationQueue.add(cellId));
    } else {
      changedDependencies.forEach((dep) => {
        dependencyGraph.get(dep)?.forEach((cell) => evaluationQueue.add(cell));
      });
    }
    if (evaluationQueue.size === 0) {
      return calculatedValuesRef.current;
    }

    const results = { ...calculatedValuesRef.current };

    const evaluateCell = (cellId: string) => {
      const formulaInfo = formulaMap.get(cellId);
      if (!formulaInfo) return;

      try {
        const result = evaluate(formulaInfo.rpn, flattenedValues);
        const idParts = cellId.split(".");

        let oldValue: any;
        if (idParts.length === 2) {
          const [gridId, cell] = idParts;
          oldValue = results[gridId]?.[cell];
          // Create a new object for the grid's results to avoid mutation
          results[gridId] = {
            ...(results[gridId] || {}),
            [cell]: result,
          };
        } else {
          oldValue = results[cellId];
          results[cellId] = result;
        }

        if (result !== oldValue) {
          dependencyGraph
            .get(cellId)
            ?.forEach((dependent) => evaluationQueue.add(dependent));
        }
      } catch (error) {
        console.error(`Error evaluating formula for ${cellId}:`, error);
      }
    };

    const processedInThisRun = new Set<string>();

    while (evaluationQueue.size > 0) {
      const cellToEvaluate = evaluationQueue.values().next().value;
      if (!cellToEvaluate) {
        // Should not happen if size > 0, but for type safety
        break;
      }
      evaluationQueue.delete(cellToEvaluate);

      if (!processedInThisRun.has(cellToEvaluate)) {
        evaluateCell(cellToEvaluate);
        processedInThisRun.add(cellToEvaluate);
      }
    }

    calculatedValuesRef.current = results;
    prevFlattenedValuesRef.current = flattenedValues;

    return results;
  }, [formValues, allComponents, formSchema]);

  const evaluateValidationExpression = (
    expression: string,
    componentId: string,
    submissionData: FormSubmission["data"]
  ): boolean => {
    const selfValue = submissionData[componentId];
    const rpn = parseFormula(expression);
    const flattenedData = flattenSubmissionData(submissionData, allComponents);
    const result = evaluate(rpn, { ...flattenedData, SELF: selfValue });
    return result === true;
  };
  return {
    calculatedValues,
    evaluateValidationExpression,
  };
}
