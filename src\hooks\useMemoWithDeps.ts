import { useRef, useMemo } from "react";
import { isEqual } from "lodash-es";

/**
 * Custom hook for memoizing values with deep dependency comparison
 *
 * This hook is similar to useMemo, but it performs a deep equality check on dependencies
 * instead of the default reference equality check. This is useful when dependencies
 * are objects or arrays that might have the same content but different references.
 *
 * @param factory - Function that returns the memoized value
 * @param deps - Dependencies array for memoization
 * @returns Memoized value
 */
export function useMemoWithDeps<T>(
  factory: () => T,
  deps: React.DependencyList
): T {
  const depsRef = useRef<React.DependencyList | undefined>(undefined);

  // Check if deps have changed using deep comparison
  const depsChanged = !depsRef.current || !isEqual(deps, depsRef.current);

  // If deps have changed, update the ref
  if (depsChanged) {
    depsRef.current = deps;
  }

  // Use useMemo with a single dependency that changes only when deep comparison detects changes
  return useMemo(factory, [depsChanged]);
}

/**
 * Custom hook for memoizing callbacks with deep dependency comparison
 *
 * This hook is similar to useCallback, but it performs a deep equality check on dependencies
 * instead of the default reference equality check. This is useful when dependencies
 * are objects or arrays that might have the same content but different references.
 *
 * @param callback - Function to memoize
 * @param deps - Dependencies array for memoization
 * @returns Memoized callback
 */
export function useCallbackWithDeps<T extends (...args: any[]) => any>(
  callback: T,
  deps: React.DependencyList
): T {
  // Use useMemoWithDeps to memoize the callback
  return useMemoWithDeps(() => callback, deps);
}
