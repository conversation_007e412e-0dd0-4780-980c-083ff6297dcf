/**
 * Utility functions for handling submission data
 */

/**
 * Checks if a value is a structured DataGrid
 */
export const isStructuredDataGrid = (val: any): boolean =>
  val && typeof val === "object" && "rows" in val && "metadata" in val;

/**
 * Checks if a value is a resource matrix
 */
export const isResourceMatrix = (val: any): boolean => {
  if (!val || typeof val !== "object") return false;
  
  // Check if it has the expected structure
  if (!("rows" in val) || !Array.isArray(val.rows)) return false;
  if (!("metadata" in val) || typeof val.metadata !== "object") return false;
  
  // Check if rows have the expected structure
  if (val.rows.length === 0) return false;
  
  // Check first row structure
  const firstRow = val.rows[0];
  return (
    typeof firstRow === "object" &&
    "rowHeader" in firstRow &&
    "cells" in firstRow &&
    typeof firstRow.cells === "object"
  );
};

/**
 * Formats a field name for display
 */
export const formatFieldName = (name: string): string => {
  // Handle special cases
  if (name === "resourceMatrix") return "Resource Matrix";
  
  // Convert camelCase to Title Case with spaces
  return name
    // Insert a space before all uppercase letters
    .replace(/([A-Z])/g, " $1")
    // Replace underscores with spaces
    .replace(/_/g, " ")
    // Capitalize the first letter
    .replace(/^./, (str) => str.toUpperCase())
    // Trim any extra spaces
    .trim();
};
