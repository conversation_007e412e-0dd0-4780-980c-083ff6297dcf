import {
  DataGridCell,
  DataGridCellInputType,
  SelectOption,
} from "@/lib/schemas/form-schemas";
import { z } from "zod";

/**
 * Utility functions for data grid components
 */

/**
 * Convert row/col indices to Excel-style coordinate (e.g., A1, B2)
 *
 * @param rowIndex - The row index (0-based)
 * @param colIndex - The column index (0-based)
 * @returns Excel-style coordinate
 */
export const indicesToExcel = (rowIndex: number, colIndex: number): string => {
  const colLetter = String.fromCharCode(65 + colIndex); // A, B, C, ...
  const rowNumber = rowIndex + 1; // 1, 2, 3, ...
  return `${colLetter}${rowNumber}`;
};

/**
 * Convert Excel-style coordinate to row/col indices
 *
 * @param excelCoord - Excel-style coordinate (e.g., A1, B2)
 * @returns Tuple of [rowIndex, colIndex] (0-based)
 */
export const excelToIndices = (excelCoord: string): [number, number] => {
  const colLetter = excelCoord.charAt(0);
  const rowNumber = parseInt(excelCoord.substring(1));
  return [rowNumber - 1, colLetter.charCodeAt(0) - 65];
};

/**
 * Get cell configuration from component cells
 *
 * @param cells - The cells configuration object
 * @param rowIndex - The row index (0-based)
 * @param colIndex - The column index (0-based)
 * @returns Cell configuration or undefined if not found
 */
export const getCellConfig = (
  cells: Record<string, DataGridCell>,
  rowIndex: number,
  colIndex: number
): DataGridCell | undefined => {
  const cellId = indicesToExcel(rowIndex, colIndex);
  return cells[cellId];
};

/**
 * Check if cell is a header
 *
 * @param cells - The cells configuration object
 * @param rowIndex - The row index (0-based)
 * @param colIndex - The column index (0-based)
 * @returns True if the cell is a header
 */
export const isCellHeader = (
  cells: Record<string, DataGridCell>,
  rowIndex: number,
  colIndex: number
): boolean => {
  const cell = getCellConfig(cells, rowIndex, colIndex);
  return cell?.type === "header";
};

/**
 * Get cell display value (from configuration or default)
 *
 * @param cells - The cells configuration object
 * @param rowIndex - The row index (0-based)
 * @param colIndex - The column index (0-based)
 * @returns Cell display value
 */
export const getCellDisplayValue = (
  cells: Record<string, DataGridCell>,
  rowIndex: number,
  colIndex: number
): string => {
  const cell = getCellConfig(cells, rowIndex, colIndex);
  if (cell?.value) {
    return cell.value;
  }

  // Default column headers (A, B, C, ...)
  if (rowIndex === 0) {
    return String.fromCharCode(65 + colIndex);
  }

  // Default row headers (1, 2, 3, ...)
  if (colIndex === 0) {
    return `${rowIndex}`;
  }

  return "";
};

/**
 * Get cell unit
 *
 * @param cells - The cells configuration object
 * @param rowIndex - The row index (0-based)
 * @param colIndex - The column index (0-based)
 * @returns Cell unit or undefined if not set
 */
export const getCellUnit = (
  cells: Record<string, DataGridCell>,
  rowIndex: number,
  colIndex: number
): string | undefined => {
  const cell = getCellConfig(cells, rowIndex, colIndex);
  return cell?.unit;
};

/**
 * Get cell input type
 *
 * @param cells - The cells configuration object
 * @param rowIndex - The row index (0-based)
 * @param colIndex - The column index (0-based)
 * @returns Cell input type (defaults to "text")
 */
export const getCellInputType = (
  cells: Record<string, DataGridCell>,
  rowIndex: number,
  colIndex: number
): DataGridCellInputType => {
  const cell = getCellConfig(cells, rowIndex, colIndex);
  return cell?.inputType ?? "text";
};

/**
 * Get cell options for select input type
 *
 * @param cells - The cells configuration object
 * @param rowIndex - The row index (0-based)
 * @param colIndex - The column index (0-based)
 * @returns Array of select options
 */
export const getCellOptions = (
  cells: Record<string, DataGridCell>,
  rowIndex: number,
  colIndex: number
): SelectOption[] => {
  const cell = getCellConfig(cells, rowIndex, colIndex);
  return cell?.options || [];
};

/**
 * Get cell min value
 *
 * @param cells - The cells configuration object
 * @param rowIndex - The row index (0-based)
 * @param colIndex - The column index (0-based)
 * @returns Minimum value or undefined if not set
 */
export const getCellMin = (
  cells: Record<string, DataGridCell>,
  rowIndex: number,
  colIndex: number
): number | undefined => {
  const cell = getCellConfig(cells, rowIndex, colIndex);
  return cell?.min;
};

/**
 * Get cell max value
 *
 * @param cells - The cells configuration object
 * @param rowIndex - The row index (0-based)
 * @param colIndex - The column index (0-based)
 * @returns Maximum value or undefined if not set
 */
export const getCellMax = (
  cells: Record<string, DataGridCell>,
  rowIndex: number,
  colIndex: number
): number | undefined => {
  const cell = getCellConfig(cells, rowIndex, colIndex);
  return cell?.max;
};

/**
 * Get cell validation rules
 *
 * @param cells - The cells configuration object
 * @param rowIndex - The row index (0-based)
 * @param colIndex - The column index (0-based)
 * @returns Validation rules object
 */
export const getCellValidationRules = (
  cells: Record<string, DataGridCell>,
  rowIndex: number,
  colIndex: number
): Record<string, any> => {
  const cell = getCellConfig(cells, rowIndex, colIndex);
  if (!cell) return {};

  const rules: Record<string, any> = {};

  // Add required validation
  if (cell.required) {
    rules.required = "This field is required";
  }

  // Add min/max validation for number inputs
  if (cell.inputType === "number") {
    if (cell.min !== undefined) {
      rules.min = {
        value: cell.min,
        message: `Minimum value is ${cell.min}`,
      };
    }
    if (cell.max !== undefined) {
      rules.max = {
        value: cell.max,
        message: `Maximum value is ${cell.max}`,
      };
    }
  }

  return rules;
};

/**
 * Validate a cell value based on its type and rules
 *
 * @param cells - The cells configuration object
 * @param rowIndex - The row index (0-based)
 * @param colIndex - The column index (0-based)
 * @param value - The value to validate
 * @returns Error message or null if valid
 */
export const validateCellValue = (
  cells: Record<string, DataGridCell>,
  rowIndex: number,
  colIndex: number,
  value: string
): string | null => {
  const rules = getCellValidationRules(cells, rowIndex, colIndex);
  if (Object.keys(rules).length === 0) return null;

  const inputType = getCellInputType(cells, rowIndex, colIndex);
  const cell = getCellConfig(cells, rowIndex, colIndex);
  if (!cell) return null;

  try {
    // Create a simple schema based on the cell type and rules
    let schema: z.ZodTypeAny = z.string();

    // Convert value based on input type
    if (inputType === "number") {
      schema = value === "" ? z.string() : z.coerce.number();

      // Add min/max validation
      if (cell.min !== undefined && schema instanceof z.ZodNumber) {
        schema = schema.min(cell.min, `Minimum value is ${cell.min}`);
      }
      if (cell.max !== undefined && schema instanceof z.ZodNumber) {
        schema = schema.max(cell.max, `Maximum value is ${cell.max}`);
      }
    }

    // Add required validation
    if (cell.required) {
      schema = schema.refine(
        (val) => val !== null && val !== undefined && val !== "",
        {
          message: "This field is required",
        }
      );
    } else {
      schema = schema.optional();
    }

    // Validate
    schema.parse(value);
    return null;
  } catch (error) {
    if (error instanceof z.ZodError) {
      return error.errors[0].message;
    }
    return "Validation error";
  }
};
