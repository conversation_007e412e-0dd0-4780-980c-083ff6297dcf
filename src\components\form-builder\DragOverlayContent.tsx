import { memo } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { FormComponent } from "@/lib/types/form";

interface DragOverlayContentProps {
  readonly activeId: string | null;
  readonly components: FormComponent[];
}

function DragOverlayContent({ activeId, components }: DragOverlayContentProps) {
  if (!activeId) return null;

  return (
    <Card className="cursor-grabbing shadow-lg border-primary opacity-80">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-medium">
              {activeId.startsWith("palette-")
                ? activeId.replace("palette-", "").charAt(0).toUpperCase() +
                  activeId.replace("palette-", "").slice(1) +
                  " Field"
                : components.find((c) => c.id === activeId)?.label}
            </h3>
            <p className="text-sm text-muted-foreground">
              {activeId.startsWith("palette-")
                ? activeId.replace("palette-", "")
                : `${components.find((c) => c.id === activeId)?.type} - ${
                    components.find((c) => c.id === activeId)?.name
                  }`}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default memo(DragOverlayContent);
