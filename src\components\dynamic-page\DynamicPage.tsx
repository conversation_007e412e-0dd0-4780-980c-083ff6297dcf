import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  PageConfig,
  ListPageConfig,
  FormPageConfig,
} from "@/lib/types/page-config";
import EnhancedDynamicListPage from "./EnhancedDynamicListPage";
import DynamicFormPage from "./DynamicFormPage";
// import { useAuth } from "@/contexts/AuthContext";
// import { Badge } from "@/components/ui/badge";
import CustomDialogWrapper from "./CustomDialogWrapper";
import { useEntityParams } from "@/hooks/useEntityParams";
import { shouldShowParameterErrors } from "@/lib/utils/dynamic-page-utils";
import { ParameterErrorDisplay } from "./ParameterErrorDisplay";

interface DynamicPageProps {
  config: PageConfig;
  entityId?: string;
  dynamicPath?: string;
  contextData?: Record<string, any>;
}

export const DynamicPage = React.memo(function DynamicPage({
  config,
  entityId,
  dynamicPath = "",
  contextData = {},
}: DynamicPageProps) {
  // const { hasPermission } = useAuth();
  const navigate = useNavigate();
  const [showForm, setShowForm] = useState(
    !!entityId || config.type === "form"
  );
  const [refetch, setRefetch] = useState(false);
  const [currentEntityId, setCurrentEntityId] = useState<string | null>(
    entityId ?? null
  );

  // Extract URL parameters if this is a list page with parameter config
  const paramResult = useEntityParams(
    config.type === "list" ? (config as ListPageConfig).parameterConfig : undefined
  );

  // Apply context transformation if provided
  let transformedContextData = paramResult.contextData;
  if (config.type === "list" && (config as ListPageConfig).contextTransform && Object.keys(paramResult.params).length > 0) {
    try {
      const transformedData = (config as ListPageConfig).contextTransform!(
        paramResult.params as Record<string, string>
      );
      transformedContextData = { ...transformedContextData, ...transformedData };
    } catch (error) {
      console.warn("Error transforming context data:", error);
    }
  }

  // Merge provided context data with extracted and transformed parameters
  const mergedContextData = {
    ...transformedContextData,
    ...contextData,
  };

  // Use dynamic path from parameters if not explicitly provided
  const effectiveDynamicPath = dynamicPath || paramResult.dynamicPath;


  // Check for parameter validation errors
  const showParameterErrors = shouldShowParameterErrors(
    paramResult,
    config as ListPageConfig
  );


  // Show parameter validation errors if any
  if (showParameterErrors) {
    return (
      <ParameterErrorDisplay
        errors={paramResult.errors}
        onRetry={() => window.location.reload()}
      />
    );
  }

  if (config.type === "form") {
    return (
      <DynamicFormPage
        config={config as FormPageConfig}
        entityId={currentEntityId ?? undefined}
      />
    );
  }

  return (
    <>
      <EnhancedDynamicListPage
        config={config as ListPageConfig}
        refetch={refetch}
        dynamicPath={effectiveDynamicPath}
        contextData={mergedContextData}
        onRefetchComplete={() => setRefetch(false)}
        onCreateNew={() => {
          if (config.entityName === "Form") {
            navigate("/forms/new");
            return;
          }
          setCurrentEntityId(null);
          setShowForm(true);
        }}
        onView={(id) => {
          navigate(`/${config.id}/${config.entityName.toLowerCase()}/${id}`);
        }}
        onEdit={(id) => {
          if (config.entityName === "Form") {
            navigate(`/${config.id}/${config.entityName.toLowerCase()}/${id}`);

            return;
          }
          setCurrentEntityId(id);
          setShowForm(true);
        }}
      />

      {!currentEntityId && (
        (config as ListPageConfig).createFormConfig ||
        (config as ListPageConfig).customDialogs?.create
      ) && (
          <CustomDialogWrapper
            open={showForm}
            onOpenChange={setShowForm}
            mode="create"
            customDialogs={(config as ListPageConfig).customDialogs}
            fallbackFormConfig={(config as ListPageConfig).createFormConfig}
            contextData={mergedContextData}
            onSuccess={() => {
              setShowForm(false);
              setRefetch(true);
            }}
            onCancel={() => setShowForm(false)}
            entityName={config.entityName}
          />
        )}

      {/* Edit Entity Dialog */}
      {currentEntityId && (
        <CustomDialogWrapper
          open={showForm}
          onOpenChange={setShowForm}
          mode="edit"
          customDialogs={(config as ListPageConfig).customDialogs}
          fallbackFormConfig={(config as ListPageConfig).createFormConfig}
          entityId={currentEntityId}
          contextData={mergedContextData}
          onSuccess={() => {
            setShowForm(false);
            setRefetch(true);
          }}
          onCancel={() => setShowForm(false)}
          entityName={config.entityName}
        />
      )}
    </>
  );
});

export default DynamicPage;
