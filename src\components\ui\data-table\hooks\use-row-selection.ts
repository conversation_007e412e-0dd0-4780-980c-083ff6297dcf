import { useState, useCallback } from "react";
import { RowSelectionState } from "@tanstack/react-table";

interface UseRowSelectionProps {
  initialSelection?: RowSelectionState;
  onSelectionChange?: (selection: RowSelectionState) => void;
}

/**
 * Hook for managing row selection state in DataTable
 */
export function useRowSelection({
  initialSelection = {},
  onSelectionChange,
}: UseRowSelectionProps = {}) {
  const [rowSelection, setRowSelection] = useState<RowSelectionState>(initialSelection);

  const handleRowSelectionChange = useCallback(
    (updaterOrValue: RowSelectionState | ((old: RowSelectionState) => RowSelectionState)) => {
      const newSelection = typeof updaterOrValue === "function" 
        ? updaterOrValue(rowSelection) 
        : updaterOrValue;
      
      setRowSelection(newSelection);
      onSelectionChange?.(newSelection);
    },
    [rowSelection, onSelectionChange]
  );

  const getSelectedRowIds = useCallback(() => {
    return Object.keys(rowSelection).filter(id => rowSelection[id]);
  }, [rowSelection]);

  const getSelectedRowCount = useCallback(() => {
    return getSelectedRowIds().length;
  }, [getSelectedRowIds]);

  const clearSelection = useCallback(() => {
    const emptySelection = {};
    setRowSelection(emptySelection);
    onSelectionChange?.(emptySelection);
  }, [onSelectionChange]);

  const selectAll = useCallback((rowIds: string[]) => {
    const allSelected = rowIds.reduce((acc, id) => {
      acc[id] = true;
      return acc;
    }, {} as RowSelectionState);
    
    setRowSelection(allSelected);
    onSelectionChange?.(allSelected);
  }, [onSelectionChange]);

  const isRowSelected = useCallback((rowId: string) => {
    return !!rowSelection[rowId];
  }, [rowSelection]);

  return {
    rowSelection,
    onRowSelectionChange: handleRowSelectionChange,
    getSelectedRowIds,
    getSelectedRowCount,
    clearSelection,
    selectAll,
    isRowSelected,
  };
}
