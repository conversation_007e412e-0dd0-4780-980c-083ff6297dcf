import { FormSchema, FormComponent } from "@/lib/schemas/form-schemas";

/**
 * Utility functions for organizing form structure
 */

export interface FormStep {
  id: string;
  label: string;
  description?: string;
  icon?: string;
  components: FormComponent[];
}

/**
 * Organizes form components into steps
 * If no steps are defined, creates a default step with all top-level components
 */
export function organizeComponentsIntoSteps(schema: FormSchema): FormStep[] {
  // Find all step components (top-level only)
  const stepComponents = schema.components.filter(
    (component) => component.type === "step" && !component.parentId
  ) as any[];

  // If no steps are defined, create a default step with all top-level components
  if (stepComponents.length === 0) {
    // Get all top-level components (those without a parentId)
    const topLevelComponents = schema.components.filter(
      (component) => !component.parentId
    );

    return [
      {
        id: "default-step",
        label: "Form",
        description: "Default form",
        components: topLevelComponents,
      },
    ];
  }

  // Map each step to its components
  return stepComponents.map((step) => {
    // Get direct children of this step
    const directChildren = schema.components.filter(
      (component) => component.parentId === step.id
    );

    return {
      id: step.id,
      label: step.label,
      description: step.description,
      icon: step.icon,
      components: directChildren,
    };
  });
}
