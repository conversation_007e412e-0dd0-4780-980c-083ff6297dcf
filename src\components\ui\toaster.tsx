import * as React from "react";
import {
  Toast,
  ToastClose,
  ToastDescription,
  ToastProvider,
  ToastTitle,
  ToastViewport,
} from "@/components/ui/toast";
// No need to import useToast here

// Create a context for toast state
export const ToastContext = React.createContext<{
  toasts: ToasterToast[];
  setToasts: React.Dispatch<React.SetStateAction<ToasterToast[]>>;
}>({
  toasts: [],
  setToasts: () => {},
});

// Define the toast type
export type ToasterToast = {
  id: string;
  title?: React.ReactNode;
  description?: React.ReactNode;
  action?: React.ReactNode;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  variant?: "default" | "destructive";
};

export function Toaster() {
  const [toasts, setToasts] = React.useState<ToasterToast[]>([]);

  return (
    <ToastContext.Provider value={{ toasts, setToasts }}>
      <ToastProvider>
        {toasts.map(function ({ id, title, description, action, ...props }) {
          return (
            <Toast key={id} {...props}>
              <div className="grid gap-1">
                {title && <ToastTitle>{title}</ToastTitle>}
                {description && (
                  <ToastDescription>{description}</ToastDescription>
                )}
              </div>
              {action}
              <ToastClose />
            </Toast>
          );
        })}
        <ToastViewport />
      </ToastProvider>
    </ToastContext.Provider>
  );
}
