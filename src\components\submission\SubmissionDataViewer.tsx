import { useMemo } from "react";
import { FormSubmission } from "@/lib/types/submission";
import { organizeComponentsIntoSteps } from "@/lib/utils/form-structure-utils";
import { SubmissionStep } from "./SubmissionStep";

interface SubmissionDataViewerProps {
  submission: FormSubmission;
}

/**
 * Renders submission data in a structured, readable format, respecting form structure.
 */
export function SubmissionDataViewer({
  submission,
}: Readonly<SubmissionDataViewerProps>) {
  // Get the form schema and submission data
  const { formSchema, data } = submission;

  // Organize components into steps to match the form's structure
  const steps = useMemo(
    () => organizeComponentsIntoSteps(formSchema),
    [formSchema]
  );

  return (
    <div className="space-y-8">
      {steps.map((step) => (
        <SubmissionStep
          key={step.id}
          title={step.label}
          description={step.description}
          components={step.components}
          allComponents={formSchema.components}
          data={data}
        />
      ))}
    </div>
  );
}
