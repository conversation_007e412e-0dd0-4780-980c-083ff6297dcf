import { useState, useEffect, useRef } from "react";
import { ConditionalRendering, FormComponent } from "@/lib/types/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { AlertCircle } from "lucide-react";
import { Switch } from "@/components/ui/switch";

interface ConditionalRenderingEditorProps {
  component: FormComponent;
  allComponents: FormComponent[];
  onChange: (conditionalRendering: ConditionalRendering | undefined) => void;
}

export default function ConditionalRenderingEditor({
  component,
  allComponents,
  onChange,
}: Readonly<ConditionalRenderingEditorProps>) {
  const [enabled, setEnabled] = useState<boolean>(
    !!component.conditionalRendering
  );
  const [condition, setCondition] = useState<ConditionalRendering>(
    component.conditionalRendering || {
      field: "",
      operator: "equals",
      value: "",
    }
  );

  // Filter out the current component from the list of available fields
  const availableFields = allComponents.filter((c) => c.id !== component.id);

  // Use a ref to track previous values and avoid unnecessary updates
  const prevConditionRef = useRef<ConditionalRendering | null>(null);
  const prevEnabledRef = useRef<boolean>(enabled);

  // Update the parent component when the condition changes
  useEffect(() => {
    // Only call onChange if the values have actually changed
    const conditionChanged =
      JSON.stringify(prevConditionRef.current) !== JSON.stringify(condition);
    const enabledChanged = prevEnabledRef.current !== enabled;

    if (conditionChanged || enabledChanged) {
      // Update refs with current values
      prevConditionRef.current = { ...condition };
      prevEnabledRef.current = enabled;

      // Call onChange with the appropriate value
      if (enabled) {
        onChange(condition);
      } else {
        onChange(undefined);
      }
    }
  }, [enabled, condition, onChange]);

  // Handle changes to the condition
  const handleConditionChange = <K extends keyof ConditionalRendering>(
    field: K,
    value: ConditionalRendering[K]
  ) => {
    setCondition((prev) => {
      // If changing to empty/notEmpty operator, clear the value field as it's not needed
      if (field === "operator" && (value === "empty" || value === "notEmpty")) {
        return {
          ...prev,
          [field]: value,
          value: "",
        };
      }

      return {
        ...prev,
        [field]: value,
      };
    });
  };

  // Handle toggling the condition on/off
  const handleToggleEnabled = (checked: boolean) => {
    setEnabled(checked);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium">Conditional Rendering</h4>
        <div className="flex items-center space-x-2">
          <Switch
            id="conditional-enabled"
            checked={enabled}
            onCheckedChange={handleToggleEnabled}
          />
          <Label htmlFor="conditional-enabled">
            {enabled ? "Enabled" : "Disabled"}
          </Label>
        </div>
      </div>

      {enabled ? (
        <Card>
          <CardContent className="pt-6 space-y-4">
            <div className="grid gap-2">
              <Label htmlFor="conditional-field">Depends on Field</Label>
              <Select
                value={condition.field}
                onValueChange={(value) => handleConditionChange("field", value)}
                disabled={availableFields.length === 0}
              >
                <SelectTrigger id="conditional-field">
                  <SelectValue placeholder="Select a field" />
                </SelectTrigger>
                <SelectContent>
                  {availableFields.map((field) => (
                    <SelectItem key={field.id} value={field.name}>
                      {field.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {availableFields.length === 0 && (
                <p className="text-xs text-muted-foreground">
                  No other fields available to create a condition
                </p>
              )}
            </div>

            <div className="grid gap-2">
              <Label htmlFor="conditional-operator">Operator</Label>
              <Select
                value={condition.operator}
                onValueChange={(value) =>
                  handleConditionChange(
                    "operator",
                    value as ConditionalRendering["operator"]
                  )
                }
              >
                <SelectTrigger id="conditional-operator">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="equals">Equals</SelectItem>
                  <SelectItem value="notEquals">Not Equals</SelectItem>
                  <SelectItem value="contains">Contains</SelectItem>
                  <SelectItem value="greaterThan">Greater Than</SelectItem>
                  <SelectItem value="lessThan">Less Than</SelectItem>
                  <SelectItem value="empty">Empty</SelectItem>
                  <SelectItem value="notEmpty">Not Empty</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {condition.operator !== "empty" &&
              condition.operator !== "notEmpty" && (
                <div className="grid gap-2">
                  <Label htmlFor="conditional-value">Value</Label>
                  <Input
                    id="conditional-value"
                    value={condition.value}
                    onChange={(e) =>
                      handleConditionChange("value", e.target.value)
                    }
                    placeholder="Enter value"
                  />
                </div>
              )}

            {!condition.field && enabled && (
              <div className="text-xs text-amber-500 flex items-center gap-1">
                <AlertCircle className="h-3 w-3" />
                <span>Select a field to create a condition</span>
              </div>
            )}
          </CardContent>
        </Card>
      ) : (
        <p className="text-sm text-muted-foreground">
          This component will always be visible. Enable conditional rendering to
          show/hide based on other field values.
        </p>
      )}
    </div>
  );
}
