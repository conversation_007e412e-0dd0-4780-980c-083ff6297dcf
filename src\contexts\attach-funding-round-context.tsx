import { createContext, useContext, useState, ReactNode } from "react";

type ProjectData = {
  id: string;
  name: string;
  [key: string]: any;
};

interface AttachFundingRoundContextType {
  isOpen: boolean;
  projectData: ProjectData | null;
  openDialog: (project: ProjectData) => void;
  closeDialog: () => void;
}

const AttachFundingRoundContext = createContext<AttachFundingRoundContextType | undefined>(
  undefined
);

interface AttachFundingRoundProviderProps {
  children: ReactNode;
}

export function AttachFundingRoundProvider({ children }: AttachFundingRoundProviderProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [projectData, setProjectData] = useState<ProjectData | null>(null);

  const openDialog = (project: ProjectData) => {
    setProjectData(project);
    setIsOpen(true);
  };

  const closeDialog = () => {
    setIsOpen(false);
    setProjectData(null);
  };

  const value = {
    isOpen,
    projectData,
    openDialog,
    closeDialog,
  };

  return (
    <AttachFundingRoundContext.Provider value={value}>
      {children}
    </AttachFundingRoundContext.Provider>
  );
}

export function useAttachFundingRoundContext() {
  const context = useContext(AttachFundingRoundContext);
  if (context === undefined) {
    throw new Error(
      "useAttachFundingRoundContext must be used within an AttachFundingRoundProvider"
    );
  }
  return context;
}
