
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import {
  CustomDialogProps,
  CustomDialogsConfig,
  FormPageConfig
} from "@/lib/types/page-config";
import DynamicFormPage from "./DynamicFormPage";

interface CustomDialogWrapperProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  mode: "create" | "edit";
  customDialogs?: CustomDialogsConfig;
  fallbackFormConfig?: FormPageConfig;
  entityId?: string;
  initialData?: Record<string, any>;
  contextData?: Record<string, any>;
  onSuccess?: (data?: any) => void;
  onCancel?: () => void;
  entityName: string;
}

/**
 * Wrapper component that renders either custom dialog components or fallback to standard forms
 */
export function CustomDialogWrapper({
  open,
  onOpenChange,
  mode,
  customDialogs,
  fallbackFormConfig,
  entityId,
  initialData,
  contextData,
  onSuccess,
  onCancel,
  entityName,
}: Readonly<CustomDialogWrapperProps>) {
  // Determine which dialog configuration to use
  const dialogConfig = mode === "create"
    ? customDialogs?.create
    : customDialogs?.edit;

  // Handle dialog close
  const handleClose = () => {
    onOpenChange(false);
    onCancel?.();
  };

  // Handle success callback
  const handleSuccess = (data?: any) => {
    onOpenChange(false);
    onSuccess?.(data);
  };

  // Prepare props for custom components
  const customDialogProps: CustomDialogProps = {
    onSuccess: handleSuccess,
    onCancel: handleClose,
    contextData,
    entityId,
    initialData: {
      ...initialData,
      ...contextData, // Merge context data as initial values
    },
  };

  // If custom dialog is configured, render it
  if (dialogConfig) {
    const CustomComponent = dialogConfig.component;
    const additionalProps = dialogConfig.props || {};

    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="xl:max-w-6xl max-h-[90vh] overflow-y-auto sm:max-w-5xl">
          <DialogTitle>
            {mode === "create" ? `Create New ${entityName}` : `Edit ${entityName}`}
          </DialogTitle>
          <CustomComponent
            {...customDialogProps}
            {...additionalProps}
          />
        </DialogContent>
      </Dialog>
    );
  }

  // Fallback to standard form if no custom dialog is configured
  if (fallbackFormConfig) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="xl:max-w-6xl max-h-[90vh] overflow-y-auto sm:max-w-5xl">
          <DialogTitle>
            {mode === "create" ? `Create New ${entityName}` : `Edit ${entityName}`}
          </DialogTitle>
          <DynamicFormPage
            config={fallbackFormConfig}
            entityId={entityId}
            initialData={customDialogProps.initialData}
            onSuccess={handleSuccess}
            onCancel={handleClose}
          />
        </DialogContent>
      </Dialog>
    );
  }

  // No dialog configuration available
  return null;
}

/**
 * Hook to determine dialog configuration based on mode and available configs
 */
export function useDialogConfig(
  mode: "create" | "edit",
  customDialogs?: CustomDialogsConfig,
  fallbackFormConfig?: FormPageConfig
) {
  const hasCustomDialog = mode === "create"
    ? !!customDialogs?.create
    : !!customDialogs?.edit;

  const canRenderDialog = hasCustomDialog || !!fallbackFormConfig;

  return {
    hasCustomDialog,
    canRenderDialog,
    dialogConfig: mode === "create" ? customDialogs?.create : customDialogs?.edit,
  };
}

/**
 * Utility component for rendering custom dialog content without the Dialog wrapper
 * Useful for embedding custom components in other contexts
 */
export function CustomDialogContent({
  mode,
  customDialogs,
  fallbackFormConfig,
  entityId,
  initialData,
  contextData,
  onSuccess,
  onCancel,
}: Omit<CustomDialogWrapperProps, "open" | "onOpenChange" | "entityName">) {
  const dialogConfig = mode === "create"
    ? customDialogs?.create
    : customDialogs?.edit;

  const customDialogProps: CustomDialogProps = {
    onSuccess,
    onCancel,
    contextData,
    entityId,
    initialData: {
      ...initialData,
      ...contextData,
    },
  };

  // Render custom component if available
  if (dialogConfig) {
    const CustomComponent = dialogConfig.component;
    const additionalProps = dialogConfig.props || {};

    return (
      <CustomComponent
        {...customDialogProps}
        {...additionalProps}
      />
    );
  }

  // Fallback to standard form
  if (fallbackFormConfig) {
    return (
      <DynamicFormPage
        config={fallbackFormConfig}
        entityId={entityId}
        initialData={customDialogProps.initialData}
        onSuccess={onSuccess}
        onCancel={onCancel}
      />
    );
  }

  // No configuration available
  return (
    <div className="p-4 text-center text-muted-foreground">
      No dialog configuration available for {mode} operation.
    </div>
  );
}

export default CustomDialogWrapper;
